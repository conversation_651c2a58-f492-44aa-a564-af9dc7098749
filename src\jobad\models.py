import logging

from django.db import models
from company.models import Company, CompanyPosition, CompanyLocation
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from django_countries.fields import CountryField

from common.models import Status, Document
from common.validators import validate_birthday, EUROPEAN_COUNTRIES
from company.models import StructureDependency, RejectionEndReason
from company.models import Company, StructureDependency, RejectionEndReason
from userprofiles.models import UserProfile
from common.validators import validate_birthday
from .exceptions import JobAdApplicationError, CandidateValidationError

logger = logging.getLogger(__name__)


class JobadStage(models.TextChoices):
    REQUESTED = 'requested'
    REJECTED = 'rejected'
    RETRY = 'retry'
    APPROVED = 'approved'
    OPEN_FOR_EVERYONE = 'open_for_everyone'
    FILLED = 'filled'
    COMPLETED = 'completed'
    WITHDRAWN = 'withdrawn'
    OPEN_AGAIN = 'open_again'


class JobadStatus(models.TextChoices):
    CREATED = 'created'
    CANCELED = 'canceled'
    CLOSED = 'closed'
    ARCHIVED = 'archived'


class JobadGroup(models.TextChoices):
    OFFICE = 'office'
    PRODUCTION = 'production'


class JobadApplicationStage(models.TextChoices):
    REQUESTED = 'requested'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    INTERVIEW = 'interview'
    INTERVIEW_COMPLETED = 'interview_completed'
    INTERVIEW_REJECTED = 'interview_rejected'
    CANDIDATE_SELECTION = 'candidate_selection'
    TENTATIVELY_OCCUPIED = 'tentatively_occupied'
    CANDIDATE_SELECTION_REJECTED = 'candidate_selection_rejected'
    COMPLETED = 'completed'
    CANCELED = 'canceled'
    SELECTION_REJECTED = 'selection_rejected'


class InterviewStage(models.TextChoices):
    REQUESTED = 'requested'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    REQUESTED_AGAIN = 'requested_again'


class SelectionStage(models.TextChoices):
    REQUESTED = 'requested'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    COMPLETED = 'completed'
    CANCELED = 'canceled'


class Salutation(models.TextChoices):
    MR = 'mr', _('Mr')
    MRS = 'mrs', _('Mrs')
    DIVERS = 'divers', _('Divers')


class WorkerStage(models.TextChoices):
    DEPLOYMENT_FINISHED = 'deployment_finished', _('Deployment finished')
    APPROVED = 'approved', _('In Deployment')
    EXTENSION_REQUESTED = 'extension_requested', _('Extension Requested')
    EXTENDED = 'extended', _('Extended')
    TRANSFER_REQUESTED = 'transfer_requested', _('Transfer requested')
    TRANSFERRED = 'transferred', _('Transferred')
    PAUSED = 'paused', _('Paused')
    RESUMED = 'resumed', _('Resumed')
    DEPLOYMENT_DISCONTINUED = 'deployment_discontinued', _('Deployment discontinued')
    DEPLOYMENT_DISCONTINUED_AS_SUPPLIER = 'deployment_discontinued_as_supplier', _('Deployment discontinued as supplier')
    INTERNAL_DEPLOYMENT = 'internal_deployment', _('Internal Deployment')
    INTERNALLY_DEPLOYED = 'internally_deployed', _('Internally Deployed')
    INTERNAL_DEPLOYMENT_ACCEPTED = 'internal_deployment_accepted', _('Internal Deployment Accepted')


class EventStatus(models.TextChoices):
    ACTIVE = 'active'
    CANCELED = 'canceled'
    TRIGGERED = 'triggered'
    REQUESTED = 'requested'


class WorkerEventType(models.TextChoices):
    PAUSE = 'pause'
    UNPAUSE = 'unpause'
    DISCONTINUE = 'discontinue'
    TERMINATION = 'termination'
    INTERNAL_DEPLOYMENT = 'internal_deployment'


class Jobad(models.Model):
    given_id = models.CharField(max_length=255, unique=True, blank=True, null=True)
    position = models.ForeignKey(CompanyPosition, on_delete=models.PROTECT)
    location = models.ForeignKey(CompanyLocation, on_delete=models.PROTECT)
    time_period = models.IntegerField()
    publisher = models.CharField(max_length=255)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    total = models.BigIntegerField()
    occupied = models.IntegerField(default=0)
    finished_deployments = models.IntegerField(default=0)
    stage = models.CharField(
        max_length=50,
        choices=JobadStage.choices,
        default=JobadStage.REQUESTED
    )
    status = models.CharField(
        max_length=50,
        choices=JobadStatus.choices,
        default=JobadStatus.CREATED
    )
    created_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='created_by_jobads', null=True, blank=True)
    modified_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='modified_by_jobads', null=True, blank=True)
    approved_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='approved_by_jobads', null=True, blank=True)
    created_date = models.DateTimeField(default=timezone.now)
    modified_date = models.DateTimeField(blank=True, null=True)
    approved_date = models.DateTimeField(blank=True, null=True)
    employee_group = models.CharField(
        max_length=50,
        choices=JobadGroup.choices,
        default=JobadGroup.OFFICE
    )
    description = models.TextField(blank=True, null=True)
    cost_department = models.CharField(max_length=50, blank=True, null=True)
    weekly_working_hours = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    anonymous = models.BooleanField(default=False)
    department_value = models.CharField(max_length=50, blank=True, null=True)
    company_value = models.CharField(max_length=50, blank=True, null=True)
    documents = models.JSONField(blank=True, null=True)
    document_with_idx = models.JSONField(blank=True, null=True)
    new_delay_duration = models.CharField(max_length=10, blank=True, null=True)
    tasks = models.TextField(blank=True, null=True)
    requirements = models.TextField(blank=True, null=True)
    driverlicense = models.JSONField(blank=True, null=True)
    starting_shift_time = models.CharField(max_length=50, blank=True, null=True)
    ending_shift_time = models.CharField(max_length=50, blank=True, null=True)
    contact = models.JSONField(blank=True, null=True)
    g25_selected = models.BooleanField(default=False)
    fuhrerschein_selected = models.BooleanField(default=False)
    working_hours = models.JSONField(blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)

    def __str__(self):
        return f'[{self.id}] {self.position.title}'


class JobadApplication(models.Model):
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)
    candidate = models.ForeignKey('Candidate', on_delete=models.CASCADE)
    stage = models.CharField(
        max_length=50,
        choices=JobadApplicationStage.choices,
        default=JobadApplicationStage.REQUESTED
    )
    status = models.CharField(
        max_length=50,
        choices=JobadStatus.choices,
        default=JobadStatus.CREATED
    )
    interview_skipped = models.BooleanField(default=False)
    last_suggestion_by = models.CharField(
        max_length=10,
        choices=[('supplier', 'Supplier'), ('customer', 'Customer')],
        null=True,
        blank=True
    )
    comment = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, related_name='created_by_jobadapplication', null=True, blank=True)
    modified_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, related_name='modified_by_jobadapplication',
                                    null=True, blank=True)
    modified_date = models.DateTimeField(default=timezone.now)
    created_date = models.DateTimeField(default=timezone.now)
    anonymous = models.BooleanField(default=False)

    # Create Errormessage if candidate and jobad are incompatible, but let save still go through
    # def clean(self):
    #     if self.jobad.g25_selected and not self.candidate.g25:
    #         raise JobAdApplicationError(_('Candidate must have G25'))

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        try:
            self.clean()
        except JobAdApplicationError as e:
            pass

    def __str__(self):
        return f'{self.jobad} - {self.candidate}'

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['jobad', 'candidate'], name='jobad_applications_jobad_candidate_key'),
        ]


class JobadAdditionalField(models.Model):
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)
    field_id = models.IntegerField()
    label = models.CharField(max_length=255)
    value = models.CharField(max_length=500)
    is_required = models.BooleanField()
    max_input = models.IntegerField(blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['jobad', 'field_id'], name='jobad_additional_fields_jobad_field_id_key'),
        ]


class JobadDepartment(StructureDependency):
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)

class JobaDocument(Document):
    jobad = models.ForeignKey('Jobad', on_delete=models.CASCADE)
    document = models.FileField(upload_to='jobad/', max_length=455)
    
    class Meta:
        db_table = 'jobad_jobadocument'

class JobadFavorite(models.Model):
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)
    user_ref = models.ForeignKey(UserProfile, on_delete=models.CASCADE)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['jobad', 'user_ref'], name='jobad_favorites_jobad_user_ref_key')
        ]

    def __str__(self):
        return f'{self.jobad} - {self.user_ref}'


class JobadManualStage(models.Model):
    jobad = models.OneToOneField(Jobad, on_delete=models.CASCADE)
    manual_stage_change = models.CharField(max_length=50)


class JobadTemplate(models.Model):
    user_ref = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    position = models.CharField(max_length=50)
    location = models.CharField(max_length=50)
    time_period = models.IntegerField()
    total = models.IntegerField()
    description = models.TextField(blank=True, null=True)
    tasks = models.TextField(blank=True, null=True)
    requirements = models.TextField(blank=True, null=True)
    contact = models.JSONField(blank=True, null=True)
    working_hours = models.JSONField(blank=True, null=True)
    weekly_working_hours = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    cost_department = models.CharField(max_length=50, blank=True, null=True)
    employee_group = models.CharField(
        max_length=50,
        choices=JobadGroup.choices,
        default=JobadGroup.OFFICE
    )


class Candidate(models.Model):
    firstname = models.CharField(_("First Name"), max_length=255)
    lastname = models.CharField(_("Last Name"), max_length=255)
    date_of_birth = models.DateField(_("Date of Birth"), validators=[validate_birthday])
    company = models.ForeignKey(Company, verbose_name=_("Company"), on_delete=models.CASCADE)
    application_date = models.DateTimeField(blank=True, null=True)
    created_date = models.DateTimeField(default=timezone.now)
    modified_date = models.DateTimeField(blank=True, null=True)
    country = CountryField(_("Origin"), blank=True, null=True)
    title = models.CharField(
        _("Title"),
        max_length=50,
        choices=Salutation.choices,
        default=Salutation.DIVERS
    )
    status = models.CharField(
        max_length=50,
        choices=Status.choices,
        default=Status.CREATED
    )
    comment = models.CharField(_("Comment"), max_length=255, blank=True, null=True)
    hourly_rate = models.DecimalField(_("Hourly Rate"), max_digits=5, decimal_places=2, blank=True, null=True)
    deleted_at = models.DateTimeField(blank=True, null=True)
    anonymous = models.BooleanField(default=False)
    document_with_idx = models.JSONField(blank=True, null=True)
    origin = CountryField(null=True, blank=True)
    g25 = models.JSONField(blank=True, null=True)
    profile_picture = models.ImageField(upload_to='candidate_profile_pictures/', null=True, blank=True)

    def clean(self):
        if self.origin and self.origin.code not in EUROPEAN_COUNTRIES:
            from .models import CandidateDocument, DocumentType  # evitar circular imports si hace falta
            has_permit = CandidateDocument.objects.filter(
                candidate=self,
                document_type=DocumentType.WORK_PERMIT
            ).exists()

            if not has_permit:
                raise CandidateValidationError(_('Candidates from outside Europe must have a work permit!'))
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f'{self.firstname} {self.lastname}'


class Worker(models.Model):
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)
    candidate = models.ForeignKey(Candidate, on_delete=models.CASCADE)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    old_end_date = models.DateTimeField(blank=True, null=True)
    suggestion_date = models.DateTimeField(blank=True, null=True)
    transfer_jobad_ref = models.CharField(max_length=255, blank=True, null=True)
    category = models.CharField(max_length=255, blank=True, null=True)
    hud_date = models.DateTimeField(blank=True, null=True)
    eqp_date = models.DateTimeField(blank=True, null=True)
    exceeds_hud = models.BooleanField(default=False)
    exceeds_eqp = models.BooleanField(default=False)
    will_exceed_hud = models.BooleanField(default=False)
    will_exceed_eqp = models.BooleanField(default=False)
    stage = models.CharField(
        max_length=50,
        choices=WorkerStage.choices,
        default=WorkerStage.APPROVED
    )
    status = models.CharField(
        max_length=50,
        choices=Status.choices,
        default=Status.CREATED
    )
    created_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='worker_created_by', blank=True, null=True)
    modified_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, related_name='worker_modified_by',
                                    null=True, blank=True)
    modified_date = models.DateTimeField(default=timezone.now)
    anonymous = models.BooleanField(default=False)
    date_of_category_change = models.DateTimeField(default=timezone.now)
    evaluation_time = models.IntegerField(default=0)

    def __str__(self):
        return f'{self.candidate} - {self.jobad}'

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['jobad', 'candidate'], name='workers_jobad_candidate_key')
        ]

class DocumentType(models.TextChoices):
    CV = 'CV', 'CV'
    WORK_PERMIT = 'work_permit', 'Work Permit'
    FUHRERSCHEIN = 'fuhrerschein', 'Fuhrerschein'
    STAPLERSCHEIN = 'staplerschein', 'Staplerschein'

class CandidateDocument(Document):
    candidate = models.ForeignKey('Candidate', on_delete=models.CASCADE)
    document = models.FileField(upload_to='candidate/', max_length=455)
    document_type = models.CharField(
        max_length=20,
        choices=DocumentType.choices,
        default=DocumentType.CV
    )
    uploaded_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='created_by_candidatedocument', null=True, blank=True)
    uploaded_on = models.DateTimeField(auto_now_add=True)    
    expiration_date = models.DateField(blank=True, null=True)
    
    def __str__(self):
        return f'{self.document_type} for {self.candidate}'

class WorkerEvent(models.Model):
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE)
    type = models.CharField(
        max_length=50,
        choices=WorkerEventType.choices
    )
    status = models.CharField(
        max_length=50,
        choices=EventStatus.choices,
        default=EventStatus.ACTIVE
    )
    timestamp = models.DateTimeField()
    performed_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE)
    content = models.TextField(blank=True, null=True)


class Interview(models.Model):
    application = models.OneToOneField(JobadApplication, on_delete=models.CASCADE)
    interview_dates = models.JSONField()
    interview_start_times = models.JSONField()
    interview_end_times = models.JSONField()
    contact_person_name = models.CharField(max_length=255, null=True, blank=True)
    contact_person_phone = models.CharField(max_length=20, null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    stage = models.CharField(
        max_length=50,
        choices=InterviewStage.choices,
        default=InterviewStage.REQUESTED
    )
    status = models.CharField(
        max_length=50,
        choices=Status.choices,
        default=Status.CREATED
    )
    created_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='created_by_interview', blank=True, null=True)
    modified_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='modified_by_interview', blank=True, null=True)
    anonymous = models.BooleanField(default=False)


class Selection(models.Model):
    application = models.OneToOneField(JobadApplication, on_delete=models.CASCADE)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    start_time = models.TimeField(blank=True, null=True)
    contact_person_name = models.CharField(max_length=255, null=True, blank=True)
    contact_person_phone = models.CharField(max_length=20, null=True, blank=True)
    exceeds_eqp = models.BooleanField(default=False)
    stage = models.CharField(
        max_length=50,
        choices=SelectionStage.choices,
        default=SelectionStage.REQUESTED
    )
    status = models.CharField(
        max_length=50,
        choices=Status.choices,
        default=Status.CREATED
    )
    created_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='created_by_selection', blank=True, null=True)
    modified_by = models.ForeignKey(UserProfile, on_delete=models.CASCADE, related_name='modified_by_selection', blank=True, null=True)
    anonymous = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.application} - {self.stage}'


class WorkerRejectionReason(models.Model):
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE)
    reason = models.ForeignKey(RejectionEndReason, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.worker} - {self.reason}"

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['worker', 'reason'], name='rejected_worker_reasons_worker_reason_key'),
        ]


class RejectedCandidateReason(models.Model):
    applicant = models.ForeignKey(Candidate, on_delete=models.CASCADE)
    reason = models.ForeignKey(RejectionEndReason, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.applicant} - {self.reason}"

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['applicant', 'reason'],
                                    name='rejected_candidate_reasons_applicant_reason_key'),
        ]


class Task(models.Model):
    title = models.CharField(max_length=255)
    jobad = models.ForeignKey(Jobad, on_delete=models.CASCADE)
    application = models.ForeignKey(JobadApplication, on_delete=models.CASCADE, null=True, blank=True)
    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    done = models.BooleanField(default=False)
    done_by = models.ForeignKey(UserProfile, on_delete=models.SET_NULL, null=True, blank=True)
    done_date = models.DateTimeField(null=True, blank=True)
    created_date = models.DateTimeField(default=timezone.now)
