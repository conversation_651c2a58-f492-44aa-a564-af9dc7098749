# company/context_processors.py


# Moved the function (part of it) from the company_detail function: this makes the company detail function easier
# to write, read and so on
# Further, this context can now be used in the complete config project, without needing to rewrite them again.
# (see settings/base.py templates)
# may not be that useful now, but maybe in the future (when we want to access company data for a common view or
# something)

from django.shortcuts import get_object_or_404

from company.models import Company, CompanyPartner, CompanyPosition, CompanyLocation, CompanyDocument, RejectionEndReason
from config import settings


def company_context(request):
    context = {}
    pk = request.GET.get('pk')
    if pk:
        company = get_object_or_404(Company, pk=pk)
        tab = request.GET.get('tab', 'details')
        context = {
            'company': company,
            'positions': CompanyPosition.objects.filter(company=company) if tab == 'locations' else [],
            'locations': CompanyLocation.objects.filter(company=company) if tab == 'locations' else [],
            'documents': CompanyDocument.objects.filter(company=company) if tab == 'documents' else [],
            'rejection_reasons': RejectionEndReason.objects.filter(company=company, setting__pk=1),
            'end_reasons': RejectionEndReason.objects.filter(company=company, setting__pk=2),
            'probetag': RejectionEndReason.objects.filter(company=company, setting__pk=3),
            'tab': tab,
        }
    return context

def documents_tab_visibility(request):
    """
    Adds `show_documents_tab` to the template context whenever:
    - the current user’s company is LGI (ID in settings)
      OR
    - the user’s company is a vendor or supplier linked to LGI via CompanyPartner.
    """
    user = request.user
    if not user.is_authenticated:
        return {}
    company = user.userprofile.company
    try:
        profile = user.userprofile
    except (AttributeError, user.userprofile.RelatedObjectDoesNotExist):
        return {}
    company = profile.company
    
    lgi_id = getattr(settings, 'LGI_COMPANY_ID', 61)

    # 1) LGI itself always sees the tab
    if company.id == lgi_id and user.userprofile.role in [1, 5]:
        return {'show_documents_tab': True}

    # 2) Vendors/suppliers linked to LGI see it too
    is_partner = CompanyPartner.objects.filter(
        customer_id=lgi_id,
        company=company
    ).exists()
    is_vendor_or_supplier = company.company_type in ('vendor', 'supplier')

    return {'show_documents_tab': is_partner and is_vendor_or_supplier}

def reports_tab_visibility(request):
    user = request.user
    if not user.is_authenticated:
        return {}
    company_id = user.userprofile.company_id
    return {'show_reports_tab': company_id in [1, 61]}