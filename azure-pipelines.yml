---
trigger:
  branches:
    include:
      - prd
  paths:
    exclude:
      - documentation/**/*

resources:
  repositories:
    - repository: templates
      type: git
      name: "Digital Projects/Templates"
      ref: refs/heads/main

stages:

  - stage: prepare
    displayName: "Prepare"
    jobs:
      - job: hashSum
        steps:
          - script: |
              commitHash=${sourceVersion:0:7}
              echo hash is $commitHash
              echo "##vso[task.setvariable variable=commitHash;isOutput=true]$commitHash"
            env: {sourceVersion: $(Build.SourceVersion)}
            name: hashSum
            displayName: "Get commit hashsum"

  - stage: docker
    displayName: "Build and Push Docker"
    dependsOn:
      - prepare
    variables:
      commitHash: $[ stageDependencies.prepare.hashSum.outputs['hashSum.commitHash'] ]
    jobs:
      - template: pipelines/docker/azure-pipelines.yml@templates
        parameters:
          imageName: "ppms"
          registry: "acreurdeprdppms"
          registryURL: "acreurdeprdppms.azurecr.io"
          azureSubscription: "AZU-VSSPN-EUR-DE-MANAGED-PRD-AssociateApp"
          dockerfilePath: "./Dockerfile"
          tag: $(commitHash)
          push: true

    # TODO: template
  - stage: deploy
    displayName: "Deploy PPMS"
    dependsOn:
      - docker
      - prepare # required for commitHash variable
    variables:
      commitHash: $[ stageDependencies.prepare.hashSum.outputs['hashSum.commitHash'] ]
    jobs:
      - job: Deploy
        displayName: "Run az containerupdate"
        steps:
          - task: AzureCLI@2
            displayName: "Update PPMS container app"
            inputs:
              azureSubscription: "AZU-VSSPN-EUR-DE-MANAGED-PRD-AssociateApp"
              scriptType: "bash"
              scriptLocation: "inlineScript"
              inlineScript: |
                az containerapp update \
                  --name ca-eur-de-prd-ppms \
                  --container-name ppms \
                  --resource-group RG-EUR-DE-PRD-DE_AssociateApp  \
                  --image acreurdeprdppms.azurecr.io/ppms:$(commitHash)
          - task: AzureCLI@2
            displayName: "Update PPMS celery container app"
            inputs:
              azureSubscription: "AZU-VSSPN-EUR-DE-MANAGED-PRD-AssociateApp"
              scriptType: "bash"
              scriptLocation: "inlineScript"
              inlineScript: |
                az containerapp update \
                  --name ca-eur-de-prd-ppms \
                  --container-name celery \
                  --resource-group RG-EUR-DE-PRD-DE_AssociateApp  \
                  --image acreurdeprdppms.azurecr.io/ppms:$(commitHash)
