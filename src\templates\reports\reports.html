{% extends "_base_frontend.html" %}
{% load application_filters %} 

{% block title %}All Reports{% endblock %}

{% block custom_header %}
  <style>
    .nav-item a {
      position: relative;
    }
    .nav-item a::after {
      content: '';
      position: absolute;
      left: 0; bottom: -5px;
      width: 0;
      height: 2px;
      background-color: #ffd200;
      transition: width 0.3s ease-in-out;
    }
    .nav-item a:hover::after {
      width: 100%;
    }
    [x-cloak] { display: none !important; }
    main {
      padding: 2rem !important;
    }
    .report-container {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      width: 100%;
    }
    @media (max-width: 1023px) {
      .report-container {
        flex-direction: column;
      }
    }
    :root {
      --y-offset: calc(64px + 64px + 2rem);
    }
    .table-header {
      display: flex;
      align-items: center;
      justify-content: start;
      margin-bottom: 1rem;
    }
    .table-header input {
      width: 100%;
      max-width: 300px;
      padding: 0.5rem 0.75rem 0.5rem 2.5rem;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
    }
    .table-header svg {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #9ca3af;
    }
    .chart-section,
    .table-section {
      flex: 1;
      max-height: calc(100vh - var(--y-offset));
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      padding: 0.75rem;
      background-color: white;
      display: flex;
      flex-direction: column;
    }
    .chart-scroll-wrapper {
      width: 100%;
      overflow-x: auto;
      overflow-y: hidden;
      white-space: nowrap;
    }
    .table-scroll-wrapper {
      flex: 1;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    .chart-scroll-wrapper::-webkit-scrollbar,
    .table-scroll-wrapper::-webkit-scrollbar {
      display: none;
    }
    .table-scroll-wrapper table {
      border-collapse: separate;
      width: 100%;
      border-spacing: 0 0.5rem;
    }
    .table-scroll-wrapper thead {
      background-color: #f9fafb;
    }
    .table-scroll-wrapper thead th {
      padding: 0.75rem 1rem;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      color: #6b7280;
    }
    .table-scroll-wrapper tbody tr {
      background-color: white;
      transition: background-color 0.2s;
    }
    .table-scroll-wrapper tbody tr:nth-child(odd) {
      background-color: #ffffff;
    }
    .table-scroll-wrapper tbody tr:hover {
      background-color: #f3f4f6;
    }
    .table-scroll-wrapper td {
      padding: 0.75rem 1rem;
      font-size: 0.75rem;
      color: #374151;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
{% endblock %}

{% block content %}
  <form id="filterForm" method="GET">
    <div class="report-container">
      <div class="chart-section w-full">
        <div class="w-full flex items-center justify-between mb-4">
          <div class="flex items-center gap-4">
            <h2 class="text-lg font-semibold mb-2">{{ total_count }} Offene Jobanzeigen</h2>
            <a href="{% url 'reports-excel' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
              class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-lg text-green-600 hover:bg-gray-50">
              <i class="fa-solid fa-file-excel mr-2"></i>Excel Export
            </a>
          </div>
          <div>
            <a href="{% url 'reports-supplier-evaluation' %}"
            class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 mr-4">
            <i class="fa-solid fa-building mr-2 text-gray-500"></i>
            Lieferantenbewertung
          </a>
          <li class="nav-item inline-block mb-4">
            <a href="{% url 'reports-reaction-time' %}" class="inline-flex items-center text-gray-700 hover:text-gray-900">
              <i class="fa-solid fa-arrow-right mr-2"></i> Reaktionszeiten
            </a>
          </li>
          </div>
        </div>
        <div class="chart-scroll-wrapper w-full">
          <canvas
            id="jobadsChart"
            style="
              display: block;
              min-width: {{ total_count|mul:40 }}px;
              height: 400px;
              box-sizing: border-box;
            "
          ></canvas>
        </div>
      </div>
      <div class="table-section w-full">
        <div class="table-header relative">
          <div
            x-data="multiLocationFilter({
              locations: [
                {id:'', name:'Any'},
                {% for loc in locations %}
                  {id:'{{loc.id}}', name:'{{loc}}'},
                {% endfor %}
              ],
              initialSelected: '{{ selected_locations }}'
            })"
            x-init="init()"
            class="relative inline-block mr-4"
          >
            <!-- Button to open dropdown -->
            <button type="button" @click="open = !open"
                    class="flex items-center px-4 py-2 border rounded-lg bg-white flex items-center gap-2 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fa-solid fa-location-dot text-accent-500"></i>
                <span class="font-medium truncate" style="max-width: 130px;" x-text="selectedLabel"></span>
                <i class="fa-solid fa-chevron-down text-gray-400"></i>
            </button>

            <!-- Dropdown list -->
            <div x-show="open" x-cloak @click.outside="open = false"
                class="absolute mt-1 bg-white border rounded shadow-lg z-20 w-64">
              <ul class="max-h-60 overflow-y-auto">
                <template x-for="loc in locations" :key="loc.id">
                  <li class="px-3 py-1 hover:bg-gray-100 cursor-pointer"
                      @click="toggleLocation(loc)">
                    <span x-text="loc.name"></span>
                    <i class="fa fa-check float-right" x-show="selectedIds.includes(loc.id)"></i>
                  </li>
                </template>
              </ul>
              <div class="p-2 border-t text-center">
                <button type="button" @click="applySelection()"
                        class="text-blue-600 font-semibold">
                  Apply
                </button>
              </div>
            </div>

            <!-- Hidden input that the form will submit -->
            <input type="hidden" name="location" :value="selectedIds.join(',')" />
          </div>
          <div x-data="multiLocationFilter({
                locations: [
                  {id:'', name:'Any'},
                  {% for pos in positions %}
                    {id:'{{ pos.id }}', name:'{{ pos.title }}'},
                  {% endfor %}
                ],
                initialSelected: '{{ selected_positions }}'
              })"
              x-init="init()"
              class="relative inline-block"
          >
            <!-- Button to open dropdown -->
            <button type="button" @click="open = !open"
                    class="flex items-center px-4 py-2 border rounded-lg bg-white gap-2 text-gray-700
                          hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    style="width: 130px;">
              <i class="fa-solid fa-briefcase text-accent-500"></i>
              <span class="font-medium truncate" style="max-width: 130px;" x-text="selectedLabel"></span>
              <i class="fa-solid fa-chevron-down text-gray-400"></i>
            </button>

            <!-- Dropdown list -->
            <div x-show="open" x-cloak @click.outside="open = false"
                class="absolute mt-2 bg-white border rounded shadow-lg z-20 w-64">
              <ul class="max-h-60 overflow-y-auto">
                <template x-for="loc in locations" :key="loc.id">
                  <li class="px-3 py-1 hover:bg-gray-100 cursor-pointer"
                      @click="toggleLocation(loc)">
                    <span x-text="loc.name"></span>
                    <i class="fa fa-check float-right" x-show="selectedIds.includes(loc.id)"></i>
                  </li>
                </template>
              </ul>
              <div class="p-2 border-t text-center">
                <button type="button" @click="applySelection()"
                        class="text-blue-600 font-semibold">
                  Apply
                </button>
              </div>
            </div>

            <input type="hidden" name="position" :value="selectedIds.join(',')" />
          </div> 
        </div>
        <div class="table-scroll-wrapper">
          <table id="reportsTable">
            <thead>
              <tr>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">ID – Name</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">Standort</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">Abteilung</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">Kostenstelle</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">Offene Positionen</th>
                <th class="px-4 py-2 text-left text-xs font-semibold text-gray-600">Besetzte</th>
              </tr>
            </thead>
            <tbody>
              {% for ja in open_jobads %}
              <tr>
                <td>{{ ja.id }} – {{ ja.position.title }}</td>
                <td>{{ ja.location }}</td>
                <td>{{ ja.department_value }}</td>
                <td>{{ ja.cost_department }}</td>
                <td>
                  {% if ja.open_positions > 100 %}
                    &infin;
                  {% else %}
                    {{ ja.open_positions }}
                  {% endif %}
                </td>
                <td>{{ ja.occupied }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </form>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    (function() {
      const labels = {{ labels|safe }};
      const actualPositions = {{ actual_positions|safe }};
      const approvedCounts = {{ approved_counts|safe }};
      const interviewCounts = {{ interview_counts|safe }};
      const interviewDoneCounts = {{ interview_done_counts|safe }};
      const selectionCounts = {{ selection_counts|safe }};
      const tentativelyCounts = {{ tentatively_counts|safe }};

      // clamp positions at 30 for the data
      const displayPositions = actualPositions.map(op => op > 30 ? 30 : op);

      // compute the maximum of (30 or total candidates stack) so the axis fits everything
      const maxStack = Math.max(
        30,
        ...labels.map((_, i) =>
          approvedCounts[i]
        + interviewCounts[i]
        + interviewDoneCounts[i]
        + selectionCounts[i]
        + tentativelyCounts[i]
        )
      );

      const ctx = document.getElementById('jobadsChart').getContext('2d');
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels,
          datasets: [
            // ── Offene Positionen ───────────────────
            {
              label: 'Offene Positionen',
              data: displayPositions,
              backgroundColor: 'rgba(61, 118, 239, 0.6)',
              borderColor:     'rgba(37, 99, 235, 1)',
              borderWidth: 1,
              borderRadius: 2,
              stack: 'positions',
              barThickness: 8
            },

            {
              label: 'Offene Kandidaten',
              data: approvedCounts,
              backgroundColor: 'rgba(254, 202, 202, 0.8)',
              stack: 'c',
            },
            {
              label: 'Interview angefragt',
              data: interviewCounts,
              backgroundColor: 'rgba(252, 165, 165, 0.8)',
              stack: 'c',
            },
            {
              label: 'Interview bestätigt',
              data: interviewDoneCounts,
              backgroundColor: 'rgba(248, 113, 113, 0.8)',
              stack: 'c',
            },
            {
              label: 'Kandidat bestätigen',
              data: selectionCounts,
              backgroundColor: 'rgba(239, 68, 68, 0.8)',
              stack: 'c',
            },
            {
              label: 'Vorläufig besetzt',
              data: tentativelyCounts,
              backgroundColor: 'rgba(220, 38, 38, 0.8)',
              stack: 'c',
            },
          ]
        },
        options: {
          // default is indexAxis: 'x' → vertical bars
          responsive: false,
          maintainAspectRatio: false,
          scales: {
            x: {
              stacked: false, 
              title: { display: true, text: 'JobAd ID' },
              ticks: {
                autoSkip: false,
                callback: idx => labels[idx]  // show numeric IDs
              },
              grid: { color: 'rgba(203, 213, 225, 0.5)' },
              categoryPercentage: 0.9,
              barPercentage: 0.7,
            },
            y: {
              stacked: true,
              min: 0,
              max: maxStack,
              title: { display: true, text: 'Anzahl' },
              grid: { color: 'rgba(203, 213, 225, 0.3)' }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              align: 'start',
              labels: { boxWidth: 12, padding: 8 }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              filter: function(tooltipItem) {
                // only show if the value at this point is non-zero
                return tooltipItem.parsed.y !== 0;
              },
              callbacks: {
                title: items => `JobAd ${labels[items[0].dataIndex]}`,
                label: ctx => {
                  const i   = ctx.dataIndex;
                  const nom = ctx.dataset.label;

                  if (nom === 'Offene Positionen') {
                    const actual = actualPositions[i];
                    return `Offene Positionen: ${actual > 30 ? '∞' : actual}`;
                  }
                  return `${nom}: ${ctx.parsed.y}`;
                }
              }
            }
          }
        }
      });
    })();
  </script>
  <script>
    function filterTable(filter) {
      filter = filter.toLowerCase();
      document.querySelectorAll('#reportsTable tbody tr')
        .forEach(row => {
          const text = row.innerText.toLowerCase();
          row.style.display = text.includes(filter) ? '' : 'none';
        });
    }
    function multiLocationFilter({ locations, initialSelected }) {
      return {
        open: false,
        locations,
        selectedIds: [],
        selectedLabel: '',

        init() {
          if (initialSelected) {
            this.selectedIds = initialSelected.split(',');
            if (this.selectedIds.includes('')) this.selectedIds = [''];
          } else {
            this.selectedIds = [''];
          }
          this.updateLabel();
        },

        updateLabel() {
          if (this.selectedIds.includes('') || this.selectedIds.length === 0) {
            this.selectedLabel = this.locations[0].name;
          } else {
            this.selectedLabel = this.locations
              .filter(l => this.selectedIds.includes(l.id))
              .map(l => l.name)
              .join(', ');
          }
        },

        toggleLocation(loc) {
          if (loc.id === '') {
            this.selectedIds = [''];
          } else {
            if (this.selectedIds.includes('')) this.selectedIds = [];
            if (this.selectedIds.includes(loc.id)) {
              this.selectedIds = this.selectedIds.filter(id => id !== loc.id);
              if (!this.selectedIds.length) this.selectedIds = [''];
            } else {
              this.selectedIds.push(loc.id);
            }
          }
          this.updateLabel();
        },

        applySelection() {
          this.open = false;
          this.$nextTick(() => document.getElementById('filterForm').submit());
        }
      };
    }
  </script>
{% endblock %}
