from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views

from common.views import CustomLoginView, CustomPasswordResetConfirmView, CustomPasswordResetView, GenerateExcelView, datenschutz, export_reaction_time_excel, export_reports_excel, export_supplier_excel, impressum, index, nutzungsbestimmungen, reaction_time_report, reports_view, supplier_evaluation
from company import views
from company.views import CompanyListView, CompanyCreateView, company_detail, CompanyDeleteView, PositionCreateView, \
    PositionUpdateView, LocationCreateView, SupplierListView, SupplierCreateView, MasterVendorListView, \
    MasterVendorCreateView, company_document_delete, company_document_download, company_structure, documents_overview, select_company_view
from jobad.views import CandidateListView, CandidateCreateView, approve_candidate, approve_jobad, cancel_jobad_application, candidate_detail, JobadListView, JobadCreateView, \
    JobadSelectCompanyView, candidate_document_delete, candidate_document_download, change_jobad_status, confirm_deployment, confirm_selection, create_interview, create_selection, delete_candidate, download_document, extract_profile_picture, interview_details, jobad_application_details, jobad_detail, JobadFavouritesView, JobadArchivedView, import_candidate, jobad_document_download, reject_jobad, reject_profile, rejection_reasons, skip_interview, terminate_deployment, update_interview, update_interview_status, upload_candidate, \
    JobadDeleteView

from company.views import CompanyListView, CompanyCreateView, company_detail, CompanyDeleteView, \
    PositionCreateView, PositionUpdateView, LocationCreateView, SupplierListView, SupplierCreateView, \
    MasterVendorListView, MasterVendorCreateView

from userprofiles.views import create_user, create_user_profile, edit_user_profile, profile_settings, profile_view
urlpatterns = [
    path('admin/', admin.site.urls),
    path('i18n/', include('django.conf.urls.i18n')),
    
    path('login/', CustomLoginView.as_view(template_name='userprofiles/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('password_reset/', CustomPasswordResetView.as_view(), name='password_reset'),
    path("password_reset/done/", auth_views.PasswordResetDoneView.as_view(template_name="registration/password_reset_done.html"), name="password_reset_done"),
    path("reset/<uidb64>/<token>/", CustomPasswordResetConfirmView.as_view(template_name="registration/password_reset_confirm.html"), name="password_reset_confirm"),
    path("reset/done/", auth_views.PasswordResetCompleteView.as_view(template_name="registration/password_reset_complete.html"), name="password_reset_complete"),

    path('', index.as_view(), name='index'),
    path('candidates/', CandidateListView.as_view(), name='candidate-list'),
    path('candidates/add/', CandidateCreateView.as_view(), name='candidate-add'),
    path('companies/', CompanyListView.as_view(), name='company-list'),
    path('companies/add/', CompanyCreateView.as_view(), name='company-add'),
    path('companies/<int:pk>/', company_detail, name="company-detail"),
    path('candidates/<int:pk>/', candidate_detail, name="candidate-detail"),
    path('candidates/<int:pk>/delete/', delete_candidate, name='delete-candidate'),
    path('candidate/<int:pk>/profile/', candidate_detail, name='candidate-profile'),
    path('companies/<int:pk>/delete/', CompanyDeleteView.as_view(), name="company-delete"),
    path('companies/<int:company_id>/positions/add/', PositionCreateView.as_view(), name='position-add'),
    path('reasons/<int:pk>/delete/', views.delete_reason, name='reason-delete'),
    path('positions/<int:pk>/edit/', PositionUpdateView.as_view(), name='position-edit'),
    path('positions/<int:pk>/delete/', views.delete_position, name='position-delete'),
    path('companies/<int:company_id>/locations/add/', LocationCreateView.as_view(), name='location-add'),   
    path('locations/<int:pk>/delete/', views.delete_location, name='location-delete'),
    path('companies/<int:pk>/documents/upload', views.upload_document, name="upload-document"),
    path("documents/<str:doc_type>/<int:pk>/download/", download_document, name="document_download"),
    path('candidates/<int:pk>/documents/upload', views.upload_document, name="upload-document"),
    path('suppliers/', SupplierListView.as_view(), name='supplier-list'),
    path('suppliers/add/', SupplierCreateView.as_view(), name='supplier-add'),
    path('master-vendors/', MasterVendorListView.as_view(), name='master-vendor-list'),
    path('master-vendors/add/', MasterVendorCreateView.as_view(), name='master-vendor-add'),
    path('jobads/', JobadListView.as_view(), name='jobad-list'),
    path('jobads/favourites/', JobadFavouritesView.as_view(), name='jobad-favourites'),
    path('jobads/archived/', JobadArchivedView.as_view(), name='jobad-archived'),
    path('jobads/add/', JobadSelectCompanyView.as_view(), name='jobad-add'),
    path('jobads/select-company/', select_company_view, name='select-company'),
    path('jobads/<int:pk>/', jobad_detail, name='jobad-detail'),
    path('jobad/<int:pk>/approve/', approve_jobad, name='approve-jobad'),
    path('jobad/<int:pk>/reject/', reject_jobad, name='reject-jobad'),
    path('jobad-application/<int:pk>/approve/', approve_candidate, name='approve_candidate'),
    path('jobads/add/<int:company_id>/', JobadCreateView.as_view(), name='jobad-add-step2'),
    path('jobads/<int:pk>/import/', import_candidate, name='import-candidate'),
    path('jobads/<int:pk>/upload-candidate/', upload_candidate, name='upload-candidate'),
    path('companies/<int:company_id>/update-structure/', views.update_structure, name='update-structure'),
    path('structure/add-node/', views.add_node, name='add-node'),  # Top-level node creation
    path('structure/<str:parent_id>/add-node/', views.add_node, name='add-node'),  # Child node creation
    path('structure/<str:parent_id>/children/', views.get_structure_by_parent, name='structure-children'), # Child nodes
    path('structure/children/', views.get_structure_by_parent, name='structure-top-level'),  # Top-level nodes
    path('companies/<int:pk>/supplier/', company_detail, name='company-supplier-detail'),
    path('companies/<int:company_id>/link-supplier/', views.link_supplier, name='link_supplier'),
    path('company/<int:company_id>/structure/', company_structure, name='company-structure'),
    path('jobads/<int:pk>/delete/', JobadDeleteView.as_view(), name='jobad-delete'),
    path('jobad-application/<int:pk>/details/', jobad_application_details, name='jobad-application-details'),
    path('jobad-application/<int:pk>/create-selection/', create_selection, name='create-selection'),
    path('jobad-application/<int:pk>/skip-interview/', skip_interview, name='skip-interview'),
    path('jobad-application/<int:pk>/confirm-deployment/', confirm_deployment, name='confirm-deployment'),
    path('jobad-application/<int:pk>/confirm-selection/', confirm_selection, name='confirm-selection'),
    path('jobad-application/<int:application_id>/create-interview/', create_interview, name='create-interview'),
    path('jobad-application/<int:application_id>/interview-details/', interview_details, name='interview-details'),
    path('jobad-application/<int:application_id>/update-interview/', update_interview, name='update-interview'),
    path('jobad-application/<int:application_id>/update-interview-status/', update_interview_status, name='update_interview_status'),
    path('profile/', profile_view, name='user-profile'),
    path('myprofile/', profile_settings, name='profile-settings'),
    path('profile/create/', create_user_profile, name='create_user_profile'),
    path('profile/<int:user_id>/edit/', edit_user_profile, name='update_user_profile'),
    path('jobad-application/<int:pk>/cancel/', cancel_jobad_application, name='cancel_jobad_application'),
    path('api/get-structure-connections', views.get_structure_connections, name='get_structure_connections'),
    path('generate-excel/', GenerateExcelView.as_view(), name='generate-excel'),
    path('company/<int:company_id>/rejection-reasons/', rejection_reasons, name='rejection-reasons'),
    path('jobad-application/<int:application_id>/reject/', reject_profile, name='reject-profile'),
    path('jobad/<int:pk>/change-status/', change_jobad_status, name='change-jobad-status'),
    path('extract-profile-picture/', extract_profile_picture, name='extract-profile-picture'),
    path('candidate/document/download/<int:doc_id>/', candidate_document_download, name='candidate-document-download'),
    path('company/document/download/<int:doc_id>/', company_document_download, name='company-document-download'),
    path('company/document/delete/<int:doc_id>/', company_document_delete, name='company-document-delete'),
    path('candidate/document/delete/<int:doc_id>/', candidate_document_delete, name='candidate-document-delete'),
    path('jobad/<int:doc_id>/', jobad_document_download, name='jobad-document-download'),
    path('worker/<int:worker_id>/terminate/', terminate_deployment, name='terminate-deployment'),
    path('documents/', documents_overview, name='documents-overview'),
    path('impressum/', impressum, name='impressum'),
    path('nutzungsbestimmungen/', nutzungsbestimmungen, name='nutzungsbestimmungen'),
    path('datenschutz/', datenschutz, name='datenschutz'),
    path('reports/', reports_view, name='reports'),
    path('reports/reaction-time/', reaction_time_report, name='reports-reaction-time'),
    path('reports/supplier-evaluation/', supplier_evaluation, name='reports-supplier-evaluation'),
    # CSV exports for report
    path('reports/reaction-time/excel/', export_reaction_time_excel, name='reports-reaction-time-excel'),
    path('reports/supplier-evaluation/excel/', export_supplier_excel, name='reports-supplier-evaluation-excel'),
    path('reports/excel', export_reports_excel, name='reports-excel'),
]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
