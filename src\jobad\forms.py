import json

from django import forms

from company.models import Company, CompanyPosition, CompanyLocation
from jobad.models import Candidate, CandidateDocument, Jobad, JobaDocument
from django_countries.widgets import CountrySelectWidget
from django.utils.translation import gettext_lazy as _


class CandidateForm(forms.ModelForm):
    class Meta:
        model = Candidate
        fields = ['company', 'title', 'firstname', 'lastname', 'date_of_birth','country',
                    'comment', 'profile_picture', 'hourly_rate']
        labels = {
            'company': _('Company'),
            'title': _('Title'),
            'firstname': _('First Name'),
            'lastname': _('Last Name'),
            'date_of_birth': _('Date of Birth'),
            'country': _('Origin'),
            'comment': _('Comment'),
            'hourly_rate': _('Hourly Rate €'),
            'cv': _('CV'),
            'profile_picture': _('Profile Picture'),
        }
        widgets = {
            'company': forms.Select(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
            'title': forms.Select(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
            'firstname': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
            'lastname': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
            'date_of_birth': forms.DateInput(
                format='%Y-%m-%d',
                attrs={
                    'type': 'date',
                    'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
                }
            ),
            'comment': forms.Textarea(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
                'rows': 1,
            }),
            'hourly_rate': forms.NumberInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
            'profile_picture': forms.FileInput(attrs={
                'class': 'hidden'
            }),
            'country': CountrySelectWidget(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not (self.instance and self.instance.pk):
            self.fields['hourly_rate'].required = True
        self.fields['company'].queryset = Company.objects.filter(company_type__in=['vendor', 'supplier'])
        self.fields['company'].empty_label = None
        self.fields['company'].initial = None
        # self.fields['g25'].required = False

class DocumentForm(forms.ModelForm):
    class Meta:
        model = CandidateDocument
        fields = ['document', 'expiration_date', 'document_type']

class WorkingHoursField(forms.MultiValueField):
    def __init__(self, *args, **kwargs):
        fields = [
            forms.TimeField(widget=forms.TimeInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5',
                'placeholder': 'Start time (HH:MM)'
            })),
            forms.TimeField(widget=forms.TimeInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5',
                'placeholder': 'End time (HH:MM)'
            }))
        ]
        super().__init__(fields=fields, require_all_fields=True, *args, **kwargs)

    def compress(self, data_list):
        if data_list:
            return f"{data_list[0]} - {data_list[1]}"
        return ''

class JobadForm(forms.ModelForm):
    position_text = forms.CharField(
        max_length=100,
        required=False,
        label=_("Custom position title")
    )
    tasks = forms.CharField(widget=forms.Textarea(attrs={
        'class': 'bg-white border-2 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-accent-500 focus:border-accent-500 block w-full p-2.5',
        'rows': 4
    }), required=False)

    description = forms.CharField(widget=forms.Textarea(attrs={
        'class': 'bg-white border-2 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-accent-500 focus:border-accent-500 block w-full p-2.5',
        'rows': 4
    }), required=False)

    requirements = forms.CharField(widget=forms.Textarea(attrs={
        'class': 'bg-white border-2 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-accent-500 focus:border-accent-500 block w-full p-2.5',
        'rows': 4
    }), required=False)

    working_hours = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )
    
    start_date = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
        })
    )

    class Meta:
        model = Jobad
        fields = [
            'company', 'position', 'location', 'time_period', 'total', 'cost_department', 'description',
            'employee_group', 'weekly_working_hours', 'contact', 'tasks', 'requirements', 'created_by', 'working_hours', 'start_date'
        ]
        widgets = {
            'company': forms.Select(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'position': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'location': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'time_period': forms.NumberInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'total': forms.NumberInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'cost_department': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'created_by': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'employee_group': forms.Select(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'weekly_working_hours': forms.NumberInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
            }),
            'contact': forms.Textarea(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none',
                'rows': 4
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['company'].queryset = Company.objects.filter(company_type='customer')
        self.fields['position'].required = False
        self.fields['position_text'].required = False
        
    def clean(self):
        cleaned = super().clean()
        if not cleaned.get('position') and not cleaned.get('position_text'):
            raise forms.ValidationError(
                _("Please either select an existing position or enter one above.")
            )
        return cleaned

class JobaDocumentForm(forms.ModelForm):
    class Meta:
        model = JobaDocument
        fields = ['document']