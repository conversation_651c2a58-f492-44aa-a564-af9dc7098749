{% load i18n %}
{% trans "Your candidate has been rejected" as candidate_rejected %}
{% trans "Deployment has been confirmed" as deployment_confirmed %}
<div x-data="{ 
    step: 1, 
    userRole: {{ user_role_number }},
    userCompanyType: '{{ user_company_type }}',
    contactPersonName2: '',
    contactPersonPhone2: '',
    contactPersonName: '',
    contactPersonPhone: '',
    lastSuggestionBy: '',
    approvalStep: false,
    selectedDate1: '', 
    waitingForApproval: false,
    selectedTimeStart1: '', 
    selectedTimeEnd1: '', 
    selectedDate2: '', 
    selectedTimeStart2: '',
    selectedTimeEnd2: '', 
    chosenDate: '', 
    chosenInterview: '',
    alternativeDate: '', 
    alternativeStartTime: '',
    alternativeEndTime: '',
    displayAlternativeDate: '',
    displayAlternativeStartTime: '',
    displayAlternativeEndTime: '',
    hasSentDates: false, 
    showOptions: false, 
    suggestAlternative: false, 
    skippedInterview: false, 
    applicationCanceled: false,
    openEditModal: false, 
    sendFinalOption: false,
    decisionMade: false,
    suggestionSent: false,
    selectedRadio: '',
    openCancelConfirmation: false,
    applicationData: {},
    openRejectModal: false,
    rejectionReasons: [],
    selectedRejectionReason: '',
    errors: {
        start_date: false,
        end_date: false,
        start_time: false,
        contact_person_name: false,
        contact_person_phone: false,
    },
    init() {
        const today = new Date();
        const tomorrow = new Date();
        tomorrow.setDate(today.getDate() + 1);

        const formattedTomorrow = tomorrow.toISOString().split('T')[0];

        this.selectedDate1 = formattedTomorrow;
        this.selectedDate2 = formattedTomorrow;
        this.alternativeDate = formattedTomorrow;
        this.displayAlternativeDate = formattedTomorrow;
    },
    formatDate(date) {
        if (!date) return '';
        const [year, month, day] = date.split('-');
        return `${day}.${month}.${year}`;
    },
    fetchApplicationData(applicationId) {
        fetch(`/jobad-application/${applicationId}/details/`, {
            method: 'GET',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            this.applicationData = data;
            this.lastSuggestionBy = data.last_suggestion_by;
            if (data.interview_skipped === true) {
                this.skippedInterview = true;
            }
            if (data.stage === 'canceled') {
                this.applicationCanceled = true;
            }

            if (data.stage === 'candidate_selection') {
                this.applicationData.start_time = data.start_time;
                this.applicationData.contact_person_name = data.contact_person_name;
                this.applicationData.contact_person_phone = data.contact_person_phone;
            }

            if (['canceled', 'rejected'].includes(data.stage)) {
            this.skippedInterview = data.stage === 'canceled';
            this.applicationCanceled = data.stage === 'rejected';
            }

            // Check if the stage is interview, and load interview data
            if (['interview', 'interview_completed', 'tentatively_occupied', 'completed'].includes(data.stage)) {
                fetch(`/jobad-application/${applicationId}/interview-details/`)
                    .then(response => response.json())
                    .then(interviewData => {
                        console.log('INTERVIEW', interviewData);
                        this.selectedDate1 = interviewData.interview_dates[0];
                        this.selectedTimeStart1 = interviewData.interview_start_times[0];
                        this.selectedTimeEnd1 = interviewData.interview_end_times[0];

                        if (interviewData.interview_dates.length > 1) {
                            this.selectedDate2 = interviewData.interview_dates[1];
                            this.selectedTimeStart2 = interviewData.interview_start_times[1];
                            this.selectedTimeEnd2 = interviewData.interview_end_times[1];
                        }

                        if (interviewData.stage === 'requested_again') {
                            this.suggestionSent = true;
                            this.alternativeDate = interviewData.interview_dates[0];
                            this.alternativeStartTime = interviewData.interview_start_times[0];
                            this.alternativeEndTime = interviewData.interview_end_times[0];

                            this.displayAlternativeDate = this.alternativeDate;
                            this.displayAlternativeStartTime = this.alternativeStartTime;
                            this.displayAlternativeEndTime = this.alternativeEndTime;
                        }

                        // Ensure the contact person details are set for Step 4
                        if (['interview_completed', 'tentatively_occupied', 'completed'].includes(data.stage)) {
                            this.chosenInterview = `${this.formatDate(this.selectedDate1)}, ${this.selectedTimeStart1} to ${this.selectedTimeEnd1}`;
                            this.contactPersonName2 = interviewData.contact_person_name;
                            this.contactPersonPhone2 = interviewData.contact_person_phone;
                            this.applicationData.contact_person_name = interviewData.contact_person_name;
                            this.applicationData.contact_person_phone = interviewData.contact_person_phone;
                        }
                    });
            }

            // Ensure the dates are in YYYY-MM-DD format
            if (data.start_date) {
                data.start_date = data.start_date.split('T')[0];
            }
            if (data.end_date) {
                data.end_date = data.end_date.split('T')[0];
            }

            if (data.interview_skipped === true) {
                this.skippedInterview = true;
            }
            console.log('JEJEJEJE', data);
            // Check the stage and adjust the step accordingly
            switch (data.stage) {
                case 'requested':
                    this.step = 3;
                    this.waitingForApproval = true;
                    break;
                case 'approved':
                    this.step = 2;
                    break;
                case 'interview':
                    this.step = 2;
                    this.hasSentDates = true;
                    break;
                case 'candidate_selection':
                    this.step = 3;
                    this.approvalStep = true;
                    break;
                case 'interview_completed':
                    this.step = 3;
                    this.approvalStep = false;
                    break;
                case 'tentatively_occupied':
                    this.step = 4;
                    break;
                case 'completed':
                    this.step = 5;
                    break;
                case 'canceled':
                    this.step = 5;
                    break;
                case 'rejected':
                    this.step = 5;
                    this.applicationCanceled = true;
                    break;
                default:
                    this.step = 1;
            }

            this.openEditModal = true;
        })
        .catch(error => {
            console.error('Error fetching application data:', error);
        });
    },
    fetchRejectionReasons(companyId, settingsId) {
        fetch(`/company/${companyId}/rejection-reasons/?settings_id=${settingsId}`, {
            method: 'GET',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            this.rejectionReasons = data.reasons;
            this.openRejectModal = true;
        })
        .catch(error => {
            console.error('Error fetching rejection reasons:', error);
        });
    },
    // create Selection entity
    submitSelection(startDate, endDate, startTime, contactPersonName, contactPersonPhone) {
        if (!this.applicationData.start_date || !this.applicationData.end_date || !this.applicationData.start_time) {
            console.error('Missing required fields');
            return;
        }
        // Reset previous errors
        this.errors = {
            start_date: false,
            end_date: false,
            start_time: false,
            contact_person_name: false,
            contact_person_phone: false,
        };

        let hasError = false;

        // Validate each input
        if (!startDate) {
            this.errors.start_date = true;
            hasError = true;
        }
        if (!endDate) {
            this.errors.end_date = true;
            hasError = true;
        }
        if (!startTime) {
            this.errors.start_time = true;
            hasError = true;
        }
        if (!contactPersonName) {
            this.errors.contact_person_name = true;
            hasError = true;
        }
        if (!contactPersonPhone) {
            this.errors.contact_person_phone = true;
            hasError = true;
        }

        // Stop submission if there are errors
        if (hasError) {
            return;
        }

        // Proceed with the fetch call if validation passes
        fetch(`/jobad-application/${this.applicationData.id}/create-selection/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_date: this.applicationData.start_date,
                end_date: this.applicationData.end_date,
                start_time: this.applicationData.start_time,
                contact_person_name: this.applicationData.contact_person_name,
                contact_person_phone: this.applicationData.contact_person_phone
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = data.stage;
                this.contactPersonName2 = this.applicationData.contact_person_name;
                this.contactPersonPhone2 = this.applicationData.contact_person_phone;
                // Check if the company is NOT LGI
                if (data.requiresApproval) {
                    this.step = 3;  // Stay on step 3
                    this.applicationData.stage = 'requested'; // Set to requested
                    this.waitingForApproval = true; // New state for approval step
                } else {
                    this.step = 3; // Move to normal approval step
                    this.approvalStep = true;
                }
            } else {
                alert('Failed to submit selection.');
            }
        })
        .catch(error => {
            console.error('Error submitting selection:', error);
        });
    },
    confirmSelection() {
        // We confirm the Selection, marking it as completed and moving to the next stage
        fetch(`/jobad-application/${this.applicationData.id}/confirm-selection/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                stage: 'completed'  // Set the Selection stage to 'completed'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'tentatively_occupied';  // Move to step 4 after the update
                this.step = 4;
            } else {
                alert('Failed to confirm the selection.');
            }
        })
        .catch(error => {
            console.error('Error confirming selection:', error);
        });
    },
    confirmDeployment(finalStartDate) {
        if (!finalStartDate) {
            alert('Please provide the exact start date.');
            return;
        }

        const formattedFinalStartDate = new Date(finalStartDate).toISOString().split('T')[0];

        fetch(`/jobad-application/${this.applicationData.id}/confirm-deployment/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                final_start_date: formattedFinalStartDate
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'completed';
                this.step = 5;
            } else {
                alert('Failed to confirm deployment.');
            }
        })
        .catch(error => {
            console.error('Error confirming deployment:', error);
        });
    },
    // first interview creation
    sendInterviewProposal() {
        const interviewData = {
            application_id: this.applicationData.id,
            interview_dates: [this.selectedDate1, this.selectedDate2], // Array of dates
            interview_start_times: [this.selectedTimeStart1, this.selectedTimeStart2], // Start times
            interview_end_times: [this.selectedTimeEnd1, this.selectedTimeEnd2], // End times
            contact_person_name: this.contactPersonName,
            contact_person_phone: this.contactPersonPhone,
            stage: 'interview',
            status: 'created'
        };

        fetch(`/jobad-application/${this.applicationData.id}/create-interview/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(interviewData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.hasSentDates = true;
            } else {
                alert(data.error || 'Failed to send interview proposal.');
            }
        })
        .catch(error => {
            console.error('Error creating interview:', error);
            alert('An unexpected error occurred while sending the interview proposal.');
        });
    },
    // Suggest another alternative date/time
    suggestAnotherDate() {
        fetch(`/jobad-application/${this.applicationData.id}/update-interview/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                alternative_date: this.alternativeDate,
                alternative_start_time: this.alternativeStartTime,
                alternative_end_time: this.alternativeEndTime,
                stage: 'requested_again',
                last_suggestion_by: this.userCompanyType === 'supplier' ? 'supplier' : 'customer',
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.displayAlternativeDate = this.alternativeDate;
                this.displayAlternativeStartTime = this.alternativeStartTime;
                this.displayAlternativeEndTime = this.alternativeEndTime;
                
                // Move to the next part of the UI flow by setting `suggestionSent` to true
                this.suggestionSent = true;
                this.chosenDate = ''; // Reset chosen date since an alternative is suggested
                this.lastSuggestionBy = this.userCompanyType === 'supplier' ? 'supplier' : 'customer'; 
            } else {
                alert('Failed to send alternative suggestion.');
            }
        })
        .catch(error => {
            console.error('Error sending alternative suggestion:', error);
        });
    },
    // Accept the suggested date and move to the next step.
    acceptAlternativeDate() {
        // First, make the necessary backend updates
        fetch(`/jobad-application/${this.applicationData.id}/update-interview-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chosen_date: this.alternativeDate,
                chosen_start_time: this.alternativeStartTime,
                chosen_end_time: this.alternativeEndTime
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'interview_completed'; 
                this.decisionMade = true;
                this.chosenDate = `${this.alternativeDate}, ${this.alternativeStartTime} to ${this.alternativeEndTime}`;
                this.suggestionSent = false;

                this.step = 3;
            } else {
                alert('Failed to update interview and JobadApplication stages.');
            }
        })
        .catch(error => {
            console.error('Error updating interview and JobadApplication:', error);
        });
    },
    cancelApplication() {
        fetch(`/jobad-application/${this.applicationData.id}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'canceled';  // Update stage
                this.step = 5;  // Move to step 5
                this.openCancelConfirmation = false;  // Close confirmation modal
                alert('Application has been successfully canceled.');
            } else {
                alert('Failed to cancel the application.');
            }
        })
        .catch(error => {
            console.error('Error canceling application:', error);
            alert('An error occurred while canceling the application.');
        });
    },
    rejectProfile() {
        // only force a reason when company ≠ 2
        if (this.applicationData.company_id !== 2 && !this.selectedRejectionReason) {
            alert('Please select a rejection reason.');
            return;
        }

        // build the payload
        const body = { stage: 'rejected' };
        if (this.applicationData.company_id !== 2) {
            body.rejection_reason_id = this.selectedRejectionReason;
        }

        fetch(`/jobad-application/${this.applicationData.id}/reject/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'rejected';
                this.openRejectModal = false;
                this.step = 5;
            } else {
                if (this.applicationData.company_id !== 2) {
                    alert('Failed to reject the profile.');
                }	
            }
        })
        .catch(error => console.error('Error rejecting profile:', error));
    },
    approveCandidate() {
        fetch(`/jobad-application/${this.applicationData.id}/approve/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ stage: 'candidate_selection' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.applicationData.stage = 'candidate_selection'; // Move to the next stage
                this.waitingForApproval = false;
                this.approvalStep = true; // Move to the approval step
            } else {
                alert('Failed to approve the candidate.');
            }
        })
        .catch(error => {
            console.error('Error approving candidate:', error);
        });
    },
}" x-init="init()">
    <!-- Modal Trigger -->
    <a @click="fetchApplicationData({{ application.id }})" class="bg-accent-500 text-white hover:bg-secondary-500 hover:text-black py-1 px-3 hover:bg-red-600 cursor-pointer">{% trans "Edit" %}</a>

    <!-- Modal Layout -->
    <div x-show="openEditModal && applicationData.id === {{ application.id }}" x-cloak class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
        <div class="bg-white p-8 shadow-lg w-full max-w-2xl">
            <!-- Candidate Name at the Top -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl text-gray-800">{{ application.candidate.firstname|title }} {{ application.candidate.lastname|title }}</h2>
                <button @click="openEditModal = false" class="text-gray-500 text-2xl hover:text-gray-700 hover:border-black">&times;</button>
            </div>

            <!-- Steps and Content in the Same Container -->
            <div class="space-y-6">
                <!-- Step 1: Candidate Approved -->
                <div class="flex items-start mb-4">
                    <div class="bg-white w-5 h-5 flex items-center justify-center mr-4">✔</div>
                    <div>
                        <p class="font-bold text-gray-800 flex align-start">{% trans "Candidate approved" %}</p>
                        <p class="text-sm text-gray-600">
                            {% if applicationData.stage == "canceled" or applicationData.stage == "rejected" %}
                                {% trans "Your candidate has been rejected" %}
                            {% else %}
                                {% trans "Your candidate has been accepted" %}
                            {% endif %}                        
                        </p>
                    </div>
                </div>                               

                <div class="flex items-start mb-4">
                    <div :class="{
                        'bg-gray-500': applicationData.stage === 'requested',  // Keep gray if requested
                        'bg-accent-500': applicationData.stage !== 'requested' && step === 2, 
                        'bg-white': step > 2
                    }" class="rounded-full text-white w-5 h-5 flex items-center justify-center mr-4">
                        <span x-show="!skippedInterview && !decisionMade && step <= 2">2</span>
                        <span x-show="skippedInterview || decisionMade || step > 2">✔</span>
                    </div>
                    <div>
                        <p class="font-bold flex align-start" :class="{ 'text-gray-500': step < 2, 'text-accent-500': step === 2, 'text-gray-800': step > 2 }">{% trans "Arrange interview/trial day" %}</p>
                        <template x-if="skippedInterview && !applicationCanceled">
                            <p class="text-sm text-gray-600 flex align-start">{% trans "Interview has been skipped" %}</p>
                        </template>
                        <template x-if="applicationCanceled">
                            <p class="text-sm text-gray-600 flex align-start">{% trans "Your candidate has been rejected" %}</p>
                        </template>

                        <!-- Compressed Step 2 after selecting a date -->
                        <template x-if="chosenInterview && !suggestionSent">
                            <div class="text-left">
                                <p class="text-sm text-gray-600">{% trans "Date:" %} <span x-text="chosenInterview"></span></p>
                                <p class="text-sm text-gray-600"><span x-text="contactPersonName2"></span>  <span x-text="contactPersonPhone2"></span></p>
                            </div>    
                        </template>

                        <!-- Expanded Step 2 (Choosing between Dates or Suggesting a New One) -->
                        <template x-if="hasSentDates && !skippedInterview && !decisionMade && !suggestionSent">
                            <div>
                                <div class="text-left" x-show="userCompanyType === 'supplier' || userCompanyType === 'global' || userCompanyType === 'vendor' ">
                                    <p class="text-sm text-gray-600 mb-2">{% trans "Choose one of the proposed dates or suggest an alternative." %}</p>
                                    <p>{{contact_person_name}}</p>
                                    <div class="mb-4 space-y-2">
                                        <template x-if="selectedTimeStart1">
                                            <label class="flex items-center space-x-2">
                                                <input type="radio" name="interview_option" @click="chosenDate = selectedDate1 + ', ' + selectedTimeStart1 + ' to ' + selectedTimeEnd1; suggestAlternative = false; selectedRadio = 'date1';" class="mr-2">
                                                {% trans "Date 1:" %} 
                                                <span x-text="formatDate(selectedDate1)"></span>, 
                                                <span x-text="selectedTimeStart1"></span>
                                                <span>-</span>
                                                <span x-text="selectedTimeEnd1"></span> {% trans "O'clock" %}
                                            </label>
                                        </template>
                                        <template x-if="selectedTimeStart2">
                                            <label class="flex items-center space-x-2">
                                                <input type="radio" name="interview_option" @click="chosenDate = selectedDate2 + ', ' + selectedTimeStart2 + ' to ' + selectedTimeEnd2; suggestAlternative = false; selectedRadio = 'date2';" class="mr-2">
                                                {% trans "Date 2:" %} 
                                                <span x-text="formatDate(selectedDate2)"></span>, 
                                                <span x-text="selectedTimeStart2"></span>
                                                <span class="px-1">-</span>
                                                <span x-text="selectedTimeEnd2"></span> {% trans "O'clock" %}
                                            </label>
                                        </template>
                                        <label class="flex items-center space-x-2">
                                            <!-- Radio for Suggesting Alternative Date -->
                                            <input type="radio" name="interview_option" @click="
                                                suggestAlternative = true;
                                                chosenDate = ''; 
                                                if (!alternativeDate) { 
                                                    alternativeDate = new Date().toISOString().split('T')[0]; 
                                                }
                                                selectedRadio = 'alternative';
                                            " class="mr-2">
                                            {% trans "Suggest alternate interview date" %}
                                        </label>
                                    </div>
    
                                    <!-- Show the Alternative Date fields only if "Suggest alternate interview date" is selected -->
                                    <template x-if="suggestAlternative">
                                        <div class="grid grid-cols-3 gap-4 mb-6">
                                            <div>
                                                <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Date" %}</label>
                                                <input type="date" x-model="alternativeDate" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Start" %}</label>
                                                <input type="time" x-model="alternativeStartTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative End" %}</label>
                                                <input type="time" x-model="alternativeEndTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                            </div>
                                        </div>
                                    </template>    
                                    <div class="flex justify-start">
                                        <!-- Send Button with Conditional Behavior -->
                                        <button @click="
                                            if (selectedRadio === 'alternative') {
                                                // Handle alternative suggestion
                                                suggestionSent = true;
                                                lastSuggestionBy = userCompanyType === 'supplier' ? 'supplier' : 'customer';
                                                fetch(`/jobad-application/${applicationData.id}/update-interview/`, {
                                                    method: 'POST',
                                                    headers: {
                                                        'X-CSRFToken': '{{ csrf_token }}',
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        alternative_date: alternativeDate,
                                                        alternative_start_time: alternativeStartTime,
                                                        alternative_end_time: alternativeEndTime,
                                                        stage: 'requested_again',
                                                        last_suggestion_by: lastSuggestionBy
                                                    })
                                                })
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.success) {
                                                        // Stay in step 2 but mark suggestion as sent
                                                        suggestionSent = true;
                                                        chosenDate = '';  // Reset chosen date since an alternative is suggested
                                                        alternativeDate = alternativeDate;
                                                        alternativeStartTime = alternativeStartTime;
                                                        alternativeEndTime = alternativeEndTime;
                                                        // for the visual
                                                        displayAlternativeDate = alternativeDate;
                                                        displayAlternativeStartTime = alternativeStartTime;
                                                        displayAlternativeEndTime = alternativeEndTime;
                                                    } else {
                                                        alert('Failed to send alternative suggestion.');
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error sending alternative suggestion:', error);
                                                });
                                            } else {
                                                // Directly set the chosen date as the final interview date
                                                fetch(`/jobad-application/${applicationData.id}/update-interview-status/`, {
                                                    method: 'POST',
                                                    headers: {
                                                        'X-CSRFToken': '{{ csrf_token }}',
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        chosen_date: selectedRadio === 'date1' ? selectedDate1 : selectedDate2,
                                                        chosen_start_time: selectedRadio === 'date1' ? selectedTimeStart1 : selectedTimeStart2,
                                                        chosen_end_time: selectedRadio === 'date1' ? selectedTimeEnd1 : selectedTimeEnd2
                                                    })
                                                })
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.success) {
                                                        // Update Alpine.js state directly to reflect changes without page reload
                                                        applicationData.stage = 'interview_completed';
                                                        chosenInterview = `${selectedRadio === 'date1' ? selectedDate1 : selectedDate2}, ${selectedRadio === 'date1' ? selectedTimeStart1 : selectedTimeStart2} to ${selectedRadio === 'date1' ? selectedTimeEnd1 : selectedTimeEnd2}`;
                                                        step = 3;  // Move to step 3 in the UI
                                                        decisionMade = true;  // Set this to indicate a decision has been made
    
                                                        // Optionally, display a confirmation message
                                                    } else {
                                                        alert('Failed to update the interview status.');
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error updating the interview status:', error);
                                                });
                                            }
                                        " class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-4">{% trans "Send" %}</button>
                                    </div>
                                </div>
                                <div class="text-left" x-show="userRole === 5 && userCompanyType === 'customer'">
                                    <p class="text-sm text-gray-600 mb-4">{% trans "Your interview suggestions have been sent:" %}</p>
                                    <ul class="mb-4 text-sm text-gray-700">
                                        <template x-if="selectedTimeStart1">
                                            <li>{% trans "Date 1: " %}<span x-text="formatDate(selectedDate1)"></span>, <span x-text="selectedTimeStart1"></span> - <span x-text="selectedTimeEnd1"></span></li>
                                        </template>
                                        <template x-if="selectedTimeStart2">
                                            <li>{% trans "Date 2: " %}<span x-text="formatDate(selectedDate2)"></span>, <span x-text="selectedTimeStart2"></span> - <span x-text="selectedTimeEnd2"></span></li>
                                        </template>
                                    </ul>
                                    <p class="text-sm text-gray-600">{% trans "Please wait for the supplier's response." %}</p>
                                </div>
                            </div>
                        </template>

                        <!-- Step 2: Suggesting Alternative Date or Accepting Existing Date -->
                        <template x-if="suggestionSent && !decisionMade">
                            <div>
                                <div x-show="lastSuggestionBy === 'supplier' && (userCompanyType === 'customer' || userCompanyType === 'global' || userCompanyType === 'vendor')" class="mt-4 text-left">
                                    <p class="text-sm text-gray-600">
                                        {% trans "The supplier has suggested a new date:" %}
                                        <span x-text="formatDate(displayAlternativeDate)"></span> {% trans "from " %}
                                        <span x-text="displayAlternativeStartTime"></span> {% trans "to " %}
                                        <span x-text="displayAlternativeEndTime"></span>
                                    </p>
                                    <p class="text-sm text-gray-600 mt-2 mb-2">{% trans "Please confirm or suggest another date" %}</p>
                                    <!-- Option to suggest another alternative -->
                                    <div class="grid grid-cols-3 gap-4 mb-6">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Date" %}</label>
                                            <input type="date" x-model="alternativeDate" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Start" %}</label>
                                            <input type="time" x-model="alternativeStartTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative End" %}</label>
                                            <input type="time" x-model="alternativeEndTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                    </div>
                            
                                    <div class="flex justify-start">
                                        <button @click="acceptAlternativeDate()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-4">{% trans "Accept Date" %}</button>
                                        <button @click="suggestAnotherDate()" class="border border-gray-900 hover:border-secondary-500 text-black hover:text-secondary-500 font-bold py-2 px-4 rounded mr-4">{% trans "Suggest Another" %}</button>
                                    </div>
                                </div>
                                <div x-show="lastSuggestionBy === 'customer' && (userCompanyType === 'supplier' || userCompanyType === 'global')" class="mt-4 text-left">
                                    <p class="text-sm text-gray-600">
                                        {% trans "Alternative interview suggestion received: " %}
                                        <span x-text="formatDate(displayAlternativeDate)"></span> {% trans "from " %}
                                        <span x-text="displayAlternativeStartTime"></span> {% trans "to " %}
                                        <span x-text="displayAlternativeEndTime"></span>
                                    </p>
                                    <p class="text-sm text-gray-600 mt-2 mb-2">{% trans "Please confirm or suggest another date" %}</p>
                                    <!-- Option to suggest another alternative -->
                                    <div class="grid grid-cols-3 gap-4 mb-6">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Date" %}</label>
                                            <input type="date" x-model="alternativeDate" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative Start" %}</label>
                                            <input type="time" x-model="alternativeStartTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Alternative End" %}</label>
                                            <input type="time" x-model="alternativeEndTime" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                    </div>
                            
                                    <div class="flex justify-start">
                                        <button @click="acceptAlternativeDate()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-4">{% trans "Accept Date" %}</button>
                                        <button @click="suggestAnotherDate()" class="border border-gray-900 hover:border-secondary-500 text-black hover:text-secondary-500 font-bold py-2 px-4 rounded mr-4">{% trans "Suggest Another" %}</button>
                                    </div>
                                </div>
                                <div x-show="lastSuggestionBy === userCompanyType" class="mt-4 text-left">
                                    <p class="text-sm text-gray-600">
                                        {% trans "Alternative interview suggestion sent: " %}
                                        <span x-text="displayAlternativeDate"></span> {% trans "from " %}
                                        <span x-text="displayAlternativeStartTime"></span> {% trans "to " %}
                                        <span x-text="displayAlternativeEndTime"></span>
                                    </p>
                                    <p class="text-sm text-gray-600 mt-2 mb-2">{% trans "Awaiting confirmation..." %}</p>
                                </div>
                            </div>
                        </template>

                        <!-- Expanded Step 2 (First Time Setting Dates) -->
                        <template x-if="step === 2 && !hasSentDates && !skippedInterview">
                            <div>
                                <div x-show="(userRole === 5 || userRole === 1) && userCompanyType === 'customer' || userCompanyType === 'global' || userCompanyType === 'vendor'">
                                    <p class="text-sm text-gray-600 mb-4 flex align-start">{% trans "Propose one or two interview dates and times to the supplier." %}</p>
                                    <div class="grid grid-cols-3 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Date 1" %}</label>
                                            <input type="date" x-model="selectedDate1" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Start Time" %}</label>
                                            <input type="time" x-model="selectedTimeStart1" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "End Time" %}</label>
                                            <input type="time" x-model="selectedTimeEnd1" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                    </div>
                                
                                    <div class="grid grid-cols-3 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Date 2" %}</label>
                                            <input type="date" x-model="selectedDate2" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Start Time" %}</label>
                                            <input type="time" x-model="selectedTimeStart2" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "End Time" %}</label>
                                            <input type="time" x-model="selectedTimeEnd2" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Contact Person Name" %}</label>
                                            <input type="text" x-model="contactPersonName" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Contact Person Phone" %}</label>
                                            <input type="number" x-model="contactPersonPhone" class="w-full border-gray-300 rounded-lg py-2 px-3">
                                        </div>
                                    </div>
                                    
                                    <div class="flex justify-start">
                                        <button @click="sendInterviewProposal(); showOptions = true" class="bg-accent-500 hover:bg-accent-700 text-white font-bold py-2 px-4 rounded mr-4">{% trans "Send" %}</button>
                                        <button
                                            @click="
                                                step = 3;
                                                skippedInterview = true;
                                                fetch(`/jobad-application/${applicationData.id}/skip-interview/`, {
                                                    method: 'POST',
                                                    headers: {
                                                        'X-CSRFToken': '{{ csrf_token }}',
                                                        'Content-Type': 'application/json',
                                                    },
                                                    body: JSON.stringify({
                                                        interview_skipped: true
                                                    })
                                                })
                                                .then(response => response.json())
                                                .then(data => {
                                                    if (data.success) {
                                                    } else {
                                                        alert('Failed to skip the interview.');
                                                    }
                                                })
                                                .catch(error => {
                                                    console.error('Error updating interview status:', error);
                                                    alert('An error occurred while skipping the interview.');
                                                });
                                            "
                                            class="border border-gray-900 hover:border-secondary-500 text-black hover:text-secondary-500 font-bold py-2 px-4 rounded mr-4"
                                        >
                                            {% trans "Skip interview" %}
                                        </button>
                                        <button @click="fetchRejectionReasons(applicationData.company_id, 1)" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded mr-4">{% trans "Reject profile" %}</button>
                                    </div>
                                </div>
                                <template x-if="userCompanyType === 'supplier' || userCompanyType === 'vendor'">
                                    <p class="text-sm text-gray-600 mb-4 flex align-start">{% trans "No interviews have been arranged yet." %}</p>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>                
                <!-- Step 3: Candidate Selection (Gray background until Step 2 is done) -->
                <div class="flex items-start mb-6">
                    <div :class="{
                        'bg-accent-500': step === 3, 
                        'bg-gray-500': step < 3, 
                        'bg-white': step > 3
                    }" 
                    class="rounded-full text-white w-5 h-5 flex items-center justify-center mr-4">
                        <span x-show="step <= 3">3</span>
                        <span x-show="step > 3">✔</span>
                    </div>
                    <div class="text-left">
                        <p :class="{
                            'text-accent-500 font-bold flex align-start': step === 3,
                            'text-gray-800 font-bold flex align-start': step > 3,
                            'text-gray-500 font-semibold flex align-start': step < 3
                        }">
                            {% trans "Candidate selection" %}
                        </p>                        
                        <template x-if="step > 3">
                            <p class="text-sm text-gray-600 flex align-start">
                                {% if applicationData.stage == "canceled" or applicationData.stage == "rejected" %}
                                    {% trans "Your candidate has been rejected" %}
                                {% else %}
                                    {% trans "Your candidate has been selected" %}
                                {% endif %}  
                            </p>
                        </template>
                        <template x-if="step === 3">
                            <div>
                                <template x-if="!waitingForApproval && !approvalStep">
                                    <!-- Initial step 3 content (setting dates) -->
                                    <div>
                                        <div x-data="{ 
                                                init() {
                                                    // On first load, set the default to 'tomorrow' + 18 months
                                                    const tomorrow = new Date();
                                                    tomorrow.setDate(tomorrow.getDate() + 1);
                                                    const yyyyMmDd = tomorrow.toISOString().split('T')[0];
                                                    this.applicationData.start_date = yyyyMmDd;
                                        
                                                    // 18 months from tomorrow
                                                    const endDate = new Date(tomorrow);
                                                    endDate.setMonth(endDate.getMonth() + 18);
                                                    this.applicationData.end_date = endDate.toISOString().split('T')[0];
                                                },
                                                recalcEndDate() {
                                                    if (!this.applicationData.start_date) return;
                                                    
                                                    // Parse the date string YYYY-MM-DD
                                                    const [year, month, day] = this.applicationData.start_date.split('-');
                                                    
                                                    // Create the date at midday (local), not midnight
                                                    const start = new Date(+year, +month - 1, +day, 12, 0, 0);
                                                    
                                                    // Add 18 months from the new start date
                                                    start.setMonth(start.getMonth() + 18);
                                                    
                                                    // Format back to YYYY-MM-DD
                                                    const yyyy = start.getFullYear();
                                                    const mm = String(start.getMonth() + 1).padStart(2, '0');
                                                    const dd = String(start.getDate()).padStart(2, '0');
                                                    
                                                    this.applicationData.end_date = `${yyyy}-${mm}-${dd}`;
                                                }
                                            }" x-init="init()">
                                            <div x-show="userRole === 5 && userCompanyType === 'customer' || userCompanyType === 'global' || userCompanyType === 'vendor'">
                                                <p class="text-sm text-gray-600 mb-4">{% trans "Select a start- and end date for the worker's contract." %}</p>
                                                <div class="grid grid-cols-2 gap-4 mb-6">
                                                    <div>
                                                        <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Begin" %}</label>
                                                        <input type="date" x-model="applicationData.start_date" @change="recalcEndDate()"
                                                            :class="errors.start_date ? 'border-red-500' : 'border-blue-100'" 
                                                            class="w-full border border-gray-300 rounded-lg py-2 px-3" required>
                                                        <p x-show="errors.start_date" class="text-red-500 text-xs mt-1">{% trans "This field is required" %}</p>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "End" %}</label>
                                                        <input type="date" x-model="applicationData.end_date" 
                                                            :class="errors.end_date ? 'border-red-500' : 'border-blue-100'" 
                                                            class="w-full border border-gray-300 rounded-lg py-2 px-3" required>
                                                        <p x-show="errors.end_date" class="text-red-500 text-xs mt-1">{% trans "This field is required" %}</p>
                                                    </div>
                                                </div>
                                                <div class="grid grid-cols-2 gap-4 mb-6">
                                                    <div>
                                                        <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Start Time" %}</label>
                                                        <input type="time" x-model="applicationData.start_time" 
                                                            :class="errors.start_time ? 'border-red-500' : 'border-blue-100'" 
                                                            class="w-full border border-gray-300 rounded-lg py-2 px-3" required>
                                                        <p x-show="errors.start_time" class="text-red-500 text-xs mt-1">{% trans "This field is required" %}</p>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Contact Person" %}</label>
                                                        <div class="flex space-x-2">
                                                            <div class="w-full">
                                                                <input type="text" x-model="applicationData.contact_person_name" 
                                                                    placeholder="Name" 
                                                                    :class="errors.contact_person_name ? 'border-red-500' : 'border-blue-100'" 
                                                                    class="w-full border border-gray-300 rounded-lg py-2 px-3" required>
                                                                <p x-show="errors.contact_person_name" class="text-red-500 text-xs mt-1">{% trans "This field is required" %}</p>
                                                            </div>
                                                            <div class="w-full">
                                                                <input type="number" x-model="applicationData.contact_person_phone" 
                                                                    placeholder="{% trans "Phone" %}" 
                                                                    :class="errors.contact_person_phone ? 'border-red-500' : 'border-blue-100'" 
                                                                    class="w-full border border-gray-300 rounded-lg py-2 px-3" required>
                                                                <p x-show="errors.contact_person_phone" class="text-red-500 text-xs mt-1">{% trans "This field is required" %}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex justify-start">
                                                    <button @click="submitSelection(applicationData.start_date, applicationData.end_date, applicationData.start_time, applicationData.contact_person_name, applicationData.contact_person_phone)" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-4">
                                                        {% trans "Accept profile" %}
                                                    </button>
                                                    <button @click="fetchRejectionReasons(applicationData.company_id, 3)" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded mr-4">{% trans "Reject profile" %}</button>
                                                </div>
                                            </div>                                        
                                            <div x-show="userCompanyType === 'supplier' || userCompanyType === 'vendor'" class="mt-4 text-left">
                                                <p class="text-sm text-gray-600 mt-2 mb-2">{% trans "Customer has not yet entered a planned start and end date..." %}</p>
                                            </div>
                                        </div>
                                    </div>
                                </template>  
                                <template x-if="step === 3 && waitingForApproval">
                                    <div class="text-center w-full">
                                        <div class="grid grid-cols-2 gap-4 rounded-lg p-4">
                                            <div class="text-left">
                                                <p class="text-sm font-semibold text-gray-700">{% trans "Start date:" %}</p>
                                                <p class="text-lg text-gray-900 font-medium" x-text="formatDate(applicationData.start_date)"></p>
                                                
                                                {% comment %} <p class="text-sm font-semibold text-gray-700 mt-2">{% trans "Contact person:" %}</p>
                                                <p class="text-lg text-gray-900 font-medium" x-text="applicationData.contact_person_name || '-'"></p> {% endcomment %}
                                    
                                                <p class="text-sm font-semibold text-gray-700 mt-2">{% trans "Start time:" %}</p>
                                                <p class="text-lg text-gray-900 font-medium" x-text="applicationData.start_time || '-'"></p>
                                            </div>
                                            <div class="text-left">
                                                <p class="text-sm font-semibold text-gray-700">{% trans "End date:" %}</p>
                                                <p class="text-lg text-gray-900 font-medium" x-text="formatDate(applicationData.end_date)"></p>
                                    
                                                {% comment %} <p class="text-sm font-semibold text-gray-700 mt-2">{% trans "Phone:" %}</p>
                                                <p class="text-lg text-gray-900 font-medium" x-text="applicationData.contact_person_phone || '-'"></p> {% endcomment %}
                                            </div>
                                        </div>
                                    
                                        <template x-if="userRole == 5 || userCompanyType === 'supplier'">
                                            <p class="text-sm text-gray-500 mt-4">{% trans "Please wait for approval." %}</p>
                                        </template>
                                    
                                        <template x-if="userRole != 5 && (userCompanyType === 'customer' || userCompanyType === 'global')">
                                            <div class="flex space-x-4">
                                                <button @click="approveCandidate()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-6 rounded">
                                                    {% trans "Approve Candidate" %}
                                                </button>
                                                <button @click="fetchRejectionReasons(applicationData.company_id, 1)" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded">
                                                    {% trans "Reject Profile" %}
                                                </button>
                                            </div>
                                        </template>                                        
                                    </div>                                    
                                </template>                                                                                                                                                   
                                <template x-if="step === 3 && approvalStep">
                                    <!-- Second part of step 3 (after profile acceptance) -->
                                    <div>
                                        <div x-show="userCompanyType === 'supplier' || userCompanyType === 'global' || userCompanyType === 'vendor'">
                                            <p class="text-1xl text-gray-600">{% trans "Candidate has been accepted. Please approve start- and end-date." %}</p>
                                            <div class="grid grid-cols-2 gap-4 mt-4 mb-4">
                                                <div>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Start date:" %} <span x-text="formatDate(applicationData.start_date)"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Contact person:" %} <span x-text="applicationData.contact_person_name"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Start time:" %} <span x-text="applicationData.start_time"></span></p>
                                                </div>
                                                <div>
                                                    
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "End date:" %} <span x-text="formatDate(applicationData.end_date)"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Phone:" %} <span x-text="applicationData.contact_person_phone"></span></p>
                                                </div>
                                            </div>                                        
                                            <div class="flex justify-center">
                                                <button @click="confirmSelection()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-4">{% trans "Confirm" %}</button>
                                                <button @click="fetchRejectionReasons(applicationData.company_id, 3)" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">{% trans "Reject profile" %}</button>
                                            </div>
                                        </div>
                                        <div x-show="userRole === 5 && userCompanyType === 'customer' || userRole === 8 && userCompanyType === 'customer'">
                                            <p class="text-1xl text-gray-600">{% trans "Candidate has been selected" %}</p>
                                            <div class="grid grid-cols-2 gap-4 mt-4 mb-4">
                                                <div>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Start date:" %} <span x-text="formatDate(applicationData.start_date)"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Contact person:" %} <span x-text="applicationData.contact_person_name"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Start time:" %} <span x-text="applicationData.start_time"></span></p>
                                                </div>
                                                <div>
                                                    
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "End date:" %} <span x-text="formatDate(applicationData.end_date)"></span></p>
                                                    <p class="text-1xl text-gray-600 mb-2">{% trans "Phone:" %} <span x-text="applicationData.contact_person_phone"></span></p>
                                                </div>
                                            </div>   
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Step 4: Confirm Start of Deployment -->
                <div class="flex items-start mb-6">
                    <div :class="{
                        'bg-accent-500': step === 4, 
                        'bg-gray-500': step < 4, 
                        'bg-white': step > 4
                    }" 
                    class="rounded-full text-white w-5 h-5 flex items-center justify-center mr-4">
                        <span x-show="step <= 4">4</span>
                        <span x-show="step > 4">✔</span>
                    </div>
                    <div>
                        <p :class="{
                            'text-accent-500 font-bold flex align-start': step === 4,
                            'text-gray-800 font-bold flex align-start': step > 4,
                            'text-gray-500 font-semibold flex align-start': step < 4
                        }">
                            {% trans "Confirm start of deployment" %}</p>
                        <template x-if="step > 4">
                            <p class="text-sm text-gray-600 flex align-start" x-text="['canceled', 'rejected'].includes(applicationData.stage) ? '{{ candidate_rejected }}' : '{{ deployment_confirmed }}'"></p>
                        </template>

                        <template x-if="step === 4">
                            <div>
                                <div x-show="userRole === 5 && userCompanyType === 'customer' || userCompanyType === 'global' || userCompanyType === 'vendor'">
                                    <p class="text-sm flex align-start text-gray-600 mb-4">{% trans "Fill in the exact start date." %}</p>
                                    <div class="grid grid-cols-2 gap-4 mb-6">
                                        <!-- Left Column (60% width) -->
                                        <div class="bg-white">
                                            <label class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Start Date" %}</label>
                                            <input type="date" x-model="applicationData.start_date" class="w-full border-gray-300 rounded-lg py-2 px-3 mb-4">
                                            
                                            <p class="text-sm text-gray-600">
                                                {% trans "Planned start date: " %}
                                                <span class="font-bold" x-text="new Date(applicationData.start_date).toLocaleDateString('de-DE')"></span>
                                            </p>
                                            <p class="text-sm text-gray-600">
                                                {% trans "Planned end date: " %}
                                                <span class="font-bold" x-text="new Date(applicationData.end_date).toLocaleDateString('de-DE')"></span>
                                            </p>
                                            <p class="text-sm text-gray-600">
                                                {% trans "Planned start time: " %}
                                                <span class="font-bold" x-text="applicationData.start_time"></span>
                                            </p>
                                        </div>
                            
                                        <!-- Right Column (Contact Person Card) -->
                                        <div class="bg-white inline" style="margin-left: 30px;">
                                            <div class="flex items-center">
                                                <i class="fas fa-user text-accent-500"></i>
                                                <input type="text" x-model="contactPersonName2" placeholder="Name" class="w-full py-2 text-gray-700 bg-white border-none" disabled>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-phone text-accent-500"></i>
                                                <input type="number" x-model="contactPersonPhone2" placeholder={% trans "Phone" %} class="w-full py-2 text-gray-700 bg-white border-none" disabled>
                                            </div>
                                        </div>
                                    </div>
                            
                                    <div class="flex justify-start mt-4">
                                        <button @click="confirmDeployment(applicationData.start_date)" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded">{% trans "Confirm" %}</button>
                                    </div>
                                </div>
                                <div x-show="userCompanyType === 'supplier' || userCompanyType === 'vendor'" class="mt-4 text-left">
                                    <p class="text-sm text-gray-600 mt-2 mb-2">{% trans "Candidate has not started the deployment yet" %}</p>
                                </div>
                            </div>
                        </template>                                                                                          
                    </div>
                </div>
                <!-- Reject Modal Layout -->
                <div x-show="openRejectModal" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
                    <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">{% trans "Reject Profile" %}</h3>
                        <p class="text-gray-700 mb-4">{% trans "Please select a reason for rejecting the profile:" %}</p>
                        
                        <!-- Dropdown for Rejection Reasons -->
                        <select x-model="selectedRejectionReason" class="w-full border-gray-300 rounded-lg py-2 px-3 mb-4">
                            <option value="">{% trans "No reason" %}</option>
                            <template x-for="reason in rejectionReasons" :key="reason.id">
                                <option :value="reason.id" x-text="reason.text"></option>
                            </template>
                        </select>
                
                        <div class="flex justify-end space-x-4">
                            <button @click="openRejectModal = false" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                                {% trans "Cancel" %}
                            </button>
                            <button @click="rejectProfile()" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                                {% trans "Confirm Rejection" %}
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Step 5: Application Finished -->
                <div class="flex items-start mb-4">
                    <div :class="(step === 5) ? 'bg-white' : 'bg-gray-500'" class="rounded-full text-white w-5 h-5 flex items-center justify-center mr-4">
                        <span x-show="step < 5">5</span>
                        <span x-show="step === 5">✔</span>
                    </div>
                    <p :class="{
                        'text-accent-500 font-bold flex align-start': step === 5,
                        'text-gray-800 font-bold flex align-start': step > 5,
                        'text-gray-500 font-semibold flex align-start': step < 5
                    }">
                        {% trans "Application finished" %}</p>
                    </div>
                    <template x-if="applicationData.stage === 'rejected'">
                        <div class="mt-2 p-2 bg-red-100 border border-red-300 rounded-lg">
                            <h3 class="text-1xl font-semibold text-red-700">{% trans "Rejection Reason" %}</h3>
                            <p class="text-sm text-gray-700 mt-2" x-text="applicationData.rejection_reason"></p>
                        </div>
                    </template>                
            </div>

            <!-- Cancel Button at the Bottom -->
            <div class="mt-6">
                <button @click="openCancelConfirmation = true" :disabled="step === 5" class="bg-gray-700 hover:bg-gray-800 text-white font-bold py-2 px-4 rounded w-full disabled:opacity-50 disabled:cursor-not-allowed">
                    {% trans "Cancel Application" %}
                </button>
            </div>
            <div x-show="openCancelConfirmation" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
                <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">{% trans "Cancel Application" %}</h3>
                    <p class="text-gray-700 mb-6">{% trans "You are about to cancel the application of this candidate. Is that right?" %}</p>
                    <div class="flex justify-end space-x-4">
                        <button @click="cancelApplication()" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            {% trans "Yes, Cancel" %}
                        </button>
                        <button @click="openCancelConfirmation = false" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                            {% trans "No, Go Back" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
