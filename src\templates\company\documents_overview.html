{% extends "_base_frontend.html" %}
{% load static %}
{% load i18n %}
{% load custom_filters %}

{% block title %}{% trans "Document Overview" %}{% endblock %}

{% block content %}
  <style>
    [x-cloak] { display: none !important; }
  </style>

  <div class="mb-6 relative flex items-center justify-center">
    <!-- Search form, centered by justify-center on the parent -->
    {% if is_admin %}
      <form
        method="get"
        action="{% url 'documents-overview' %}"
        class="flex w-full max-w-md"
      >
        <input
          type="text"
          name="q"
          value="{{ q }}"
          placeholder="{% trans 'Search companies…' %}"
          class="flex-grow px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring"
        />
        <button
          type="submit"
          class="px-4 py-2 bg-accent-500 hover:bg-secondary-500 text-white rounded-r-lg"
          title="{% trans 'Search' %}"
        >
          <i class="fas fa-search"></i>
        </button>
      </form>
    {% endif %}
  </div>


  <div x-data="{ showUpload: false, openModalId: null }" x-init="openModalId = {{ request.GET.open|default:'null' }}">
    <!-- Featured Company Section and Coming Soon Feature -->
    <div class="grid grid-cols-1 md:grid-cols-1 gap-6 mb-8 align-start justify-start">
      <!-- Featured Company Card -->
      <div>
        {% for company, docs in docs_by_company.items %}
          {% if company.id == lgi_id %}
            <div class="bg-gray-50 rounded-2xl shadow-lg p-6 flex md:flex-row w-full">
              <!-- LEFT half -->
              <div class="w-full md:w-2/5 pr-2 mb-6 md:mb-0 flex flex-col items-center text-center">
                <div class="w-14 h-14 flex items-center justify-center bg-accent-500 rounded-full shadow-md mb-4">
                  <img src="{% static 'images/lgi.png' %}" alt="LGI Logo" class="p-2 h-12 w-12">
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">{{ company.name }}</h3>
                <div class="flex items-center space-x-2 mb-4">
                  <i class="fa-solid fa-file-lines text-gray-700"></i>
                  <span class="text-gray-700">{{ docs|length }} {% trans "Documents" %}</span>
                  {% if docs|length >= 1 %}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-500 text-white">
                      <i class="fa-solid fa-file-pdf"></i>
                    </span>
                  {% endif %}
                </div>
                <button
                  @click="openModalId = {{ company.pk }}"
                  class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white p-2 rounded-lg flex items-center justify-center transition"
                >
                  <i class="fas fa-folder-open mr-2"></i>
                  {% trans "View All Documents" %}
                </button>
              </div>
              
              <!-- RIGHT half -->
              <div class="w-full md:w-3/5">
                <div class="grid grid-cols-4 gap-4">
                  {% for structure, structure_docs in lgi_list %}
                    <button
                      @click="openModalId='{{ company.pk }}_{{ structure.id }}'"
                      class="flex flex-col items-center p-2 hover:bg-accent-500 hover:text-secondary-500 rounded-lg transition"
                      title="{{ structure.value }} ({{ structure_docs|length }} docs)"
                    >
                      <div class="w-8 h-8 flex items-center justify-center bg-accent-100 text-accent-600 rounded-full mb-1">
                        <i class="fa-solid fa-sitemap"></i>
                      </div>
                      <span class="text-sm text-center leading-tight">
                        {{ structure.value }}
                      </span>
                      <span class="text-xs text-gray-500 mt-1">
                        {{ structure_docs|length }} {% trans "docs" %}
                      </span>
                    </button>
                  {% endfor %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>

    <!-- Partners & Suppliers Section -->
    <div class="mt-8">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {% for company, docs in docs_by_company.items %}
          {% if company.id != lgi_id %}
            <div
              class="bg-white rounded-2xl shadow hover:shadow-lg transition p-6 cursor-pointer flex flex-col justify-between"
              @click="openModalId = {{ company.pk }}"
            >
              <h2 class="text-lg font-semibold mb-2 truncate">{{ company.name }}</h2>
              <div class="flex items-center justify-between mb-4">
                <span class="text-gray-700 font-medium">
                  {{ docs|length }} {% trans "Documents" %}
                </span>
                <div class="flex items-center space-x-2">
                  {% if docs|length >= 1 %}
                    <i class="fa-solid fa-file-word text-accent-500 text-lg"></i>
                  {% endif %}
                  {% if docs|length >= 2 %}
                    <i class="fa-solid fa-folder-open text-accent-500 text-lg"></i>
                  {% endif %}
                  {% if docs|length >= 3 %}
                    <i class="fa-regular fa-file-pdf text-accent-500 text-lg"></i>
                  {% endif %}
                  {% if docs|length > 3 %}
                    <span class="flex items-center justify-center w-6 h-6 bg-accent-500 text-white text-xs font-semibold rounded-full">
                      +{{ docs|length|add:-3 }}
                    </span>
                  {% endif %}
                </div>
              </div>
              <div class="mt-auto flex space-x-2">
                <span class="inline-block bg-accent-100 text-accent-800 text-xs px-2 py-1 rounded-full">
                  {% if is_admin %}{% trans "Admin" %}{% else %}{% trans "Self" %}{% endif %}
                </span>
                <span class="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {{ docs.0.uploaded_on|date:"d-m-Y" }}
                </span>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
    <!-- Keep all modals outside the grid -->
    {% for company, docs in docs_by_company.items %}
      <div
        x-show="openModalId === {{ company.pk }}"
        x-transition.opacity
        x-cloak
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <div
          @click.away="openModalId = null"
          class="bg-white w-11/12 md:w-3/4 lg:w-1/2 rounded-xl shadow-xl overflow-hidden flex flex-col max-h-[90vh]"
        >
          <!-- Header -->
          <div class="flex justify-between items-center px-6 py-4 border-b">
            <h3 class="text-xl font-bold">{{ company.name }} – {% trans "Document" %}</h3>
            <button @click="openModalId = null" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times text-2xl"></i>
            </button>
          </div>
          <!-- Body (scrollable) -->
          <div class="overflow-y-auto p-6 space-y-4">
            {% if docs %}
              <table class="w-full text-sm divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-2 text-left">{% trans "File Name" %}</th>
                    <th class="px-4 py-2 text-left">{% trans "Uploaded By" %}</th>
                    <th class="px-4 py-2 text-left">{% trans "Uploaded On" %}</th>
                    <th class="px-4 py-2 text-left">{% trans "Expiration Date" %}</th>
                    <th class="px-4 py-2"></th>
                  </tr>
                </thead>
                <tbody class="bg-white">
                  {% for doc in docs %}
                    <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                      <td class="px-4 py-2">{{ doc.document.name|after_hash:25 }}</td>
                      <td class="px-4 py-2">{{ doc.uploaded_by.get_full_name }}</td>
                      <td class="px-4 py-2">{{ doc.uploaded_on|date:"d-m-Y" }}</td>
                      <td class="px-4 py-2">{{ doc.expiration_date }}</td>
                      <td class="px-4 py-2 whitespace-nowrap">
                        <a href="{% url 'document_download' 'company' doc.id %}" taget="_blank" rel="noopener" class="mr-3">
                          <i class="fas fa-download"></i>
                        </a>
                        {% if is_admin %}
                          <a href="{% url 'company-document-delete' doc.id %}?next=documents&open={{ company.pk }}&q={{ q }}" class="text-red-500">
                            <i class="fas fa-trash"></i>
                          </a>
                        {% endif %}
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            {% else %}
              <p class="text-gray-500 italic">{% trans "No documents available." %}</p>
            {% endif %}
          </div>
          <!-- Footer (upload button for admin) -->
          {% if is_admin %}
          <div class="px-6 py-3 flex justify-end space-x-2 border-b">
            <button
              @click="showUpload = !showUpload"
              class="bg-accent-500 hover:bg-secondary-500 text-white px-4 py-2 rounded"
            >
              {% trans "Upload Document" %}
            </button>
          </div>

          <!-- Upload section (toggles) -->
          <div x-show="showUpload" class="p-6 bg-gray-50 space-y-4">
            <form
              method="post"
              enctype="multipart/form-data"
              class="flex flex-col space-y-4"
            >
              {% csrf_token %}
              <input type="hidden" name="company_id" value="{{ company.pk }}"/>
              <div class="grid flex grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block font-medium">{% trans "Select File" %}</label>
                  <input type="file" name="document" required class="mt-1 block w-full"/>
                </div>
                {% if company.id == 61 %}
                <div>
                  <label class="block font-medium">{% trans "Division" %}</label>
                  <select name="structure_id" class="mt-1 block w-full border rounded px-2 py-1">
                    <option value="">{% trans "None (Company-wide)" %}</option>
                    {% for structure, _ in lgi_list %}
                      <option value="{{ structure.id }}">{{ structure.value }}</option>
                    {% endfor %}
                  </select>
                </div>
                {% endif %}
              </div>

              <div class="flex justify-end space-x-2">
                <button
                  type="submit"
                  class="bg-accent-500 hover:bg-secondary-500 text-white px-4 py-2 rounded"
                >
                  {% trans "Save" %}
                </button>
                <button
                  type="button"
                  @click="showUpload = false"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
                >
                  {% trans "Cancel" %}
                </button>
              </div>
            </form>
          </div>
          {% endif %}
        </div>
      </div>
    {% endfor %}

    <!-- Structure-specific modals for LGI -->
    {% for company, docs in docs_by_company.items %}
      {% if company.id == 61 %}
        {% for structure, structure_docs in lgi_list %}
          <div
            x-show="openModalId === '{{ company.pk }}_{{ structure.id }}'"
            x-transition.opacity
            x-cloak
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <div
              @click.away="openModalId = null"
              class="bg-white w-11/12 md:w-3/4 lg:w-1/2 rounded-xl shadow-xl overflow-hidden flex flex-col max-h-[90vh]"
            >
              <!-- Header -->
              <div class="flex justify-between items-center px-6 py-4 border-b">
                <h3 class="text-xl font-bold">{{ structure.value }}</h3>
                <button @click="openModalId = null" class="text-gray-500 hover:text-gray-700">
                  <i class="fas fa-times text-2xl"></i>
                </button>
              </div>
              <!-- Body (scrollable) -->
              <div class="overflow-y-auto p-6 space-y-4">
                {% if structure_docs %}
                  <table class="w-full text-sm divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="px-4 py-2 text-left">{% trans "File Name" %}</th>
                        <th class="px-4 py-2 text-left">{% trans "Uploaded By" %}</th>
                        <th class="px-4 py-2 text-left">{% trans "Uploaded On" %}</th>
                        <th class="px-4 py-2 text-left">{% trans "Expiration Date" %}</th>
                        <th class="px-4 py-2"></th>
                      </tr>
                    </thead>
                    <tbody class="bg-white">
                      {% for doc in structure_docs %}
                        <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                          <td class="px-4 py-2">{{ doc.document.name|after_hash:25 }}</td>
                          <td class="px-4 py-2">{{ doc.uploaded_by.get_full_name }}</td>
                          <td class="px-4 py-2">{{ doc.uploaded_on|date:"d-m-Y" }}</td>
                          <td class="px-4 py-2">{{ doc.expiration_date }}</td>
                          <td class="px-4 py-2 whitespace-nowrap">
                            <a href="{% url 'document_download' 'company' doc.id %}" taget="_blank" rel="noopener" class="mr-3">
                              <i class="fas fa-download"></i>
                            </a>
                            {% if is_admin %}
                              <a href="{% url 'company-document-delete' doc.id %}?next=documents&open={{ company.pk }}_{{ structure.id }}&q={{ q }}" class="text-red-500">
                                <i class="fas fa-trash"></i>
                              </a>
                            {% endif %}
                          </td>
                        </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                {% else %}
                  <p class="text-gray-500 italic">{% trans "No documents available for this division." %}</p>
                {% endif %}
              </div>
              <!-- Footer (upload button for admin) -->
              {% if is_admin %}
              <div class="px-6 py-3 flex justify-end space-x-2 border-b">
                <button
                  @click="showUpload = !showUpload"
                  class="bg-accent-500 hover:bg-secondary-500 text-white px-4 py-2 rounded"
                >
                  {% trans "Upload Document" %}
                </button>
              </div>

              <!-- Upload section (toggles) -->
              <div x-show="showUpload" class="p-6 bg-gray-50 space-y-4">
                <form
                  method="post"
                  enctype="multipart/form-data"
                  class="flex flex-col space-y-4"
                >
                  {% csrf_token %}
                  <input type="hidden" name="company_id" value="{{ company.pk }}"/>
                  <input type="hidden" name="structure_id" value="{{ structure.id }}"/>
                  <div class="grid flex grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label class="block font-medium">{% trans "Select File" %}</label>
                      <input type="file" name="document" required class="mt-1 block w-full"/>
                    </div>
                  </div>

                  <div class="flex justify-end space-x-2">
                    <button
                      type="submit"
                      class="bg-accent-500 hover:bg-secondary-500 text-white px-4 py-2 rounded"
                    >
                      {% trans "Save" %}
                    </button>
                    <button
                      type="button"
                      @click="showUpload = false"
                      class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded"
                    >
                      {% trans "Cancel" %}
                    </button>
                  </div>
                </form>
              </div>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      {% endif %}
    {% endfor %}

    {% if not docs_by_company %}
      <p class="mt-12 text-center text-gray-500">{% trans "Ihr Unternehmen hat keine Dokumente" %}</p>
    {% endif %}
  </div>
{% endblock %}
