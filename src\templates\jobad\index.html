{% extends '_base_frontend.html' %}
{% load static %}
{% load i18n %}
{% load application_filters %}
{% block title %}{% trans "Job ads" %}{% endblock %}

{% block content %}
<div class="container mx-auto">
    <form id="filterForm" method="GET" action="{% url 'jobad-list' %}" class="space-y-4">
        <div class="flex items-center justify-between pb-2">
            <!-- Left Side: Filter Buttons with Count Bubbles -->
            <div class="flex items-center space-x-4 relative">
                <!-- Active Button with Count Bubble -->
                <button type="button" 
                        onclick="filterJobads('active', event)" 
                        class="relative flex items-center border border-gray-300 hover:border-gray-400 text-white font-semibold py-2 px-4 rounded-md shadow-sm filter-btn">
                    <i class="fas fa-check-circle mr-2"></i> {% trans "Active" %}
                    <span class="absolute top-0 right-0 transform translate-x-2 -translate-y-2 bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                        {{ active_count }}
                    </span>
                </button>
        
                <!-- Archived Button with Count Bubble -->
                <button type="button"
                        onclick="filterJobads('archived', event)" 
                        class="relative flex items-center bg-white border border-gray-300 hover:border-gray-400 text-gray-700 font-semibold py-2 px-4 rounded-md shadow-sm filter-btn">
                    <i class="fas fa-archive mr-2"></i> {% trans "Archived" %}
                    <span class="absolute top-0 right-0 transform translate-x-2 -translate-y-2 bg-gray-300 text-black text-xs font-bold px-2 py-0.5 rounded-full">
                        {{ archived_count }}
                    </span>
                </button>
            </div>
            <!-- Filters -->
            <div class="flex flex-wrap mb-6 items-end space-x-4">
                <!-- Company Filter -->
                <div class="flex flex-col items-center" style="width: 130px;">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center w-full">
                        {% trans "Company" %}
                    </label>
                    <div x-data="multiCustomerFilter({
                            customers: [
                                {id: '', name: '{% trans "Any" %}'},
                                {% for c in companies %}
                                    {id: '{{ c.id }}', name: '{{ c.name }}'},
                                {% endfor %}
                            ],
                            initialSelected: '{{ request.GET.company|default_if_none:"" }}'
                        })" class="relative inline-block w-full">
                        <!-- Dropdown button -->
                        <button type="button"
                                @click="open = !open"
                                class="flex items-center justify-between w-full px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm
                                    hover:bg-gray-50 text-gray-900 focus:outline-none focus:ring-2 focus:ring-accent-500"
                                style="width:130px;">
                                <i class="fa-solid fa-building text-accent-500 mr-2"></i>
                            <span class="font-medium truncate" style="max-width: 60px;" x-text="selectedLabel"></span>
                            <i class="fa-solid fa-chevron-down text-gray-400 ml-2"></i>
                        </button>
                        <!-- Dropdown list -->
                        <div x-show="open"
                            @click.outside="open = false"
                            class="absolute mt-2 w-full bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-[750px] overflow-y-auto">
                            <ul class="py-1">
                                <template x-for="cust in customers" :key="cust.id">
                                    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="toggleCustomer(cust)">
                                        <span x-text="cust.name"></span>
                                        <template x-if="selectedIds.includes(cust.id)">
                                            <i class="fa-solid fa-check float-right"></i>
                                        </template>
                                    </li>
                                </template>
                            </ul>
                            <!-- Apply Button -->
                            <div class="px-4 py-2 border-t">
                                <button type="button" @click="applySelection()"
                                        class="w-full text-center text-accent-500 font-semibold">
                                    {% trans "Apply" %}
                                </button>
                            </div>
                        </div>
                        <!-- Hidden input -->
                        <input type="hidden" name="company" :value="selectedIds.join(',')" />
                    </div>
                </div>
                <!-- Worker Group Filter -->
                <div class="flex flex-col items-center" style="width: 130px;">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center w-full">{% trans "Group" %}</label>
                    <div x-data="multiGroupFilter({
                            groups: [
                            {id: '', name: '{% trans "Any" %}'},
                            {id: 'office', name: '{% trans "Office" %}'},
                            {id: 'production', name: '{% trans "Production" %}'}
                            ],
                            initialSelected: '{{ request.GET.group|default_if_none:"" }}'
                        })" class="relative inline-block">
                        <button type="button" @click="open = !open"
                                class="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm
                                    hover:bg-gray-50 text-gray-900 focus:outline-none focus:ring-2 focus:ring-accent-500"
                                style="width: 130px;">
                            <i class="fa-solid fa-users text-accent-500"></i>
                            <span class="font-medium truncate" style="max-width: 130px;" x-text="selectedLabel"></span>
                            <i class="fa-solid fa-chevron-down text-gray-400"></i>
                        </button>
                        <div x-show="open" @click.outside="open = false"
                            class="absolute mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-[750px] overflow-y-auto">
                            <ul class="py-1">
                                <template x-for="grp in groups" :key="grp.id">
                                    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="toggleGroup(grp)">
                                        <span x-text="grp.name"></span>
                                        <template x-if="selectedIds.includes(grp.id)">
                                            <i class="fa-solid fa-check float-right"></i>
                                        </template>
                                    </li>
                                </template>
                            </ul>
                            <div class="px-4 py-2 border-t">
                                <button type="button" @click="applySelection()"
                                        class="w-full text-center text-accent-500 font-semibold">
                                    {% trans "Apply" %}
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="group" :value="selectedIds.join(',')" />
                    </div>
                </div>

                <!-- Location Filter -->
                <div class="flex flex-col items-center" style="width: 130px;">
                    <label class="block text-sm font-medium text-gray-700 mb-1 text-center w-full">{% trans "Location" %}</label>
                    <div x-data="multiLocationFilter({
                            locations: [
                            {id: '', name: '{% trans "Any" %}'},
                            {% for loc in locations %}
                                {id: '{{ loc.id }}', name: '{{ loc }}'},
                            {% endfor %}
                            ],
                            initialSelected: '{{ request.GET.location|default_if_none:"" }}'
                        })" class="relative inline-block">
                        <button type="button" @click="open = !open"
                                class="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm
                                    hover:bg-gray-50 text-gray-900 focus:outline-none focus:ring-2 focus:ring-accent-500"
                                style="width: 130px;">
                            <i class="fa-solid fa-location-dot text-accent-500"></i>
                            <span class="font-medium truncate" style="max-width: 130px;" x-text="selectedLabel"></span>
                            <i class="fa-solid fa-chevron-down text-gray-400"></i>
                        </button>
                        <div x-show="open" @click.outside="open = false"
                            class="absolute mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-[750px] overflow-y-auto">
                            <ul class="py-1">
                                <template x-for="loc in locations" :key="loc.id">
                                    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="toggleLocation(loc)">
                                        <span x-text="loc.name"></span>
                                        <template x-if="selectedIds.includes(loc.id)">
                                            <i class="fa-solid fa-check float-right"></i>
                                        </template>
                                    </li>
                                </template>
                            </ul>
                            <div class="px-4 py-2 border-t">
                                <button type="button" @click="applySelection()"
                                        class="w-full text-center text-accent-500 font-semibold">
                                    {% trans "Apply" %}
                                </button>
                            </div>
                        </div>
                        <input type="hidden" name="location" :value="selectedIds.join(',')" />
                    </div>
                </div>

                <!-- Center: Search Bar -->
                <div class="relative inline-block">
                    <div class="bg-white rounded shadow p-1 flex items-center max-w-lg w-full">
                        <input type="text"
                               name="search"
                               placeholder="{% trans 'Search jobads...' %}"
                               class="border-none h-8 px-2 w-full"
                               value="{{ search }}">
                        <button type="submit" class="ml-2 text-gray-500 mr-2">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>      
            </div>

            <!-- Right Side: Sort By and Add Job Ad Buttons -->
            <div class="flex items-center space-x-4">
                <!-- Sort By Button -->
                <button type="button" class="flex items-center bg-white border border-gray-300 hover:border-gray-400 text-gray-700 hover:text-black font-semibold py-2 px-4 rounded-md shadow-sm">
                    <i class="fas fa-sort mr-2"></i> {% trans "Sort By" %}
                </button>
                <!-- Add Job Ad Button -->
                <a href="{% url 'jobad-add' %}"
                    class="flex items-center border bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded-md shadow-sm {% if not can_add_jobad %} opacity-50 cursor-not-allowed pointer-events-none {% endif %}"
                    {% if not can_add_jobad %} tabindex="-1" aria-disabled="true" {% endif %}
                >
                    {% trans "+ Add Jobad" %}
                </a>
            </div>
        </div>        
        <input type="hidden" name="tab" value="{{ tab }}">      
    </form>
    <div class="space-y-4">
        <!-- Active Job Ads -->
        <div id="active-jobads">
            {% for jobad in jobad_list %}
                <div class="relative flex items-center justify-between p-4 bg-white border rounded-lg shadow-md hover:shadow-lg transition cursor-pointer mb-4"
                    onclick="window.location.href='{% url 'jobad-detail' jobad.pk %}'">
                    <div class="flex items-center w-full justify-between">
                        <div class="relative w-1/5">
                            <h3 class="text-lg font-bold text-black">{{ jobad.position }}</h3>
                            <p class="text-sm text-black mb-2">{% trans "STATUS:" %} {{ jobad.stage|format_stage }}</p>
                            <div class="w-full bg-accent-200 h-2 rounded">
                                <div class="bg-red-600 h-2 rounded" style="width: 0%;"></div>
                            </div>
                        </div>
                        <div class="flex space-x-6 w-3/4 items-start text-sm">
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Company" %}</h4>
                                <p class="text-sm text-black">{{ jobad.company_value }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Location" %}</h4>
                                <p class="text-sm text-black">{{ jobad.location }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Open Positions" %}</h4>
                                <p class="text-sm text-black">{{ jobad.total|subtract:jobad.occupied }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Department" %}</h4>
                                <p class="text-sm text-black">{{ jobad.department_value }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Filled Positions" %}</h4>
                                <p class="text-sm text-black">{{ jobad.occupied }} {% trans "of" %} {{ jobad.total }}</p>
                            </div>
                        </div>
                        <div class="relative" onclick="event.stopPropagation();" x-data="{ showTooltip: false }">
                            <button class="text-gray-500 hover:text-black focus:outline-none" @click="showTooltip = !showTooltip">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div x-show="showTooltip" @click.outside="showTooltip = false"
                                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20" style="display: none;">
                                <a href="{% url 'jobad-detail' jobad.pk %}?tab=candidate-profile"
                                class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                                    {% trans "Candidate Profile" %}
                                </a>
                                <a href="{% url 'jobad-detail' jobad.pk %}?tab=worker-deployment"
                                class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                                    {% trans "Worker Deployment" %}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="absolute bottom-2 right-2 text-xs text-gray-500">
                        Job Id: {{jobad.id}} |
                        {% if jobad.created_by %}
                            {% with creator=jobad.created_by.user %}
                                {{ creator.first_name }} {{ creator.last_name }} | {{ jobad.created_date|date:"d.m.Y" }}<br>
                            {% endwith %}
                        {% else %}
                             {% trans "No creator" %} | {{ jobad.created_date|date:"d.m.Y" }}<br>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
            {% if not jobad_list %}
                <p class="text-center text-gray-500">{% trans "No active job ads found" %}</p>
            {% endif %}
        </div>
    
        <!-- Archived Job Ads -->
        <div id="archived-jobads" style="display: none;">
            {% for jobad in archived_jobads %}
                <div class="relative flex items-center justify-between p-4 bg-white border rounded-lg shadow-md hover:shadow-lg transition cursor-pointer mb-4"
                    onclick="window.location.href='{% url 'jobad-detail' jobad.pk %}'">
                    <div class="flex items-center w-full justify-between">
                        <div class="relative w-1/5">
                            <h3 class="text-lg font-bold text-black">{{ jobad.position }}</h3>
                            <p class="text-sm text-black mb-2">{% trans "STATUS:" %} {{ jobad.stage|format_stage }}</p>
                            <div class="w-full bg-accent-200 h-2 rounded">
                                <div class="bg-red-600 h-2 rounded" style="width: 0%;"></div>
                            </div>
                        </div>
                        <div class="flex space-x-6 w-3/4 items-start text-sm">
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Company" %}</h4>
                                <p class="text-sm text-black">{{ jobad.company_value }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Location" %}</h4>
                                <p class="text-sm text-black">{{ jobad.location }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Open Positions" %}</h4>
                                <p class="text-sm text-black">{{ jobad.total }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Department" %}</h4>
                                <p class="text-sm text-black">{{ jobad.department_value }}</p>
                            </div>
                            <div class="w-1/5">
                                <h4 class="text-sm font-bold text-black uppercase">{% trans "Filled Positions" %}</h4>
                                <p class="text-sm text-black">{{ jobad.occupied }} {% trans "of" %} {{ jobad.total }}</p>
                            </div>
                        </div>
                        <div class="relative" onclick="event.stopPropagation();" x-data="{ showTooltip: false }">
                            <button class="text-gray-500 hover:text-black focus:outline-none" @click="showTooltip = !showTooltip">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div x-show="showTooltip" @click.outside="showTooltip = false"
                                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20" style="display: none;">
                                <a href="{% url 'jobad-detail' jobad.pk %}?tab=candidate-profile"
                                class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                                    {% trans "Candidate Profile" %}
                                </a>
                                <a href="{% url 'jobad-detail' jobad.pk %}?tab=worker-deployment"
                                class="block px-4 py-2 text-gray-800 hover:bg-gray-100">
                                    {% trans "Worker Deployment" %}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="absolute bottom-2 right-2 text-xs text-gray-500">
                        Job Id: {{ jobad.id }} |
                        {% if jobad.created_by %}
                            {% with creator=jobad.created_by.user %}
                                {{ creator.first_name }} {{ creator.last_name }} | {{ jobad.created_date|date:"d.m.Y" }}<br>
                            {% endwith %}
                        {% else %}
                            {% trans "No creator" %} | {{ jobad.created_date|date:"d.m.Y" }}<br>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
            {% if not archived_jobads %}
                <p class="text-center text-gray-500">{% trans "No archived job ads found" %}</p>
            {% endif %}
        </div>
    </div>
    
</div>

<script>
    function initializeTabFilter() {
        // Obtiene el valor actual de "tab" del input oculto (por defecto "active")
        const tabInput = document.querySelector('input[name="tab"]');
        const currentTab = tabInput ? tabInput.value : 'active';
    
        // Actualiza los estilos de todos los botones
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('bg-accent-500', 'text-white');
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
        });
        
        // Encuentra y actualiza el botón correspondiente al tab actual
        const selectedButton = document.querySelector(`.filter-btn[onclick*="${currentTab}"]`);
        if (selectedButton) {
            selectedButton.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
            selectedButton.classList.add('bg-accent-500', 'text-white');
        }
    }

    function filterJobads(filter, event) {
        // Actualiza el input oculto "tab"
        const tabInput = document.querySelector('input[name="tab"]');
        if (tabInput) {
            tabInput.value = filter;
        }
        
        // Actualiza los estilos de los botones
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('bg-accent-500', 'text-white');
            btn.classList.add('bg-white', 'text-gray-700', 'border-gray-300');
        });
        const selectedButton = document.querySelector(`.filter-btn[onclick*="${filter}"]`);
        if (selectedButton) {
            selectedButton.classList.remove('bg-white', 'text-gray-700', 'border-gray-300');
            selectedButton.classList.add('bg-accent-500', 'text-white');
        }
        
        // Obtén el valor del search
        const searchValue = document.querySelector('input[name="search"]').value.trim();
        
        // Si el search está vacío, actualiza la URL y muestra/oculta los contenedores sin submit
        if (searchValue === "") {
            const newUrl = updateQueryStringParameter(window.location.href, 'tab', filter);
            history.pushState({}, '', newUrl);
            
            const activeContainer = document.getElementById('active-jobads');
            const archivedContainer = document.getElementById('archived-jobads');
            if (filter === 'active') {
                activeContainer.style.display = 'block';
                archivedContainer.style.display = 'none';
            } else {
                activeContainer.style.display = 'none';
                archivedContainer.style.display = 'block';
            }
        } else {
            // Si hay búsqueda, se hace submit para que el servidor aplique el filtro completo
            document.getElementById('filterForm').submit();
        }
    }

    function updateQueryStringParameter(uri, key, value) {
        var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
        var separator = uri.indexOf('?') !== -1 ? "&" : "?";
        if (uri.match(re)) {
        return uri.replace(re, '$1' + key + "=" + value + '$2');
        } else {
        return uri + separator + key + "=" + value;
        }
    }

    function multiLocationFilter({ locations, initialSelected }) {
        return {
            open: false,
            locations: locations,
            selectedIds: [],
            selectedLabel: '',
            init() {
                if (initialSelected) {
                    this.selectedIds = initialSelected.split(',');
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [''];
                    }
                } else {
                    this.selectedIds = [''];
                }
                this.updateLabel();
            },
            updateLabel() {
                if (this.selectedIds.includes('') || this.selectedIds.length === 0) {
                    this.selectedLabel = this.locations[0].name;
                } else {
                    let names = this.locations.filter(l => this.selectedIds.includes(l.id))
                                              .map(l => l.name);
                    this.selectedLabel = names.join(', ');
                }
            },
            toggleLocation(loc) {
                if (loc.id === '') {
                    this.selectedIds = [''];
                } else {
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [];
                    }
                    if (this.selectedIds.includes(loc.id)) {
                        this.selectedIds = this.selectedIds.filter(id => id !== loc.id);
                        if (this.selectedIds.length === 0) {
                            this.selectedIds = [''];
                        }
                    } else {
                        this.selectedIds.push(loc.id);
                    }
                }
                this.updateLabel();
            },
            applySelection() {
                this.open = false;
                this.$nextTick(() => {
                    document.getElementById('filterForm').submit();
                });
            }
        }
    }
    
    function multiCustomerFilter({ customers, initialSelected }) {
        return {
            open: false,
            customers: customers,
            selectedIds: [],
            selectedLabel: '',
            init() {
                // If an initial value is provided, assume it's a comma‑separated string
                if (initialSelected) {
                    this.selectedIds = initialSelected.split(',');
                    // If "Any" (empty string) is among the selections, use default
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [''];
                    }
                } else {
                    this.selectedIds = [''];
                }
                this.updateLabel();
            },
            updateLabel() {
                // If "Any" is selected or nothing is selected, show the default label
                if (this.selectedIds.includes('') || this.selectedIds.length === 0) {
                    this.selectedLabel = this.customers[0].name;
                } else {
                    let names = this.customers.filter(c => this.selectedIds.includes(c.id))
                                              .map(c => c.name);
                    this.selectedLabel = names.join(', ');
                }
            },
            toggleCustomer(cust) {
                if (cust.id === '') {
                    // Selecting "Any" clears other selections
                    this.selectedIds = [''];
                } else {
                    // If "Any" is currently selected, remove it
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [];
                    }
                    // Toggle this option: remove if already selected, add if not
                    if (this.selectedIds.includes(cust.id)) {
                        this.selectedIds = this.selectedIds.filter(id => id !== cust.id);
                        if (this.selectedIds.length === 0) {
                            this.selectedIds = [''];
                        }
                    } else {
                        this.selectedIds.push(cust.id);
                    }
                }
                this.updateLabel();
            },
            applySelection() {
                this.open = false;
                // Submit the form after a tick so the hidden input is updated
                this.$nextTick(() => {
                    document.getElementById('filterForm').submit();
                });
            }
        }
    }

    function multiGroupFilter({ groups, initialSelected }) {
        return {
            open: false,
            groups: groups,
            selectedIds: [],
            selectedLabel: '',
            init() {
                if (initialSelected) {
                    this.selectedIds = initialSelected.split(',');
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [''];
                    }
                } else {
                    this.selectedIds = [''];
                }
                this.updateLabel();
            },
            updateLabel() {
                if (this.selectedIds.includes('') || this.selectedIds.length === 0) {
                    this.selectedLabel = this.groups[0].name;
                } else {
                    let names = this.groups.filter(g => this.selectedIds.includes(g.id))
                                           .map(g => g.name);
                    this.selectedLabel = names.join(', ');
                }
            },
            toggleGroup(grp) {
                if (grp.id === '') {
                    this.selectedIds = [''];
                } else {
                    if (this.selectedIds.includes('')) {
                        this.selectedIds = [];
                    }
                    if (this.selectedIds.includes(grp.id)) {
                        this.selectedIds = this.selectedIds.filter(id => id !== grp.id);
                        if (this.selectedIds.length === 0) {
                            this.selectedIds = [''];
                        }
                    } else {
                        this.selectedIds.push(grp.id);
                    }
                }
                this.updateLabel();
            },
            applySelection() {
                this.open = false;
                this.$nextTick(() => {
                    document.getElementById('filterForm').submit();
                });
            }
        }
    }
    document.addEventListener("DOMContentLoaded", function () {
        // Llama a la función de inicialización para que, al cargar la página,
        // se marquen los botones según el valor actual del parámetro "tab"
        initializeTabFilter();
    });
</script>
{% endblock %}
