"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 5.0.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
import os
from pathlib import Path
from urllib.parse import quote_plus
from django.urls import reverse_lazy
from decouple import config
from django.templatetags.static import static
from dotenv import load_dotenv
from azure.identity import ChainedTokenCredential, DefaultAzureCredential, AzureCliCredential
from azure.storage.blob import BlobServiceClient
load_dotenv()
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent
BASE_URL = "http://127.0.0.1:8000"
# CONTAINER_APP_HOSTNAME = os.getenv('CONTAINER_APP_HOSTNAME')
# if CONTAINER_APP_HOSTNAME:
#     ALLOWED_HOSTS = [CONTAINER_APP_HOSTNAME]
# else:
#     # locally allow all
ALLOWED_HOSTS = ['*']
CSRF_TRUSTED_ORIGINS = [
    'https://int-ppms.proserv-dl.de',
    'https://uat-ppms.proserv-dl.de',
    'https://prd-ppms.proserv-dl.de',
    'https://ca-eur-de-uat-ppms.thankfulsea-0ba3a4e8.westeurope.azurecontainerapps.io',
    'https://ppms.proserv-dl.de',
    'https://ca-eur-de-prd-ppms.kindflower-6867c041.westeurope.azurecontainerapps.io',
    'https://dev-ppms.proserv-dl.de',
    'http://127.0.0.1:8000',
    'http://localhost',
]

# Time to logout
SESSION_COOKIE_AGE = 1800
SESSION_SAVE_EVERY_REQUEST = True

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-w4nh7i556ezd(t&$r=blk*(79^n)r8zz1qekp1t3louwkd_u^z'

DEBUG = os.getenv("DEBUG", "True") == "True"

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        },
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Application definition

INSTALLED_APPS = [
    'unfold',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_q',
    'django_countries',
    'phonenumber_field',
    'compressor',
    'company.apps.CompanyConfig',
    'common.apps.CommonConfig',
    'jobad.apps.JobadsConfig',
    'userprofiles.apps.UserprofilesConfig',
    'import_export',
]

PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'common.middleware.LoginRequiredMiddleware', 
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.middleware.locale.LocaleMiddleware',
]

ROOT_URLCONF = 'config.urls'
LGI_COMPANY_ID = 61
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'company.context_processors.company_context',
                'company.context_processors.documents_tab_visibility',
                'company.context_processors.reports_tab_visibility',
                'userprofiles.context_processors.user_profile_context',
                "common.context_processors.language_flags",
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB'),
        'USER': os.environ.get('POSTGRES_USER'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD'),
        'HOST': os.environ.get('POSTGRES_HOST', 'localhost'),
        'PORT': os.environ.get('POSTGRES_PORT', '5435'),
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en'

LANGUAGES = [
    ('en', 'English'),
    ('de', 'Deutsch'),
]

LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = '/static/'
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]
STATIC_ROOT = BASE_DIR / 'static'
# AZURE_ACCOUNT_NAME = os.getenv("STORAGE_ACCOUNT_NAME")
# AZURE_CONTAINER = os.getenv("STORAGE_CONTAINER_NAME")
# AZURE_CUSTOM_DOMAIN = f"{AZURE_ACCOUNT_NAME}.blob.core.windows.net"
# AZURE_LOCATION = "media"
# MEDIA_URL = f"https://{AZURE_CUSTOM_DOMAIN}/{AZURE_CONTAINER}/{AZURE_LOCATION}/"

credential = ChainedTokenCredential(
    AzureCliCredential(),  # for local
    DefaultAzureCredential()  # for Azure environments
)


# DEFAULT_FILE_STORAGE = "common.storage_backends.AzureMediaStorage"

if not DEBUG:
    # production / azure
    DEFAULT_FILE_STORAGE = "common.storage_backends.AzureMediaStorage"
    AZURE_ACCOUNT_NAME = os.getenv("STORAGE_ACCOUNT_NAME")
    AZURE_CONTAINER = os.getenv("STORAGE_CONTAINER_NAME")
    AZURE_LOCATION = "media"
    AZURE_CUSTOM_DOMAIN = f"{AZURE_ACCOUNT_NAME}.blob.core.windows.net"
    MEDIA_URL = f"https://{AZURE_CUSTOM_DOMAIN}/{AZURE_CONTAINER}/media/"

    AZURE_BLOB_SERVICE_CLIENT = BlobServiceClient(
        account_url=f"https://{AZURE_ACCOUNT_NAME}.blob.core.windows.net",
        credential=credential
    )
else:
    DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
    MEDIA_ROOT = BASE_DIR / "media"
    MEDIA_URL  = "/media/"
    
# AZURE_BLOB_SERVICE_CLIENT = BlobServiceClient(
#     account_url=f"https://{AZURE_ACCOUNT_NAME}.blob.core.windows.net",
#     credential=credential
# )

# MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

COMPRESS_ROOT = STATIC_ROOT

COMPRESS_ENABLED = True

STATICFILES_FINDERS = ('compressor.finders.CompressorFinder',)

# DjangoQ
Q_CLUSTER = {
    'name': 'DjangORM',
    'workers': 1,
    'recycle': 1,
    'timeout': 30,
    'django_redis': 'default',
    'retry': 31,
    'queue_limit': 50,
    'bulk': 10,
    'orm': 'default',
    'catch_up': False,
}

# Redis Configuration
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
        #'BACKEND': 'django_redis.cache.RedisCache',
        #'LOCATION': 'redis://127.0.0.1:6379/1',
        #'OPTIONS': {
        #    'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        #}
    }
}

# Django Countries

COUNTRIES_FIRST = ['DE', 'FR', 'IT', 'ES', 'GB', 'TR', 'KN', 'LC']
COUNTRIES_FIRST_BREAK = '----------------'

# Django Unfold

UNFOLD = {
    "SITE_TITLE": "PPMS",
    "SITE_HEADER": "PPMS Admin",
    "SITE_URL": "/admin",
    "SITE_ICON": lambda request: static("images/icon_admin.svg"),  # single icon for all modes
    "SITE_LOGO": lambda request: static("images/logo_admin.svg"),  # single logo for all modes
    "SITE_FAVICONS": [
        {
            "rel": "icon",
            "sizes": "32x32",
            "type": "image/svg+xml",
            "href": lambda request: static("images/favicon.png"),
        },
    ],
    "SHOW_HISTORY": True,  # show/hide "History" button, default: True
    "SHOW_VIEW_ON_SITE": True,  # show/hide "View on site" button, default: True
    "ENVIRONMENT": None,  # No custom environment callback
    "DASHBOARD_CALLBACK": None,  # No custom dashboard callback
    "THEME": "light",  # Force theme: "dark" or "light". Will disable theme switcher
    "LOGIN": {
        "redirect_after": lambda request: reverse_lazy("admin:index"),
    },
    "COLORS": {
        "primary": {
            "50": "250 245 255",
            "100": "243 232 255",
            "200": "233 213 255",
            "300": "216 180 254",
            "400": "192 132 252",
            "500": "168 85 247",
            "600": "147 51 234",
            "700": "126 34 206",
            "800": "107 33 168",
            "900": "88 28 135",
            "950": "59 7 100",
        },
    },
    "SIDEBAR": {
        "show_search": True,  # Search in applications and models names
        "show_all_applications": True,  # Dropdown with all applications and models
    },
}

LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/login/'
LOGIN_URL = '/login/'
EMAIL_API_URL = config("EMAIL_API_URL")
EMAIL_AUTH_KEY = config("EMAIL_AUTH_KEY")
EMAIL_SENDER = config("EMAIL_SENDER")
EMAIL_PASSWORD = config("EMAIL_PASSWORD")


# Redis settings
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost:6379')  # Default to 'localhost'
REDIS_PORT = os.getenv('REDIS_PORT', 6379)
REDIS_DB = os.getenv('REDIS_DB', '0')  # Default to 0
# Celery settings
# CELERY_BROKER_URL = REDIS_HOST
# CELERY_RESULT_BACKEND = REDIS_HOST
CELERY_BROKER_URL = f'redis://{REDIS_HOST}/{REDIS_DB}'
CELERY_RESULT_BACKEND = f'redis://{REDIS_HOST}/{REDIS_DB}'

CELERY_ACCEPT_CONTENT = ['json']  # Accept JSON for task serialization
CELERY_TASK_SERIALIZER = 'json'  # Serialize tasks as JSON
CELERY_TIMEZONE = TIME_ZONE
