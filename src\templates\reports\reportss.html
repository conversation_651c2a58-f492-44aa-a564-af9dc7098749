{% extends "_base_frontend.html" %}

{% block title %}Reaktionszeiten{% endblock %}

{% block custom_header %}
  <style>
    .chart-container {
      max-height: calc(100vh - var(--y-offset));
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      background-color: white;
      padding: 1rem;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      overflow-y: auto;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="flex items-center mb-4 space-x-4">
    <a href="{% url 'reports' %}" class="inline-flex items-center text-gray-700 hover:text-gray-900">
      <i class="fa-solid fa-arrow-left mr-2"></i> Zurück zur Übersicht
    </a>
  </div>
  <div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold mb-4">Reaction Time Report</h1>
    <a href="{% url 'reports-reaction-time-excel' %}"
      class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-lg text-green-600 hover:bg-gray-50">
      <i class="fa-solid fa-file-excel mr-2"></i>Excel Export
    </a>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="chart-container">
      <h2 class="text-lg font-semibold mb-2">Tage bis Jobbeginn</h2>
      <canvas id="toStartChart"></canvas>
    </div>

    <div class="chart-container">
      <h2 class="text-lg font-semibold mb-2">Tage bis erste Bewerbung</h2>
      <canvas id="toFirstAppChart"></canvas>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    (function() {
      // 1) Labels: “<JobAd.id> – <Position.Title>”
      const labels = [
        {% for ja in jobads %}
          "{{ ja.id }} – {{ ja.position.title }}",
        {% endfor %}
      ];

      // 2) Tage bis Jobbeginn = ja.days_to_start.days
      const toStartDays = [
        {% for ja in jobads %}
          {{ ja.days_to_start.days }},
        {% endfor %}
      ];

      // 3) Tage bis erste Bewerbung = ja.days_to_first_app.days
      const toFirstAppDays = [
        {% for ja in jobads %}
          {{ ja.days_to_first_app.days }},
        {% endfor %}
      ];

      // Gemeinsame Optionen für horizontale Balkendiagramme
      const chartOptions = {
        responsive: true,
        maintainAspectRatio: true,
        indexAxis: 'y',  // horizontale Balken
        scales: {
          x: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Anzahl Tage'
            }
          },
          y: {
            ticks: {
              autoSkip: false,
              font: { size: 10 }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return context.parsed.x + ' Tage';
              }
            }
          }
        }
      };

      // Linkes Chart: “Tage bis Jobbeginn”
      const ctxStart = document
        .getElementById('toStartChart')
        .getContext('2d');

      new Chart(ctxStart, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Tage bis Jobbeginn',
            data: toStartDays,
            backgroundColor: 'rgba(75, 192, 192, 0.6)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: chartOptions
      });

      // Rechtes Chart: “Tage bis erste Bewerbung”
      const ctxFirst = document
        .getElementById('toFirstAppChart')
        .getContext('2d');

      new Chart(ctxFirst, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Tage bis erste Bewerbung',
            data: toFirstAppDays,
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: chartOptions
      });
    })();
  </script>
{% endblock %}
