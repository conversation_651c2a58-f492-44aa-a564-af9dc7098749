# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-06-17 13:22+0200\n"
"PO-Revision-Date: 2024-12-16 13:37+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"

#: .\venv\Lib\site-packages\click\_termui_impl.py:556
#, python-brace-format
msgid "{editor}: Editing failed"
msgstr ""

#: .\venv\Lib\site-packages\click\_termui_impl.py:560
#, python-brace-format
msgid "{editor}: Editing failed: {e}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1124
msgid "Aborted!"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1349
#: .\venv\Lib\site-packages\click\core.py:1379
#, python-brace-format
msgid "(Deprecated) {text}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1396
#, fuzzy
#| msgid "Open Positions"
msgid "Options"
msgstr "Offene Stellen"

#: .\venv\Lib\site-packages\click\core.py:1422
#, python-brace-format
msgid "Got unexpected extra argument ({args})"
msgid_plural "Got unexpected extra arguments ({args})"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\core.py:1438
msgid "DeprecationWarning: The command {name!r} is deprecated."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1645
#, fuzzy
#| msgid "Comment"
msgid "Commands"
msgstr "Kommentar"

#: .\venv\Lib\site-packages\click\core.py:1677
msgid "Missing command."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1755
msgid "No such command {name!r}."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2313
#, fuzzy
#| msgid "Value must be valid JSON."
msgid "Value must be an iterable."
msgstr "Der Wert muss gültiges JSON sein."

#: .\venv\Lib\site-packages\click\core.py:2334
#, python-brace-format
msgid "Takes {nargs} values but 1 was given."
msgid_plural "Takes {nargs} values but {len} were given."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\core.py:2783
#, python-brace-format
msgid "env var: {var}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2813
msgid "(dynamic)"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2828
#, python-brace-format
msgid "default: {default}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2841
#, fuzzy
#| msgid "Requirements"
msgid "required"
msgstr "Anforderungen"

#: .\venv\Lib\site-packages\click\decorators.py:457
#, python-format
msgid "%(prog)s, version %(version)s"
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:520
msgid "Show the version and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:541
msgid "Show this message and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:49
#: .\venv\Lib\site-packages\click\exceptions.py:88
#, python-brace-format
msgid "Error: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:80
#, python-brace-format
msgid "Try '{command} {option}' for help."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:129
#, python-brace-format
msgid "Invalid value: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:131
#, python-brace-format
msgid "Invalid value for {param_hint}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:187
msgid "Missing argument"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:189
#, fuzzy
#| msgid "Existing Connections"
msgid "Missing option"
msgstr "Vorhandene Verbindungen"

#: .\venv\Lib\site-packages\click\exceptions.py:191
msgid "Missing parameter"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:193
#, python-brace-format
msgid "Missing {param_type}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:200
#, python-brace-format
msgid "Missing parameter: {param_name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:220
#, python-brace-format
msgid "No such option: {name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:232
#, python-brace-format
msgid "Did you mean {possibility}?"
msgid_plural "(Possible options: {possibilities})"
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\exceptions.py:270
#, fuzzy
#| msgid "Unknown"
msgid "unknown error"
msgstr "Unbekannt"

#: .\venv\Lib\site-packages\click\exceptions.py:277
msgid "Could not open file {filename!r}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\formatting.py:156
msgid "Usage:"
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:233
msgid "Argument {name!r} takes {nargs} values."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:415
msgid "Option {name!r} does not take a value."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:476
msgid "Option {name!r} requires an argument."
msgid_plural "Option {name!r} requires {nargs} arguments."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\shell_completion.py:326
msgid "Shell completion is not supported for Bash versions older than 4.4."
msgstr ""

#: .\venv\Lib\site-packages\click\shell_completion.py:333
msgid "Couldn't detect Bash version, shell completion is not supported."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:158
#, fuzzy
#| msgid "Awaiting confirmation..."
msgid "Repeat for confirmation"
msgstr "Warten auf Bestätigung..."

#: .\venv\Lib\site-packages\click\termui.py:174
msgid "Error: The value you entered was invalid."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:176
#, python-brace-format
msgid "Error: {e.message}"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:187
msgid "Error: The two entered values do not match."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:243
msgid "Error: invalid input"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:773
msgid "Press any key to continue..."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:267
#, python-brace-format
msgid ""
"Choose from:\n"
"\t{choices}"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:299
#, fuzzy
#| msgid "Value %(value)r is not a valid choice."
msgid "{value!r} is not {choice}."
msgid_plural "{value!r} is not one of {choices}."
msgstr[0] "Wert %(value)r ist keine gültige Auswahl."
msgstr[1] "Wert %(value)r ist keine gültige Auswahl."

#: .\venv\Lib\site-packages\click\types.py:393
msgid "{value!r} does not match the format {format}."
msgid_plural "{value!r} does not match the formats {formats}."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\click\types.py:415
#, fuzzy
#| msgid "Value %(value)r is not a valid choice."
msgid "{value!r} is not a valid {number_type}."
msgstr "Wert %(value)r ist keine gültige Auswahl."

#: .\venv\Lib\site-packages\click\types.py:471
#, python-brace-format
msgid "{value} is not in the range {range}."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:612
#, fuzzy
#| msgid "Value %(value)r is not a valid choice."
msgid "{value!r} is not a valid boolean."
msgstr "Wert %(value)r ist keine gültige Auswahl."

#: .\venv\Lib\site-packages\click\types.py:636
#, fuzzy
#| msgid "“%(value)s” is not a valid UUID."
msgid "{value!r} is not a valid UUID."
msgstr "“%(value)s” ist keine gültige UUID."

#: .\venv\Lib\site-packages\click\types.py:826
msgid "file"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:828
#, fuzzy
#| msgid "History"
msgid "directory"
msgstr "Verlauf"

#: .\venv\Lib\site-packages\click\types.py:830
#, fuzzy
#| msgid "File path"
msgid "path"
msgstr "Dateipfad"

#: .\venv\Lib\site-packages\click\types.py:881
msgid "{name} {filename!r} does not exist."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:890
msgid "{name} {filename!r} is a file."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:898
msgid "{name} {filename!r} is a directory."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:907
msgid "{name} {filename!r} is not readable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:916
msgid "{name} {filename!r} is not writable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:925
msgid "{name} {filename!r} is not executable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:992
#, python-brace-format
msgid "{len_type} values are required, but {len_value} was given."
msgid_plural "{len_type} values are required, but {len_value} were given."
msgstr[0] ""
msgstr[1] ""

#: .\venv\Lib\site-packages\django\contrib\messages\apps.py:15
msgid "Messages"
msgstr "Nachrichten"

#: .\venv\Lib\site-packages\django\contrib\sitemaps\apps.py:8
msgid "Site Maps"
msgstr "Site Maps"

#: .\venv\Lib\site-packages\django\contrib\staticfiles\apps.py:9
msgid "Static Files"
msgstr "Statische Dateien"

#: .\venv\Lib\site-packages\django\contrib\syndication\apps.py:7
msgid "Syndication"
msgstr "Syndizierung"

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: .\venv\Lib\site-packages\django\core\paginator.py:30
msgid "…"
msgstr "..."

#: .\venv\Lib\site-packages\django\core\paginator.py:50
msgid "That page number is not an integer"
msgstr "Diese Seitenzahl ist keine ganze Zahl"

#: .\venv\Lib\site-packages\django\core\paginator.py:52
msgid "That page number is less than 1"
msgstr "Diese Seitenzahl ist weniger als 1"

#: .\venv\Lib\site-packages\django\core\paginator.py:54
msgid "That page contains no results"
msgstr "Diese Seite enthält keine Ergebnisse"

#: .\venv\Lib\site-packages\django\core\validators.py:22
msgid "Enter a valid value."
msgstr "Geben Sie einen gültigen Wert ein."

#: .\venv\Lib\site-packages\django\core\validators.py:104
#: .\venv\Lib\site-packages\django\forms\fields.py:752
msgid "Enter a valid URL."
msgstr "Geben Sie eine gültige URL ein."

#: .\venv\Lib\site-packages\django\core\validators.py:165
msgid "Enter a valid integer."
msgstr "Geben Sie eine gültige ganze Zahl ein."

#: .\venv\Lib\site-packages\django\core\validators.py:176
msgid "Enter a valid email address."
msgstr "Bitte gib eine gültige E-Mail Adresse ein."

#. Translators: "letters" means latin letters: a-z and A-Z.
#: .\venv\Lib\site-packages\django\core\validators.py:259
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""
"Geben Sie ein gültiges URL-Kürzel ein, das aus Buchstaben, Zahlen, "
"Unterstrichen oder Bindestrichen besteht."

#: .\venv\Lib\site-packages\django\core\validators.py:267
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""
"Gib ein gültiges URL-Kürzel ein, das aus Unicode-Buchstaben, Zahlen, "
"Unterstrichen oder Bindestrichen besteht."

#: .\venv\Lib\site-packages\django\core\validators.py:279
#: .\venv\Lib\site-packages\django\core\validators.py:287
#: .\venv\Lib\site-packages\django\core\validators.py:316
msgid "Enter a valid IPv4 address."
msgstr "Gib eine gültige IPv4-Adresse ein."

#: .\venv\Lib\site-packages\django\core\validators.py:296
#: .\venv\Lib\site-packages\django\core\validators.py:317
msgid "Enter a valid IPv6 address."
msgstr "Geben Sie eine gültige IPv6-Adresse ein."

#: .\venv\Lib\site-packages\django\core\validators.py:308
#: .\venv\Lib\site-packages\django\core\validators.py:315
msgid "Enter a valid IPv4 or IPv6 address."
msgstr "Gib eine gültige IPv4- oder IPv6-Adresse ein."

#: .\venv\Lib\site-packages\django\core\validators.py:351
msgid "Enter only digits separated by commas."
msgstr "Geben Sie nur Zahlen, durch Komma getrennten sein können, ein."

#: .\venv\Lib\site-packages\django\core\validators.py:357
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""
"Stellen Sie sicher das der Wert %(limit_value)s ist (aktueller Wert "
"%(show_value)s)."

#: .\venv\Lib\site-packages\django\core\validators.py:392
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""
"Stellen sie sicher das der Wert kleiner gleich oder gleich %(limit_value)s "
"ist."

#: .\venv\Lib\site-packages\django\core\validators.py:401
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""
"Stellen sie sicher das der Wert grösser gleich oder gleich %(limit_value)s "
"ist."

#: .\venv\Lib\site-packages\django\core\validators.py:410
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr ""
"Stelle sicher, dass dieser Wert ein Vielfaches der Schrittweite "
"%(limit_value)s ist."

#: .\venv\Lib\site-packages\django\core\validators.py:420
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
"Stellen Sie sicher, dass dieser Wert mindestens %(limit_value)d Zeichen hat "
"(aktuell hat er %(show_value)d Zeichen)."
msgstr[1] ""
"Stellen Sie sicher, dass dieser Wert mindestens %(limit_value)d Zeichen hat "
"(aktuell hat er %(show_value)d Zeichen)."

#: .\venv\Lib\site-packages\django\core\validators.py:438
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""
"Stellen Sie sicher, dass dieser Wert höchstens %(limit_value)d Zeichen hat "
"(aktuell hat er %(show_value)d Zeichen)."
msgstr[1] ""
"Stellen Sie sicher, dass dieser Wert höchstens %(limit_value)d Zeichen hat "
"(aktuell hat er %(show_value)d Zeichen)."

#: .\venv\Lib\site-packages\django\core\validators.py:461
#: .\venv\Lib\site-packages\django\forms\fields.py:347
#: .\venv\Lib\site-packages\django\forms\fields.py:386
msgid "Enter a number."
msgstr "Geben Sie eine Zahl ein."

#: .\venv\Lib\site-packages\django\core\validators.py:463
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""
"Achten Sie darauf, dass insgesamt nicht mehr als %(max)s Ziffer vorhanden "
"ist."
msgstr[1] ""
"Achte darauf, dass insgesamt nicht mehr als %(max)s Ziffern vorhanden sind."

#: .\venv\Lib\site-packages\django\core\validators.py:468
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""
"Stellen Sie sicher, dass nicht mehr als %(max)s Dezimalstelle vorhanden sind."
msgstr[1] ""
"Stellen Sie sicher, dass nicht mehr als %(max)s Dezimalstellen vorhanden "
"sind."

#: .\venv\Lib\site-packages\django\core\validators.py:473
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""
"Stellen Sie sicher, dass nicht mehr als %(max)s Ziffer vor dem Dezimalpunkt "
"stehen."
msgstr[1] ""
"Stelle sicher, dass nicht mehr als %(max)s Ziffer vor dem Dezimalpunkt "
"stehen."

#: .\venv\Lib\site-packages\django\core\validators.py:544
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""
"Die Dateierweiterung \"%(extension)s\" ist nicht erlaubt. Erlaubte "
"Erweiterungen sind: %(allowed_extensions)s."

#: .\venv\Lib\site-packages\django\core\validators.py:605
msgid "Null characters are not allowed."
msgstr "Null-Zeichen sind nicht erlaubt."

#: .\venv\Lib\site-packages\django\db\models\base.py:1423
#: .\venv\Lib\site-packages\django\forms\models.py:893
#: .\venv\Lib\site-packages\unfold\contrib\inlines\admin.py:103
msgid "and"
msgstr "und"

#: .\venv\Lib\site-packages\django\db\models\base.py:1425
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr "%(model_name)s mit diesem %(field_labels)s existiert bereits."

#: .\venv\Lib\site-packages\django\db\models\constraints.py:17
#, fuzzy, python-format
msgid "Constraint “%(name)s” is violated."
msgstr "Die Einschränkung \"%(name)s\" wurde verletzt."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:128
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr "Wert %(value)r ist keine gültige Auswahl."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:129
msgid "This field cannot be null."
msgstr "Dieses Feld kann nicht null sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:130
msgid "This field cannot be blank."
msgstr "Dieses Feld kann nicht leer sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:131
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr "%(model_name)s mit diesem %(field_label)s existiert bereits."

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:135
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""
"%(field_label)s muss für %(date_field_label)s %(lookup_type)s eindeutig sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:173
#, python-format
msgid "Field of type: %(field_type)s"
msgstr "Feld vom Type: %(field_type)s"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1094
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr "Der Wert “%(value)s” muss entweder True oder False sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1095
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr "Der “%(value)s”-Wert muss entweder True, False oder None sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1097
msgid "Boolean (Either True or False)"
msgstr "Boolescher Ausdruck (entweder True oder False)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1147
#, python-format
msgid "String (up to %(max_length)s)"
msgstr "String (bis zu%(max_length)s)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1149
#, fuzzy
msgid "String (unlimited)"
msgstr "String (unbegrenzt)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1253
msgid "Comma-separated integers"
msgstr "Komma-getrennte Ganzzahlen"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1354
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""
"Der Wert “%(value)s” hat ein ungültiges Datumsformat. Er muss im Format JJJJ-"
"MM-TT vorliegen."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1358
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1493
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""
"Der Wert “%(value)s” hat das richtige Format (JJJJ-MM-TT), aber es ist ein "
"ungültiges Datum."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1362
msgid "Date (without time)"
msgstr "Datum (ohne Uhrzeit)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1489
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD "
"HH:MM[:ss[.uuuuuu]][TZ] format."
msgstr ""
"Der Wert “%(value)s” hat ein ungültiges Format. Er muss im Format JJJJ-MM-TT "
"HH:MM[:ss[.uuuuuuu]][TZ] vorliegen."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1497
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""
"Der Wert “%(value)s” hat das richtige Format (JJJJ-MM-TT HH:MM[:ss[.uuuuuuu]]"
"[TZ]), aber es handelt sich um ein ungültiges Datum/Uhrzeit."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1502
msgid "Date (with time)"
msgstr "Datum (mit Zeit)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1626
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr "Der Wert “%(value)s” muss eine Dezimalzahl sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1628
msgid "Decimal number"
msgstr "Dezimalzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1789
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] "
"[[HH:]MM:]ss[.uuuuuu] format."
msgstr ""
"Der Wert “%(value)s” hat ein ungültiges Format. Er muss im Format [DD] "
"[[HH:]MM:]ss[.uuuuuuu] vorliegen."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1793
msgid "Duration"
msgstr "Dauer"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1845
msgid "Email address"
msgstr "E-Mail-Adresse"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1870
msgid "File path"
msgstr "Dateipfad"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1948
#, python-format
msgid "“%(value)s” value must be a float."
msgstr "Der Wert “%(value)s” muss ein Fließkommazahl sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1950
msgid "Floating point number"
msgstr "Fließkommazahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1990
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr "Der Wert “%(value)s” muss eine Ganzzahl sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1992
msgid "Integer"
msgstr "Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2088
msgid "Big (8 byte) integer"
msgstr "Große (8 Byte) Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2105
msgid "Small integer"
msgstr "Kleine Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2113
msgid "IPv4 address"
msgstr "IPv4-Adresse"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2144
msgid "IP address"
msgstr "IP-Adresse"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2237
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2238
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr "Der “%(value)s”-Wert muss entweder None, True oder False sein."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2240
msgid "Boolean (Either True, False or None)"
msgstr "Boolescher Ausdruck (entweder True, False oder None)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2291
msgid "Positive big integer"
msgstr "Positive grosse Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2306
msgid "Positive integer"
msgstr "Positive Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2321
msgid "Positive small integer"
msgstr "Positive kleine Ganzzahl"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2337
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr "Slug (bis zu %(max_length)s)"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2373
msgid "Text"
msgstr "Text"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2448
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""
"Der Wert “%(value)s” hat ein ungültiges Format. Er muss im Format "
"HH:MM[:ss[.uuuuuuu]] vorliegen."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2452
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""
"Der Wert “%(value)s” hat das richtige Format (HH:MM[:ss[.uuuuuuu]]), aber es "
"handelt sich um eine ungültige Zeit."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2456
#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:174
#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:190
#: .\venv\Lib\site-packages\unfold\widgets.py:460
msgid "Time"
msgstr "Zeit"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2564
msgid "URL"
msgstr "URL"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2588
msgid "Raw binary data"
msgstr "Binäre Rohdaten"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2653
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr "“%(value)s” ist keine gültige UUID."

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2655
msgid "Universally unique identifier"
msgstr "Universell eindeutiger Identifikator"

#: .\venv\Lib\site-packages\django\db\models\fields\files.py:232
msgid "File"
msgstr "Datei"

#: .\venv\Lib\site-packages\django\db\models\fields\files.py:393
msgid "Image"
msgstr "Bild"

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:26
msgid "A JSON object"
msgstr "Ein JSON Objekt"

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:28
msgid "Value must be valid JSON."
msgstr "Der Wert muss gültiges JSON sein."

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:919
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr "%(model)s Instanz mit %(field)s und %(value)r existiert nicht."

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:921
msgid "Foreign Key (type determined by related field)"
msgstr "Fremdschlüssel (Typ wird durch das Bezugsfeld bestimmt)"

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1212
msgid "One-to-one relationship"
msgstr "Eins-zu-Eins-Beziehung"

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1269
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr "%(from)s - %(to)s Beziehung"

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1271
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr "%(from)s - %(to)s Beziehungen"

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1319
msgid "Many-to-many relationship"
msgstr "Viel-zu-Viel-Beziehung"

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: .\venv\Lib\site-packages\django\forms\boundfield.py:184
msgid ":?.!"
msgstr ":?.!"

#: .\venv\Lib\site-packages\django\forms\fields.py:91
msgid "This field is required."
msgstr "Dies ist ein Pflichtfeld."

#: .\venv\Lib\site-packages\django\forms\fields.py:298
msgid "Enter a whole number."
msgstr "Geben Sie eine ganze Zahl ein."

#: .\venv\Lib\site-packages\django\forms\fields.py:467
#: .\venv\Lib\site-packages\django\forms\fields.py:1241
msgid "Enter a valid date."
msgstr "Gültiges Datum eingeben."

#: .\venv\Lib\site-packages\django\forms\fields.py:490
#: .\venv\Lib\site-packages\django\forms\fields.py:1242
msgid "Enter a valid time."
msgstr "Geben Sie eine gültige Uhrzeit an."

#: .\venv\Lib\site-packages\django\forms\fields.py:517
msgid "Enter a valid date/time."
msgstr "Geben Sie ein gültiges Datum/Uhrzeit ein."

#: .\venv\Lib\site-packages\django\forms\fields.py:551
msgid "Enter a valid duration."
msgstr "Gültige Dauer eingeben."

#: .\venv\Lib\site-packages\django\forms\fields.py:552
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr "Die Anzahl der Tage muss zwischen {min_days} und {max_days} liegen."

#: .\venv\Lib\site-packages\django\forms\fields.py:621
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""
"Es wurde keine Datei übermittelt. Überprüfen Sie die Kodierungsart im "
"Formular."

#: .\venv\Lib\site-packages\django\forms\fields.py:622
msgid "No file was submitted."
msgstr "Es wurde keine Datei übermittelt."

#: .\venv\Lib\site-packages\django\forms\fields.py:623
msgid "The submitted file is empty."
msgstr "Die übermittelte Datei ist leer."

#: .\venv\Lib\site-packages\django\forms\fields.py:625
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""
"Stellen Sie sicher, dass dieser Dateiname höchstens %(max)d Zeichen hat "
"(aktuell hat er %(length)d Zeichen)."
msgstr[1] ""
"Stellen Sie sicher, dass dieser Dateiname höchstens %(max)d Zeichen hat "
"(aktuell hat er %(length)d Zeichen)."

#: .\venv\Lib\site-packages\django\forms\fields.py:630
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""
"Bitte übermitteln Sie entweder eine Datei ein oder kreuzen Sie die "
"Zurücksetzen-Checkbox an, nicht beides."

#: .\venv\Lib\site-packages\django\forms\fields.py:694
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""
"Laden Sie ein gültiges Bild hoch. Die von Ihnen hochgeladene Datei war "
"entweder kein Bild oder ein beschädigtes Bild."

#: .\venv\Lib\site-packages\django\forms\fields.py:857
#: .\venv\Lib\site-packages\django\forms\fields.py:949
#: .\venv\Lib\site-packages\django\forms\models.py:1566
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""
"Machen Sie eine gültige Auswahl. “%(value)s” gehört nicht zu den verfügbaren "
"Auswahlmöglichkeiten."

#: .\venv\Lib\site-packages\django\forms\fields.py:951
#: .\venv\Lib\site-packages\django\forms\fields.py:1070
#: .\venv\Lib\site-packages\django\forms\models.py:1564
msgid "Enter a list of values."
msgstr "Geben Sie eine Liste von Werten ein."

#: .\venv\Lib\site-packages\django\forms\fields.py:1071
msgid "Enter a complete value."
msgstr "Geben Sie einen vollständigen Wert ein."

#: .\venv\Lib\site-packages\django\forms\fields.py:1310
msgid "Enter a valid UUID."
msgstr "Geben Sie eine gültige UUID ein."

#: .\venv\Lib\site-packages\django\forms\fields.py:1340
msgid "Enter a valid JSON."
msgstr "Geben Sie gültiges JSON ein."

#. Translators: This is the default suffix added to form field labels
#: .\venv\Lib\site-packages\django\forms\forms.py:98
msgid ":"
msgstr ":"

#: .\venv\Lib\site-packages\django\forms\forms.py:244
#: .\venv\Lib\site-packages\django\forms\forms.py:328
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr "(Verstecktes Feld %(name)s) %(error)s"

#: .\venv\Lib\site-packages\django\forms\formsets.py:63
#, fuzzy, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""
"ManagementForm-Daten fehlen oder wurden manipuliert. Fehlende Felder: "
"%(field_names)s. Möglicherweise müssen Sie einen Fehlerbericht einreichen, "
"wenn das Problem weiterhin besteht."

#: .\venv\Lib\site-packages\django\forms\formsets.py:67
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] "Bitte reiche höchstens %(num)d Formular ein."
msgstr[1] "Bitte reiche höchstens %(num)d Formulare ein."

#: .\venv\Lib\site-packages\django\forms\formsets.py:72
#, fuzzy, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] "Bitte reichen Sie mindestens %(num)d Formular ein."
msgstr[1] "Bitte reichen Sie mindestens %(num)d Formulare ein."

#: .\venv\Lib\site-packages\django\forms\formsets.py:484
#: .\venv\Lib\site-packages\django\forms\formsets.py:491
msgid "Order"
msgstr "Bestellung"

#: .\venv\Lib\site-packages\django\forms\formsets.py:499
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_preview.html:26
#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:55
#: .\src\templates\candidate\tab\details_tab.html:126
#: .\src\templates\candidate\tab\documents_tab.html:101
#: .\src\templates\company\tabs\details_tab.html:62
#: .\src\templates\company\tabs\documents_tab.html:71
#: .\src\templates\jobad\jobad_detail.html:344
#: .\src\templates\jobad\jobad_detail.html:545
msgid "Delete"
msgstr "Löschen"

#: .\venv\Lib\site-packages\django\forms\models.py:886
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr "Bitte korrigieren Sie die doppelten Daten für %(field)s."

#: .\venv\Lib\site-packages\django\forms\models.py:891
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""
"Bitte korrigieren Sie die doppelten Daten für %(field)s, die eindeutig sein "
"müssen."

#: .\venv\Lib\site-packages\django\forms\models.py:898
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""
"Bitte korrigieren Sie die doppelten Daten für %(field_name)s, die für "
"%(lookup)s in %(date_field)s eindeutig sein müssen."

#: .\venv\Lib\site-packages\django\forms\models.py:907
msgid "Please correct the duplicate values below."
msgstr "Bitte korrigieren Sie die doppelten Werte unten."

#: .\venv\Lib\site-packages\django\forms\models.py:1338
msgid "The inline value did not match the parent instance."
msgstr "Der Inline-Wert stimmte nicht mit der übergeordneten Instanz überein."

#: .\venv\Lib\site-packages\django\forms\models.py:1429
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""
"Machen sie eine gültige Auswahl. Diese Auswahl entspricht keiner der "
"verfügbaren Optionen."

#: .\venv\Lib\site-packages\django\forms\models.py:1568
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr "\"%(pk)s\" ist kein gültiger Wert."

#: .\venv\Lib\site-packages\django\forms\utils.py:226
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""
"%(datetime)s konnte in der Zeitzone %(current_timezone)s nicht interpretiert "
"werden; sie kann mehrdeutig sein oder nicht existieren."

#: .\venv\Lib\site-packages\django\forms\widgets.py:463
msgid "Clear"
msgstr "Zurücksetzen"

#: .\venv\Lib\site-packages\django\forms\widgets.py:464
msgid "Currently"
msgstr "Aktuell"

#: .\venv\Lib\site-packages\django\forms\widgets.py:465
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\stacked.html:44
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\tabular.html:74
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list_default.html:42
msgid "Change"
msgstr "Ändern"

#: .\venv\Lib\site-packages\django\forms\widgets.py:794
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\boolean.html:8
msgid "Unknown"
msgstr "Unbekannt"

#: .\venv\Lib\site-packages\django\forms\widgets.py:795
msgid "Yes"
msgstr "Ja"

#: .\venv\Lib\site-packages\django\forms\widgets.py:796
#: .\src\templates\company\list.html:34
msgid "No"
msgstr "Nein"

#. Translators: Please do not add spaces around commas.
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:874
msgid "yes,no,maybe"
msgstr "ja, Nein, oder Vielleicht"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:904
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:921
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] "%(size)d Byte"
msgstr[1] "%(size)d Bytes"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:923
#, python-format
msgid "%s KB"
msgstr "%s KB"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:925
#, python-format
msgid "%s MB"
msgstr "%s MB"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:927
#, python-format
msgid "%s GB"
msgstr "%s GB"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:929
#, python-format
msgid "%s TB"
msgstr "%s TB"

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:931
#, python-format
msgid "%s PB"
msgstr "%s PB"

#: .\venv\Lib\site-packages\django\utils\dateformat.py:73
msgid "p.m."
msgstr "PM."

#: .\venv\Lib\site-packages\django\utils\dateformat.py:74
msgid "a.m."
msgstr "AM."

#: .\venv\Lib\site-packages\django\utils\dateformat.py:79
msgid "PM"
msgstr "PM"

#: .\venv\Lib\site-packages\django\utils\dateformat.py:80
msgid "AM"
msgstr "AM"

#: .\venv\Lib\site-packages\django\utils\dateformat.py:152
msgid "midnight"
msgstr "Mitternacht"

#: .\venv\Lib\site-packages\django\utils\dateformat.py:154
msgid "noon"
msgstr "Mittag"

#: .\venv\Lib\site-packages\django\utils\dates.py:7
msgid "Monday"
msgstr "Montag"

#: .\venv\Lib\site-packages\django\utils\dates.py:8
msgid "Tuesday"
msgstr "Dienstag"

#: .\venv\Lib\site-packages\django\utils\dates.py:9
msgid "Wednesday"
msgstr "Mittwoch"

#: .\venv\Lib\site-packages\django\utils\dates.py:10
msgid "Thursday"
msgstr "Donnerstag"

#: .\venv\Lib\site-packages\django\utils\dates.py:11
msgid "Friday"
msgstr "Freitag"

#: .\venv\Lib\site-packages\django\utils\dates.py:12
msgid "Saturday"
msgstr "Samstag"

#: .\venv\Lib\site-packages\django\utils\dates.py:13
msgid "Sunday"
msgstr "Sonntag"

#: .\venv\Lib\site-packages\django\utils\dates.py:16
msgid "Mon"
msgstr "Mo"

#: .\venv\Lib\site-packages\django\utils\dates.py:17
msgid "Tue"
msgstr "Di"

#: .\venv\Lib\site-packages\django\utils\dates.py:18
msgid "Wed"
msgstr "Mi"

#: .\venv\Lib\site-packages\django\utils\dates.py:19
msgid "Thu"
msgstr "Do"

#: .\venv\Lib\site-packages\django\utils\dates.py:20
msgid "Fri"
msgstr "Fr"

#: .\venv\Lib\site-packages\django\utils\dates.py:21
msgid "Sat"
msgstr "Sam"

#: .\venv\Lib\site-packages\django\utils\dates.py:22
msgid "Sun"
msgstr "So"

#: .\venv\Lib\site-packages\django\utils\dates.py:25
msgid "January"
msgstr "Januar"

#: .\venv\Lib\site-packages\django\utils\dates.py:26
msgid "February"
msgstr "Februar"

#: .\venv\Lib\site-packages\django\utils\dates.py:27
msgid "March"
msgstr "März"

#: .\venv\Lib\site-packages\django\utils\dates.py:28
msgid "April"
msgstr "April"

#: .\venv\Lib\site-packages\django\utils\dates.py:29
msgid "May"
msgstr "Mai"

#: .\venv\Lib\site-packages\django\utils\dates.py:30
msgid "June"
msgstr "Juni"

#: .\venv\Lib\site-packages\django\utils\dates.py:31
msgid "July"
msgstr "Juli"

#: .\venv\Lib\site-packages\django\utils\dates.py:32
msgid "August"
msgstr "August"

#: .\venv\Lib\site-packages\django\utils\dates.py:33
msgid "September"
msgstr "September"

#: .\venv\Lib\site-packages\django\utils\dates.py:34
msgid "October"
msgstr "Oktober"

#: .\venv\Lib\site-packages\django\utils\dates.py:35
msgid "November"
msgstr "November"

#: .\venv\Lib\site-packages\django\utils\dates.py:36
msgid "December"
msgstr "Dezember"

#: .\venv\Lib\site-packages\django\utils\dates.py:39
msgid "jan"
msgstr "Jan"

#: .\venv\Lib\site-packages\django\utils\dates.py:40
msgid "feb"
msgstr "Feb"

#: .\venv\Lib\site-packages\django\utils\dates.py:41
msgid "mar"
msgstr "Mrz"

#: .\venv\Lib\site-packages\django\utils\dates.py:42
msgid "apr"
msgstr "Apr"

#: .\venv\Lib\site-packages\django\utils\dates.py:43
msgid "may"
msgstr "Mai"

#: .\venv\Lib\site-packages\django\utils\dates.py:44
msgid "jun"
msgstr "Jun"

#: .\venv\Lib\site-packages\django\utils\dates.py:45
msgid "jul"
msgstr "Jul"

#: .\venv\Lib\site-packages\django\utils\dates.py:46
msgid "aug"
msgstr "Aug"

#: .\venv\Lib\site-packages\django\utils\dates.py:47
msgid "sep"
msgstr "Sep"

#: .\venv\Lib\site-packages\django\utils\dates.py:48
msgid "oct"
msgstr "Okt"

#: .\venv\Lib\site-packages\django\utils\dates.py:49
msgid "nov"
msgstr "Nov"

#: .\venv\Lib\site-packages\django\utils\dates.py:50
msgid "dec"
msgstr "Dez"

#: .\venv\Lib\site-packages\django\utils\dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr "Jan."

#: .\venv\Lib\site-packages\django\utils\dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr "Feb."

#: .\venv\Lib\site-packages\django\utils\dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr "März"

#: .\venv\Lib\site-packages\django\utils\dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr "April"

#: .\venv\Lib\site-packages\django\utils\dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr "Mai"

#: .\venv\Lib\site-packages\django\utils\dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr "Juni"

#: .\venv\Lib\site-packages\django\utils\dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr "Juli"

#: .\venv\Lib\site-packages\django\utils\dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr "Aug."

#: .\venv\Lib\site-packages\django\utils\dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr "Sept."

#: .\venv\Lib\site-packages\django\utils\dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr "Okt."

#: .\venv\Lib\site-packages\django\utils\dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr "Nov."

#: .\venv\Lib\site-packages\django\utils\dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr "Dez."

#: .\venv\Lib\site-packages\django\utils\dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr "Januar"

#: .\venv\Lib\site-packages\django\utils\dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr "Februar"

#: .\venv\Lib\site-packages\django\utils\dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr "März"

#: .\venv\Lib\site-packages\django\utils\dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr "April"

#: .\venv\Lib\site-packages\django\utils\dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr "Mai"

#: .\venv\Lib\site-packages\django\utils\dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr "Juni"

#: .\venv\Lib\site-packages\django\utils\dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr "Juli"

#: .\venv\Lib\site-packages\django\utils\dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr "August"

#: .\venv\Lib\site-packages\django\utils\dates.py:75
msgctxt "alt. month"
msgid "September"
msgstr "September"

#: .\venv\Lib\site-packages\django\utils\dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr "Oktober"

#: .\venv\Lib\site-packages\django\utils\dates.py:77
msgctxt "alt. month"
msgid "November"
msgstr "November"

#: .\venv\Lib\site-packages\django\utils\dates.py:78
msgctxt "alt. month"
msgid "December"
msgstr "Dezember"

#: .\venv\Lib\site-packages\django\utils\ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr "Dies ist keine gültige IPv6-Adresse."

#: .\venv\Lib\site-packages\django\utils\text.py:137
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr "%(truncated_text)s…"

#: .\venv\Lib\site-packages\django\utils\text.py:322
msgid "or"
msgstr "oder"

#. Translators: This string is used as a separator between list elements
#: .\venv\Lib\site-packages\django\utils\text.py:341
#: .\venv\Lib\site-packages\django\utils\timesince.py:135
msgid ", "
msgstr ", "

#: .\venv\Lib\site-packages\django\utils\timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d Jahr"
msgstr[1] "%(num)d Jahre"

#: .\venv\Lib\site-packages\django\utils\timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d Monat"
msgstr[1] "%(num)d Monate"

#: .\venv\Lib\site-packages\django\utils\timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d Woche"
msgstr[1] "%(num)d Wochen"

#: .\venv\Lib\site-packages\django\utils\timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d Tag"
msgstr[1] "%(num)d Tage"

#: .\venv\Lib\site-packages\django\utils\timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d Stunde"
msgstr[1] "%(num)d Stunden"

#: .\venv\Lib\site-packages\django\utils\timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d Minute"
msgstr[1] "%(num)d Minuten"

#: .\venv\Lib\site-packages\django\views\csrf.py:111
msgid "Forbidden"
msgstr "Nicht erlaubt"

#: .\venv\Lib\site-packages\django\views\csrf.py:112
msgid "CSRF verification failed. Request aborted."
msgstr "Die CSRF-Verifizierung ist fehlgeschlagen. Anfrage abgebrochen."

#: .\venv\Lib\site-packages\django\views\csrf.py:116
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""
"Diese Meldung wird angezeigt, weil für diese HTTPS-Site ein \"Referer-"
"Header\" von Ihrem Webbrowser gesendet werden muss, aber keiner gesendet "
"wurde. Dieser Header ist aus Sicherheitsgründen erforderlich, um "
"sicherzustellen, dass Dein Browser nicht von Dritten gekapert wird."

#: .\venv\Lib\site-packages\django\views\csrf.py:122
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""
"Wenn Sie Ihren Browser so konfiguriert haben, dass die \"Referer\"-Header "
"deaktiviert sind, aktivieren Sie sie bitte wieder, zumindest für diese "
"Website oder für HTTPS-Verbindungen oder für \"gleichartige\" Anfragen."

#: .\venv\Lib\site-packages\django\views\csrf.py:127
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a "
"rel=\"noreferrer\" …> for links to third-party sites."
msgstr ""
"Wenn Sie das <meta name=\"referrer\" content=\"no-referrer\"> Tag verwenden "
"oder oder die Kopfzeile \"Referrer-Policy: no-referrer\" einfügen, entfernen "
"Sie diese bitte. Der CSRF-Schutz verlangt, dass der \"Referrer\"-Header eine "
"strenge Überprüfung des Referrers durchführt. Wenn Sie sich Sorgen um den "
"Datenschutz machen, verwenden Sie Alternativen wie z.B. <a "
"rel=\"noreferrer\" …> für Links zu Websites Dritter."

#: .\venv\Lib\site-packages\django\views\csrf.py:136
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""
"Sie sehen diese Meldung, weil diese Website ein CSRF-Cookie beim Übermitteln "
"von Formularen benötigt. Dieses Cookie ist aus Sicherheitsgründen "
"erforderlich, um sicherzustellen, dass Ihr Browser nicht von Dritten "
"missbraucht wird."

#: .\venv\Lib\site-packages\django\views\csrf.py:142
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""
"Wenn Sie Ihren Browser so konfiguriert haben, dass Cookies deaktiviert sind, "
"aktivieren Sie sie bitte wieder, zumindest für diese Website oder für "
"Anfragen \"gleichen Ursprungs\"."

#: .\venv\Lib\site-packages\django\views\csrf.py:148
msgid "More information is available with DEBUG=True."
msgstr "Weitere Informationen erhalten Sie mit DEBUG=True."

#: .\venv\Lib\site-packages\django\views\generic\dates.py:44
msgid "No year specified"
msgstr "Kein Jahr angegeben"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:64
#: .\venv\Lib\site-packages\django\views\generic\dates.py:115
#: .\venv\Lib\site-packages\django\views\generic\dates.py:214
msgid "Date out of range"
msgstr "Datum außerhalb des Bereichs"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:94
msgid "No month specified"
msgstr "Kein Monat angegeben"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:147
msgid "No day specified"
msgstr "Kein Tag angegeben"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:194
msgid "No week specified"
msgstr "Keine Woche angegeben"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:349
#: .\venv\Lib\site-packages\django\views\generic\dates.py:380
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr "Nein%(verbose_name_plural)s ist verfügbar"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:652
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because "
"%(class_name)s.allow_future is False."
msgstr ""
"Future %(verbose_name_plural)s ist nicht verfügbar, da %(class_name)s. "
"allow_future False ist."

#: .\venv\Lib\site-packages\django\views\generic\dates.py:692
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr "Ungültige Datum \"%(datestr)s\" für das Format \"%(format)s\""

#: .\venv\Lib\site-packages\django\views\generic\detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr "Kein %(verbose_name)s wurde mit der Abfrage gefunden"

#: .\venv\Lib\site-packages\django\views\generic\list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr "Die Seite ist nicht “letzte”, und auch keine Zahl."

#: .\venv\Lib\site-packages\django\views\generic\list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr "Ungültige Seite (%(page_number)s):%(message)s"

#: .\venv\Lib\site-packages\django\views\generic\list.py:169
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr "Leere Liste und “%(class_name)s.allow_empty” ist False."

#: .\venv\Lib\site-packages\django\views\static.py:38
msgid "Directory indexes are not allowed here."
msgstr "Verzeichnisindizes sind hier nicht erlaubt."

#: .\venv\Lib\site-packages\django\views\static.py:40
#, python-format
msgid "“%(path)s” does not exist"
msgstr "“%(path)s” existiert nicht"

#: .\venv\Lib\site-packages\django\views\static.py:79
#, python-format
msgid "Index of %(directory)s"
msgstr "Index von %(directory)s"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:7
#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:220
msgid "The install worked successfully! Congratulations!"
msgstr "Die Installation hat erfolgreich funktioniert! Herzlichen Glückwunsch!"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""
"<a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">Versionshinweise</a> für Django "
"%(version)s anzeigen"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:221
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" "
"rel=\"noopener\">DEBUG=True</a> is in your settings file and you have not "
"configured any URLs."
msgstr ""
"Sie sehen diese Seite, weil <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" "
"rel=\"noopener\">DEBUG=True</a> in Ihrer Einstellungsdatei steht und Sie "
"keine URLs konfiguriert haben."

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:229
msgid "Django Documentation"
msgstr "Django-Dokumentation"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:230
msgid "Topics, references, &amp; how-to’s"
msgstr "Themen, Referenzen, &amp; Anleitungen"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:238
msgid "Tutorial: A Polling App"
msgstr "Tutorial: Eine Umfrage-App"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:239
msgid "Get started with Django"
msgstr "Erste Schritte mit Django"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:247
msgid "Django Community"
msgstr "Django-Gemeinschaft"

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:248
msgid "Connect, get help, or contribute"
msgstr "Verbinden, Hilfe holen oder beitragen"

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1311
#, python-format
msgid "Attempting to connect to qpid with SASL mechanism %s"
msgstr ""

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1316
#, python-format
msgid "Connected to qpid with SASL mechanism %s"
msgstr ""

#: .\venv\Lib\site-packages\kombu\transport\qpid.py:1334
#, python-format
msgid "Unable to connect to qpid with SASL mechanism %s"
msgstr ""

#: .\venv\Lib\site-packages\unfold\admin.py:124
#: .\venv\Lib\site-packages\unfold\templatetags\unfold_list.py:325
msgid "Select record"
msgstr "Datensatz auswählen"

#: .\venv\Lib\site-packages\unfold\admin.py:156
#: .\venv\Lib\site-packages\unfold\admin.py:177
msgid "Select value"
msgstr "Wähle Wert"

#: .\venv\Lib\site-packages\unfold\admin.py:538
msgid "Select action"
msgstr "Aktion auswählen"

#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:58
#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:116
msgid "All"
msgstr "Alle"

#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:82
#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:105
#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:122
#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:149
#: .\venv\Lib\site-packages\unfold\contrib\filters\admin.py:166
#, fuzzy
msgid "By {}"
msgstr "Von {}"

#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:77
msgid "Value"
msgstr "Wert"

#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:93
#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:133
msgid "From"
msgstr "Von"

#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:100
#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:143
msgid "To"
msgstr "Bis"

#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:169
msgid "Date from"
msgstr "Datum ab"

#: .\venv\Lib\site-packages\unfold\contrib\filters\forms.py:185
msgid "Date to"
msgstr "Datum bis"

#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_date_range.html:5
#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_datetime_range.html:5
#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_numeric_range.html:5
#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_numeric_single.html:5
#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_numeric_slider.html:7
#, fuzzy, python-format
msgid "By %(filter_title)s"
msgstr "Von %(filter_title)s"

#: .\venv\Lib\site-packages\unfold\contrib\filters\templates\unfold\filters\filters_numeric_slider.html:30
msgid "Not enough data."
msgstr "Nicht genügend Daten."

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\array.html:30
msgid "Add new item"
msgstr "Neues Element hinzufügen"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:7
msgid "Paragraph"
msgstr "Absatz"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:11
msgid "Underlined"
msgstr "Unterstrichen"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:15
msgid "Bold"
msgstr "Fett"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:19
msgid "Italic"
msgstr "Kursiv"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:23
msgid "Strike"
msgstr "Durchgestrichen"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:27
#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:98
msgid "Link"
msgstr "Link"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:35
#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:39
#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:43
#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:47
msgid "Heading"
msgstr "Überschrift"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:55
msgid "Quote"
msgstr "Zitat"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:59
msgid "Code"
msgstr "Code"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:63
msgid "Unordered list"
msgstr "Ungeordnete Liste einfügen"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:67
msgid "Ordered list"
msgstr "Geordnete Liste"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:71
msgid "Indent increase"
msgstr "Vergrößerung des Einzugs"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:75
msgid "Indent decrease"
msgstr "Verkleinerung des Einzugs"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:83
msgid "Undo"
msgstr "Rückgängig"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:87
msgid "Redo"
msgstr "Wiederholen"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:95
msgid "Enter an URL"
msgstr "Gib eine URL ein"

#: .\venv\Lib\site-packages\unfold\contrib\forms\templates\unfold\forms\helpers\toolbar.html:102
msgid "Unlink"
msgstr "Trennen"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\change_form.html:8
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage.html:22
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_group.html:25
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_user.html:26
msgid "Object permissions"
msgstr "Objekt-Berechtigungen"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage.html:10
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_group.html:13
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_user.html:14
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\export.html:17
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import.html:28
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:11
#: .\venv\Lib\site-packages\unfold\templates\admin\app_index.html:15
#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\change_password.html:13
#: .\venv\Lib\site-packages\unfold\templates\admin\base.html:23
#: .\venv\Lib\site-packages\unfold\templates\admin\change_form.html:18
#: .\venv\Lib\site-packages\unfold\templates\admin\change_list.html:31
#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:17
#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:17
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:9
#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_done.html:12
#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_form.html:12
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_icon.html:6
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_icon.html:8
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_icon.html:10
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_logo.html:5
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_logo.html:7
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\site_logo.html:9
msgid "Home"
msgstr "Startseite"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_group.html:39
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_user.html:40
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:10
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:44
msgid "Object"
msgstr "Objekt"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_group.html:42
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:15
#: .\src\templates\jobad\index.html:87
msgid "Group"
msgstr "Gruppe"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_group.html:49
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_user.html:50
#: .\venv\Lib\site-packages\unfold\templates\admin\pagination.html:35
#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:10
#: .\src\templates\candidate\add.html:25
#: .\src\templates\company\documents_overview.html:244
#: .\src\templates\company\documents_overview.html:353
#: .\src\templates\company\tabs\_user_registration.html:178
#: .\src\templates\jobad\jobad_detail.html:338
#: .\src\templates\jobad\jobad_detail.html:419
#: .\src\templates\userprofiles\_user_registration_modal.html:201
#: .\src\templates\userprofiles\profile.html:201
msgid "Save"
msgstr "Speichern"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\admin\guardian\model\obj_perms_manage_user.html:43
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:33
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:15
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:33
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:38
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:54
#: .\src\templates\company\company_detail.html:82
msgid "User"
msgstr "Benutzer"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:7
msgid "Group permissions"
msgstr "Berechtigungen gruppieren"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:25
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:49
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:25
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:49
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:42
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:58
msgid "Action"
msgstr "Aktion"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:51
#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:51
#: .\src\templates\company\company_edit.html:7
#: .\src\templates\company\tabs\supplier.html:96
#: .\src\templates\jobad\jobad_detail.html:335
#: .\src\templates\jobad\tab\_edit_modal.html:532
#: .\src\templates\jobad\tab\worker_deployment.html:80
msgid "Edit"
msgstr "Bearbeiten"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\group_form.html:67
msgid "Manage group"
msgstr "Gruppe verwalten"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:7
msgid "User permissions"
msgstr "Benutzer Rechte"

#: .\venv\Lib\site-packages\unfold\contrib\guardian\templates\unfold\guardian\user_form.html:67
msgid "Manage user"
msgstr "Benutzer verwalten"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\change_form.html:8
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\change_list_export_item.html:4
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\export.html:28
msgid "Export"
msgstr "Export"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\change_list_import_item.html:4
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import.html:39
#: .\src\templates\jobad\tab\candidate_profile.html:57
#: .\src\templates\jobad\tab\candidate_profile.html:184
msgid "Import"
msgstr "Importieren"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\export.html:41
#, fuzzy, python-format
msgid ""
"\n"
"                    Export %(len)s selected item.\n"
"                "
msgid_plural ""
"\n"
"                    Export %(len)s selected items.\n"
"                "
msgstr[0] ""
"\n"
"Exportieren Sie %(len)s ausgewähltes Element.\n"
"                "
msgstr[1] ""
"\n"
"Exportieren Sie %(len)s ausgewählte Elemente.\n"
"                "

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\export.html:60
msgid "This exporter will export the following fields"
msgstr "Dieser Exporter exportiert die folgenden Felder"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\export.html:79
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_form.html:21
#: .\src\templates\company\location_form.html:9
#: .\src\templates\company\position_form.html:9
#: .\src\templates\company\rejections_form.html:9
#: .\src\templates\company\structure_form.html:9
#: .\src\templates\company\tabs\employees.html:74
msgid "Submit"
msgstr "Senden"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_confirm.html:10
msgid ""
"Below is a preview of data to be imported. If you are satisfied with the "
"results, click 'Confirm import'"
msgstr ""
"Im Folgenden finden Sie eine Vorschau der zu importierenden Daten. Wenn Sie "
"mit den Ergebnissen zufrieden sind, klicken Sie auf \"Import bestätigen\""

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_confirm.html:15
msgid "Confirm import"
msgstr "Import bestätigen"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_errors.html:19
msgid "Line number"
msgstr "Zeilennummer"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_preview.html:22
#: .\src\templates\index.html:95
msgid "New"
msgstr "Neu"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_preview.html:24
msgid "Skipped"
msgstr "Übersprungen"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_preview.html:28
msgid "Update"
msgstr "Aktualisieren"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:7
msgid "Some rows failed to validate"
msgstr "Einige Zeilen konnten nicht überprüft werden"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:13
msgid ""
"Please correct these errors in your data where possible, then reupload it "
"using the form above."
msgstr ""
"Bitte korrigieren Sie diese Fehler in Ihren Daten, wo möglich, und laden Sie "
"sie dann mit dem obigen Formular erneut hoch."

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:22
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:40
msgid "Row"
msgstr "Zeile"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:26
#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:44
msgid "Errors"
msgstr "Fehler"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\import_validation.html:65
msgid "Non field specific"
msgstr "Nicht feldspezifisch"

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\resource_fields_list.html:6
msgid "This exporter will export the following fields: "
msgstr "Dieser Exporter exportiert die folgenden Felder: "

#: .\venv\Lib\site-packages\unfold\contrib\import_export\templates\admin\import_export\resource_fields_list.html:8
msgid "This importer will import the following fields: "
msgstr "Dieser Importeur importiert die folgenden Felder: "

#. Translators: Model verbose name and instance representation,
#. suitable to be an item in a list.
#: .\venv\Lib\site-packages\unfold\contrib\inlines\admin.py:94
#, fuzzy, python-format
msgid "%(class_name)s %(instance)s"
msgstr "%(class_name)s %(instance)s"

#: .\venv\Lib\site-packages\unfold\contrib\inlines\admin.py:106
#, python-format
msgid ""
"Deleting %(class_name)s %(instance)s would require deleting the following "
"protected related objects: %(related_objects)s"
msgstr ""
"Um %(class_name)s %(instance)s zu löschen, müssten die folgenden geschützten "
"verwandten Objekte gelöscht werden: %(related_objects)s"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history.html:8
msgid ""
"Choose a date from the list below to revert to a previous version of this "
"object."
msgstr ""
"Wählen Sie ein Datum aus der Liste unten aus, um zu einer früheren Version "
"dieses Objekts zurückzukehren."

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history.html:16
msgid "This object doesn't have a change history."
msgstr "Dieses Objekt verfügt nicht über einen Änderungsverlauf."

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:22
#: .\venv\Lib\site-packages\unfold\templates\admin\change_form_object_tools.html:6
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:21
#: .\src\templates\candidate\candidate_detail.html:30
#: .\src\templates\jobad\jobad_detail.html:129
#: .\src\templates\jobad\tab\worker_deployment.html:69
msgid "History"
msgstr "Abgelehnt / Abgemeldet"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:26
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\stacked.html:46
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\tabular.html:76
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list_default.html:38
msgid "View"
msgstr "Anzeigen"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:29
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\submit_line.html:9
msgid "Revert"
msgstr "Zurücksetzen"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:48
msgid ""
"Press the 'Revert' button below to revert to this version of the object."
msgstr ""
"Klicken Sie unten auf die Schaltfläche \"Zurücksetzen\", um zu dieser "
"Version des Objekts zurückzukehren."

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_form.html:52
msgid "Press the 'Change History' button below to edit the history."
msgstr ""
"Klicken Sie unten auf die Schaltfläche \"Verlauf ändern\", um den Verlauf zu "
"bearbeiten."

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:20
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:56
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:34
#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:50
msgid "Date/time"
msgstr "Datum/Zeit"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:24
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:60
#: .\src\jobad\forms.py:23 .\src\jobad\models.py:299
msgid "Comment"
msgstr "Kommentar"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:28
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:64
msgid "Changed by"
msgstr "Geändert durch"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:32
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:78
msgid "Change reason"
msgstr "Änderungsgrund"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:36
#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:82
msgid "Changes"
msgstr "Änderungen"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\object_history_list.html:74
msgid "None"
msgstr "Nichts"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\submit_line.html:15
msgid "Change History"
msgstr "Änderungshistorie"

#: .\venv\Lib\site-packages\unfold\contrib\simple_history\templates\simple_history\submit_line.html:20
#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:34
#: .\src\templates\company\tabs\employees.html:75
#: .\src\templates\userprofiles\profile.html:205
msgid "Close"
msgstr "Schließen"

#: .\venv\Lib\site-packages\unfold\forms.py:44
msgid "Select action to run"
msgstr "Auszuführende Aktion auswählen"

#: .\venv\Lib\site-packages\unfold\forms.py:102
msgid ""
"Raw passwords are not stored, so there is no way to see this user’s "
"password, but you can change the password using <a href=\"{}\" class=\"text-"
"primary-600 dark:text-primary-500\">this form</a>."
msgstr ""
"Rohe Passwörter werden nicht gespeichert, so dass es keine Möglichkeit gibt, "
"das Passwort dieses Benutzers zu sehen, aber Sie können das Passwort über <a "
"href=\"{}\" class=\"text-primary-600 dark:text-primary-500\">dieses "
"Formular</a> ändern."

#: .\venv\Lib\site-packages\unfold\templates\admin\actions.html:22
msgid "Run the selected action"
msgstr "Ausführen der ausgewählten Aktion"

#: .\venv\Lib\site-packages\unfold\templates\admin\actions.html:23
msgid "Run"
msgstr "Ausführen"

#: .\venv\Lib\site-packages\unfold\templates\admin\actions.html:42
msgid "Click here to select the objects across all pages"
msgstr "Klicken Sie hier um Objekte über alle Seiten hinweg auszuwählen"

#: .\venv\Lib\site-packages\unfold\templates\admin\actions.html:43
#, python-format
msgid "Select all %(total_count)s %(module_name)s"
msgstr "Alle auswählen %(total_count)s %(module_name)s"

#: .\venv\Lib\site-packages\unfold\templates\admin\actions.html:49
msgid "Clear selection"
msgstr "Auswahl löschen"

#: .\venv\Lib\site-packages\unfold\templates\admin\app_index.html:5
#: .\venv\Lib\site-packages\unfold\templates\admin\base_site.html:3
#: .\venv\Lib\site-packages\unfold\templates\admin\index.html:7
#: .\venv\Lib\site-packages\unfold\templates\unfold\layouts\base.html:11
msgid "Django site admin"
msgstr "Django Site Admin"

#: .\venv\Lib\site-packages\unfold\templates\admin\app_index.html:30
#: .\venv\Lib\site-packages\unfold\templates\admin\base_site.html:6
#: .\venv\Lib\site-packages\unfold\templates\admin\index.html:12
#: .\venv\Lib\site-packages\unfold\templates\unfold\layouts\base.html:6
msgid "Django administration"
msgstr "Django-Administration"

#: .\venv\Lib\site-packages\unfold\templates\admin\app_list.html:12
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list_default.html:9
#, python-format
msgid "Models in the %(name)s application"
msgstr "Modelle in der %(name)s Anwendung"

#: .\venv\Lib\site-packages\unfold\templates\admin\app_list.html:47
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list.html:98
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list_default.html:59
msgid "You don’t have permission to view or edit anything."
msgstr "Sie haben keine Berechtigung, etwas anzuzeigen oder zu bearbeiten."

#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\add_form.html:7
msgid ""
"First, enter a username and password. Then, you’ll be able to edit more user "
"options."
msgstr ""
"Geben Sie zunächst einen Benutzernamen und ein Kennwort ein. Anschließend "
"können Sie weitere Benutzeroptionen bearbeiten."

#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\add_form.html:9
msgid "Enter a username and password."
msgstr "Geben Sie Benutzernamen und Kennwort an."

#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\change_password.html:25
#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\change_password.html:55
#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_done.html:15
#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_form.html:15
#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_form.html:46
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\account_links.html:23
msgid "Change password"
msgstr "Passwort ändern"

#: .\venv\Lib\site-packages\unfold\templates\admin\auth\user\change_password.html:44
#, python-format
msgid "Enter a new password for the user <strong>%(username)s</strong>."
msgstr ""
"Geben Sie ein neues Kennwort für den Benutzer <strong>%(username)s</"
"strong>ein."

#: .\venv\Lib\site-packages\unfold\templates\admin\change_form.html:32
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\add_link.html:5
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\add_link.html:8
#, python-format
msgid "Add %(name)s"
msgstr "%(name)s hinzufügen"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_form_object_tools.html:12
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\stacked.html:58
#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\tabular.html:84
msgid "View on site"
msgstr "Auf der Webseite anzeigen"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_list.html:98
msgid "Filters"
msgstr "Filter"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_list_results.html:28
msgid "Select all rows"
msgstr "Alle Zeilen auswählen"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_list_results.html:42
msgid "Toggle sorting"
msgstr "Sortierung ein-/ausschalten"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_list_results.html:50
msgid "Remove from sorting"
msgstr "Aus der Sortierung entfernen"

#: .\venv\Lib\site-packages\unfold\templates\admin\change_list_results.html:56
#, python-format
msgid "Sorting priority: %(priority_number)s"
msgstr "Sortierung: %(priority_number)s"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:37
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would result in deleting "
"related objects, but your account doesn't have permission to delete the "
"following types of objects:"
msgstr ""
"Das Löschen der %(object_name)s '%(escaped_object)s' würde dazu führen, dass "
"zugehörige Objekte gelöscht werden, aber Ihr Konto verfügt nicht über die "
"Berechtigung zum Löschen der folgenden Objekttypen:"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:53
#, python-format
msgid ""
"Deleting the %(object_name)s '%(escaped_object)s' would require deleting the "
"following protected related objects:"
msgstr ""
"Das Löschen der %(object_name)s '%(escaped_object)s'  erfordert das Löschen "
"der folgenden geschützten verwandten Objekte:"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:69
#, python-format
msgid ""
"Are you sure you want to delete the %(object_name)s \"%(escaped_object)s\"? "
"All of the following related items will be deleted:"
msgstr ""
"Sind Sie sicher, dass Sie die %(object_name)s \"%(escaped_object)s\" löschen "
"möchten? Alle folgenden zugehörigen Elemente werden gelöscht:"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:76
#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:71
msgid "Objects"
msgstr "Objekte"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:100
#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:93
msgid "No, take me back"
msgstr "Nein, bitte abbrechen"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_confirmation.html:103
#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:96
msgid "Yes, I’m sure"
msgstr "Ja, ich bin sicher"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:27
msgid "Delete multiple objects"
msgstr "Mehrere Objekte löschen"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:37
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would result in deleting related "
"objects, but your account doesn't have permission to delete the following "
"types of objects:"
msgstr ""
"Das Löschen der ausgewählten %(objects_name)s würde im Löschen geschützter "
"verwandter Objekte resultieren, allerdings besitzt Ihr Benutzerkonto nicht "
"die nötigen Rechte, um die folgenden Daten zu löschen:"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:50
#, python-format
msgid ""
"Deleting the selected %(objects_name)s would require deleting the following "
"protected related objects:"
msgstr ""
"Das Löschen der ausgewählten %(objects_name)s würde ein Löschen der "
"folgenden geschützten verwandten Objekte erfordern:"

#: .\venv\Lib\site-packages\unfold\templates\admin\delete_selected_confirmation.html:64
#, python-format
msgid ""
"Are you sure you want to delete the selected %(objects_name)s? All of the "
"following objects and their related items will be deleted:"
msgstr ""
"Sind Sie sicher, dass Sie die ausgewählten %(objects_name)s löschen wollen? "
"Alle folgenden Objekte und ihre verwandten Objekte werden gelöscht:"

#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\tabular.html:38
msgid "Delete?"
msgstr "Löschen?"

#: .\venv\Lib\site-packages\unfold\templates\admin\edit_inline\tabular.html:143
msgid "Remove"
msgstr "Entfernen"

#: .\venv\Lib\site-packages\unfold\templates\admin\filter.html:5
#, python-format
msgid " By %(filter_title)s "
msgstr " Von %(filter_title)s "

#: .\venv\Lib\site-packages\unfold\templates\admin\includes\object_delete_summary.html:5
msgid "Summary"
msgstr "Zusammenfassung"

#: .\venv\Lib\site-packages\unfold\templates\admin\login.html:31
msgid "Welcome back to"
msgstr "Willkommen zurück in"

#: .\venv\Lib\site-packages\unfold\templates\admin\login.html:42
#, python-format
msgid ""
"You are authenticated as %(username)s, but are not authorized to access this "
"page. Would you like to login to a different account?"
msgstr ""
"Sie sind als %(username)s authentifiziert, aber nicht berechtigt, auf diese "
"Seite zuzugreifen. Möchten Sie sich auf einem anderen Konto anmelden?"

#: .\venv\Lib\site-packages\unfold\templates\admin\login.html:63
msgid "Log in"
msgstr "Anmelden"

#: .\venv\Lib\site-packages\unfold\templates\admin\login.html:72
msgid "Forgotten your password or username?"
msgstr "Benutzername oder Passwort vergessen?"

#: .\venv\Lib\site-packages\unfold\templates\admin\login.html:84
msgid "Return to site"
msgstr "Zurück zur Seite"

#: .\venv\Lib\site-packages\unfold\templates\admin\nav_sidebar.html:17
msgid "Menu"
msgstr "Menü"

#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:81
msgid "entry"
msgid_plural "entries"
msgstr[0] "Eintrag"
msgstr[1] "Einträge"

#: .\venv\Lib\site-packages\unfold\templates\admin\object_history.html:84
msgid ""
"This object doesn’t have a change history. It probably wasn’t added via this "
"admin site."
msgstr ""
"Dieses Objekt verfügt nicht über einen Änderungsverlauf. Es wurde "
"wahrscheinlich nicht über diese Admin-Seite hinzugefügt."

#: .\venv\Lib\site-packages\unfold\templates\admin\pagination.html:28
#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\welcomemsg.html:14
msgid "Show all"
msgstr "Alle anzeigen"

#: .\venv\Lib\site-packages\unfold\templates\admin\search_form.html:6
msgid "Type to search"
msgstr "Suchtext eingeben"

#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:23
msgid "Save and continue editing"
msgstr "Speichern und mit dem Bearbeiten fortfahren"

#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:25
msgid "Save and view"
msgstr "Speichern und Anzeigen"

#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:40
msgid "Save and add another"
msgstr "Sichern und neu hinzufügen"

#: .\venv\Lib\site-packages\unfold\templates\admin\submit_line.html:46
msgid "Save as new"
msgstr "Neu anlegen"

#: .\venv\Lib\site-packages\unfold\templates\registration\logged_out.html:11
msgid "You have been successfully logged out from the administration"
msgstr "Sie wurden erfolgreich von der Administration abgemeldet"

#: .\venv\Lib\site-packages\unfold\templates\registration\logged_out.html:16
msgid "Thanks for spending some quality time with the web site today."
msgstr ""
"Vielen Dank, dass Sie heute etwas Zeit mit der Website verbracht haben."

#: .\venv\Lib\site-packages\unfold\templates\registration\logged_out.html:20
msgid "Log in again"
msgstr "Erneut Anmelden"

#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_done.html:26
msgid "Your password was changed."
msgstr "Ihr Passwort wurde geändert."

#: .\venv\Lib\site-packages\unfold\templates\registration\password_change_form.html:35
msgid ""
"Please enter your old password, for security’s sake, and then enter your new "
"password twice so we can verify you typed it in correctly."
msgstr ""
"Bitte geben Sie aus Sicherheitsgründen Ihr altes Passwort ein, und geben Sie "
"dann Ihr neues Passwort zweimal ein, damit wir überprüfen können, ob Sie es "
"richtig eingegeben haben."

#: .\venv\Lib\site-packages\unfold\templates\unfold\change_list_filter.html:9
msgid "Filter"
msgstr "Filter"

#: .\venv\Lib\site-packages\unfold\templates\unfold\change_list_filter.html:16
msgid "Hide counts"
msgstr "Zählungen ausblenden"

#: .\venv\Lib\site-packages\unfold\templates\unfold\change_list_filter.html:20
msgid "Show counts"
msgstr "Anzahl anzeigen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\change_list_filter.html:29
msgid "Clear all filters"
msgstr "Alle Filter zurücksetzen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\change_list_filter.html:59
msgid "Apply Filters"
msgstr "Filter anwenden"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\account_links.html:17
msgid "View site"
msgstr "Seite ansehen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\account_links.html:32
#: .\src\templates\_base_frontend.html:200
msgid "Log out"
msgstr "Abmelden"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\actions_row.html:4
msgid "More actions"
msgstr "Weitere Aktionen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list.html:68
msgid "All applications"
msgstr "Alle Anwendungsbereiche"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\app_list_default.html:31
#: .\src\templates\company\add.html:5 .\src\templates\company\list.html:12
msgid "Add"
msgstr "Hinzufügen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\boolean.html:10
msgid "True"
msgstr "Wahr"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\boolean.html:12
msgid "False"
msgstr "Falsch"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\display_header.html:6
msgid "Record picture"
msgstr "Bild aufnehmen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\history.html:9
msgid "Recent actions"
msgstr "Letze Änderungen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\history.html:28
msgid "Unknown content"
msgstr "Unbekannter Inhalt"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\messages\errornote.html:6
msgid "Please correct the error below."
msgstr "Bitte korrigieren Sie den untenstehenden Fehler."

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\messages\errornote.html:8
msgid "Please correct the errors below."
msgstr "Bitte korrigieren Sie die untenstehenden Fehler."

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\search.html:18
msgid "Search apps and models..."
msgstr "Apps und Modelle suchen..."

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\search.html:18
msgid "Filter navigation items"
msgstr "Filtern von Navigationselementen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\tab_list.html:24
msgid "General"
msgstr "Allgemein"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\theme_switch.html:15
msgid "Dark"
msgstr "Dunkel"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\theme_switch.html:22
msgid "Light"
msgstr "Hell"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\theme_switch.html:29
msgid "System"
msgstr "System"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\welcomemsg.html:14
#, python-format
msgid "%(counter)s result"
msgid_plural "%(counter)s results"
msgstr[0] "%(counter)s Ergebnis"
msgstr[1] "%(counter)s Ergebnisse"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\welcomemsg.html:14
#, python-format
msgid "%(full_result_count)s total"
msgstr "%(full_result_count)s gesamt"

#: .\venv\Lib\site-packages\unfold\templates\unfold\helpers\welcomemsg.html:22
msgid "Welcome,"
msgstr "Willkommen,"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\clearable_file_input.html:6
msgid "Image preview"
msgstr "Bildvorschau"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\clearable_file_input.html:24
#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\clearable_file_input_small.html:17
msgid "Choose file to upload"
msgstr "Wählen Sie die Datei zum Hochladen aus"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\related_widget_wrapper.html:16
#, python-format
msgid "Change selected %(model)s"
msgstr "Ausgewählte %(model)s ändern"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\related_widget_wrapper.html:26
#, python-format
msgid "Add another %(model)s"
msgstr "Hinzufügen eines weiteren %(model)s"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\related_widget_wrapper.html:35
#, python-format
msgid "View selected %(model)s"
msgstr "Ausgewählte %(model)s anzeigen"

#: .\venv\Lib\site-packages\unfold\templates\unfold\widgets\related_widget_wrapper.html:45
#, python-format
msgid "Delete selected %(model)s"
msgstr "Löschen ausgewählter %(model)s"

#: .\venv\Lib\site-packages\unfold\templatetags\unfold_list.py:109
msgid "Select all objects on this page for an action"
msgstr "Markieren Sie alle Objekte auf dieser Seite für eine Aktion"

#: .\venv\Lib\site-packages\unfold\widgets.py:455
#: .\src\templates\index.html:272
msgid "Date"
msgstr "Veröffentlicht"

#: .\venv\Lib\site-packages\unfold\widgets.py:549
msgid "Select currency"
msgstr "Währung wählen"

#: .\src\common\models.py:76
#: .\src\templates\company\tabs\_user_registration.html:148
#: .\src\templates\userprofiles\_user_registration_modal.html:171
#: .\src\templates\userprofiles\tab\details_tab.html:22
msgid "Street"
msgstr "Straße"

#: .\src\common\models.py:77
#: .\src\templates\userprofiles\tab\details_tab.html:30
msgid "Post Code"
msgstr "Postleitzahl"

#: .\src\common\models.py:78
#: .\src\templates\company\tabs\_user_registration.html:157
#: .\src\templates\userprofiles\_user_registration_modal.html:180
#: .\src\templates\userprofiles\tab\details_tab.html:26
msgid "City"
msgstr "Stadt/Ort"

#: .\src\common\models.py:83
#: .\src\templates\company\tabs\_user_registration.html:167
#: .\src\templates\userprofiles\_user_registration_modal.html:190
msgid "Telephone number"
msgstr "Telefonnummer"

#: .\src\common\models.py:84
#: .\src\templates\company\tabs\_user_registration.html:162
msgid "Country"
msgstr "Land"

#: .\src\common\models.py:94 .\src\templates\_base_frontend.html:87
#: .\src\templates\company\tabs\details_tab.html:51
msgid "Logo"
msgstr "Logo"

#: .\src\common\validators.py:15 .\src\jobad\models.py:317
msgid "Candidates from outside Europe must have a work permit!"
msgstr "Bewerber von außerhalb Europas müssen eine Arbeitserlaubnis haben!"

#: .\src\common\views.py:654
msgid "Enter new password"
msgstr "Geben Sie Benutzernamen und Kennwort an."

#: .\src\common\views.py:716
msgid "Password reset unsuccessful"
msgstr "Passwort konnte nicht zurückgesetzt werden"

#: .\src\company\templatetags\custom_filters.py:18
msgid "Suche Unternehmen"
msgstr "Search company"

#: .\src\company\templatetags\custom_filters.py:19
msgid "Suche Lieferanten"
msgstr "Search supplier"

#: .\src\company\templatetags\custom_filters.py:20
msgid "Suche Master Vendor"
msgstr "Search Master Vendor"

#: .\src\company\templatetags\custom_filters.py:23
#: .\src\templates\company\documents_overview.html:31
msgid "Search"
msgstr "Suchen"

#: .\src\company\views.py:339
#, python-format
msgid "Neues Dokument für %(name)s"
msgstr "Neues Dokument für %(name)s"

#: .\src\company\views.py:923
msgid "Document uploaded successfully"
msgstr "Dokument erfolgreich geladen"

#: .\src\company\views.py:942
msgid "Error uploading document"
msgstr "Fehler beim Hochladen des Dokuments"

#: .\src\email_service\services.py:84
#, python-brace-format
msgid "Candidate Imported: {candidate_name}"
msgstr "Kandidat importiert: {candidate_name}"

#: .\src\email_service\services.py:112
#, python-brace-format
msgid "Neuer Kandidat für Ihre Stellenanzeige {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:142
#, python-brace-format
msgid "Neue Stellenanzeige {jobad_title} wurde hochgeladen"
msgstr ""

#: .\src\email_service\services.py:157
#, fuzzy, python-brace-format
#| msgid "Candidate Imported: {candidate_name}"
msgid "Kandidat akzeptiert ohne Interview/Probetag: {candidate_name}"
msgstr "Kandidat importiert: {candidate_name}"

#: .\src\email_service\services.py:174
#, python-brace-format
msgid "Einladung Interview/Probetag – Kandidat {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:194
#, python-brace-format
msgid "Interview/Probetag muss verschoben werden {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:214
#, python-brace-format
msgid "Interview/Probetag akzeptiert – Kandidat {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:233
#, python-brace-format
msgid "Zusage erteilt – Kandidat {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:265
#, python-brace-format
msgid "Zusage {candidate_name} Start {start_date}"
msgstr ""

#: .\src\email_service\services.py:291
#, python-brace-format
msgid "{candidate_name} hat seinen Einsatz begonnen"
msgstr ""

#: .\src\email_service\services.py:306
#, python-brace-format
msgid "Absage – Kandidat: {candidate_name}"
msgstr ""

#: .\src\email_service\services.py:320
#, python-brace-format
msgid "New User Registered: {user_name}"
msgstr ""

#: .\src\email_service\services.py:347
#, python-brace-format
msgid "New User in Linked Company: {linked_company_name}"
msgstr ""

#: .\src\email_service\services.py:369 .\src\jobad\views.py:968
#, python-brace-format
msgid "Statusänderung: Stellenanzeige: {jobad_title}, jetzt {jobad_status}"
msgstr ""

#: .\src\jobad\forms.py:17 .\src\jobad\models.py:283
#: .\src\templates\candidate\list.html:143
#: .\src\templates\candidate\tab\details_tab.html:88
#: .\src\templates\index.html:268
#: .\src\templates\jobad\_jobad_list_partial.html:11
#: .\src\templates\jobad\add.html:28 .\src\templates\jobad\index.html:38
#: .\src\templates\jobad\index.html:218 .\src\templates\jobad\index.html:287
#: .\src\templates\jobad\jobad_detail.html:148
#: .\src\templates\jobad\tab\candidate_profile.html:103
#: .\src\templates\jobad\tab\candidate_profile.html:215
#: .\src\templates\jobad\tab\history.html:25
#: .\src\templates\jobad\tab\history.html:74
#: .\src\templates\jobad\tab\worker_deployment.html:31
#: .\src\templates\userprofiles\_user_registration_modal.html:100
#: .\src\templates\userprofiles\profile.html:153
msgid "Company"
msgstr "Firma"

#: .\src\jobad\forms.py:18 .\src\jobad\models.py:289
#: .\src\templates\company\tabs\terms.html:15 .\src\templates\index.html:267
msgid "Title"
msgstr "Titel"

#: .\src\jobad\forms.py:19 .\src\jobad\models.py:280
#: .\src\templates\userprofiles\tab\details_tab.html:6
msgid "First Name"
msgstr "Vorname"

#: .\src\jobad\forms.py:20 .\src\jobad\models.py:281
#: .\src\templates\jobad\tab\candidate_profile.html:163
#: .\src\templates\userprofiles\tab\details_tab.html:10
msgid "Last Name"
msgstr "Nachname"

#: .\src\jobad\forms.py:21 .\src\jobad\models.py:282
#: .\src\templates\jobad\tab\candidate_profile.html:164
msgid "Date of Birth"
msgstr "Geburtsdatum"

#: .\src\jobad\forms.py:22 .\src\jobad\models.py:287
#: .\src\templates\userprofiles\_user_registration_modal.html:185
msgid "Origin"
msgstr "Nationalität"

#: .\src\jobad\forms.py:24
msgid "Hourly Rate €"
msgstr "Verrechnungssatz €"

#: .\src\jobad\forms.py:25 .\src\templates\candidate\list.html:180
#: .\src\templates\candidate\tab\documents_tab.html:25
#: .\src\templates\jobad\tab\candidate_profile.html:272
msgid "CV"
msgstr "Lebenslauf"

#: .\src\jobad\forms.py:26
msgid "Profile Picture"
msgstr "Profilbild"

#: .\src\jobad\forms.py:100
msgid "Custom position title"
msgstr "Custom Position"

#: .\src\jobad\forms.py:180
msgid "Please either select an existing position or enter one above."
msgstr ""
"Bitte wählen Sie entweder eine vorhandene Position aus oder geben Sie oben "
"eine ein."

#: .\src\jobad\models.py:76
msgid "Mr"
msgstr "Männlich"

#: .\src\jobad\models.py:77
msgid "Mrs"
msgstr "Weiblich"

#: .\src\jobad\models.py:78
msgid "Divers"
msgstr "Divers"

#: .\src\jobad\models.py:82
msgid "Deployment finished"
msgstr "Einsatz beendet"

#: .\src\jobad\models.py:83
msgid "In Deployment"
msgstr "Im Einsatz"

#: .\src\jobad\models.py:84
#, fuzzy
#| msgid "Requested"
msgid "Extension Requested"
msgstr "Beantragt"

#: .\src\jobad\models.py:85
msgid "Extended"
msgstr "Erweitert"

#: .\src\jobad\models.py:86
msgid "Transfer requested"
msgstr "Übertragung beantragt"

#: .\src\jobad\models.py:87
msgid "Transferred"
msgstr "Übertragen"

#: .\src\jobad\models.py:88
msgid "Paused"
msgstr "Pausiert"

#: .\src\jobad\models.py:89
msgid "Resumed"
msgstr "Fortgesetzt"

#: .\src\jobad\models.py:90
msgid "Deployment discontinued"
msgstr "Einsatz beendet"

#: .\src\jobad\models.py:91
msgid "Deployment discontinued as supplier"
msgstr "Einsatz beenden als Lieferant"

#: .\src\jobad\models.py:92
msgid "Internal Deployment"
msgstr "Interner Einsatz"

#: .\src\jobad\models.py:93
msgid "Internally Deployed"
msgstr "Innerbetrieblich eingesetzt"

#: .\src\jobad\models.py:94
msgid "Internal Deployment Accepted"
msgstr "Interne Bereitstellung Akzeptiert"

#: .\src\jobad\models.py:300
msgid "Hourly Rate"
msgstr "Verrechnungssatz"

#: .\src\jobad\utils\stage.py:4
msgid "Requested"
msgstr "Beantragt"

#: .\src\jobad\utils\stage.py:5
msgid "Approved"
msgstr "Genehmigt"

#: .\src\jobad\utils\stage.py:6 .\src\templates\jobad\jobad_detail.html:411
msgid "Rejected"
msgstr "Abgelehnt"

#: .\src\jobad\utils\stage.py:7
msgid "Interview Proposal"
msgstr "Interview angefragt"

#: .\src\jobad\utils\stage.py:8
msgid "Interview Completed"
msgstr "Interview bestätigt"

#: .\src\jobad\utils\stage.py:9
msgid "Interview Rejected"
msgstr "Interview abgelehnt"

#: .\src\jobad\utils\stage.py:10
msgid "Candidate Selection"
msgstr "Kandidat bestätigen / ablehnen"

#: .\src\jobad\utils\stage.py:11
msgid "Tentatively Occupied"
msgstr "Vorläufig besetzt"

#: .\src\jobad\utils\stage.py:12
msgid "Candidate Selection Rejected"
msgstr "Kandidat abgelehnt"

#: .\src\jobad\utils\stage.py:13 .\src\templates\jobad\jobad_detail.html:410
msgid "Completed"
msgstr "Abgeschlossen"

#: .\src\jobad\utils\stage.py:14
msgid "Canceled"
msgstr "Abbrechen"

#: .\src\jobad\utils\stage.py:15
msgid "Selection Rejected"
msgstr "Datensatz auswählen"

#: .\src\jobad\utils\stage.py:16 .\src\jobad\utils\stage.py:19
msgid "Offen"
msgstr ""

#: .\src\jobad\utils\stage.py:17
msgid "Besetzt"
msgstr ""

#: .\src\jobad\utils\stage.py:18
msgid "Withdrawn"
msgstr "Zurückgezogen"

#: .\src\jobad\utils\stage.py:20
msgid "Retry"
msgstr "Wiederholung"

#: .\src\templates\_base_frontend.html:20
#: .\src\templates\_base_frontend.html:66
#: .\src\templates\_base_frontend.html:94
msgid "Dashboard"
msgstr "Dashboard"

#: .\src\templates\_base_frontend.html:67
#: .\src\templates\_base_frontend.html:99 .\src\templates\index.html:183
#: .\src\templates\jobad\index.html:5
msgid "Job ads"
msgstr "Job Anzeigen"

#: .\src\templates\_base_frontend.html:68
#: .\src\templates\_base_frontend.html:104
#: .\src\templates\candidate\list.html:52
#: .\src\templates\candidate\list.html:65 .\src\templates\index.html:181
msgid "Candidates"
msgstr "Bewerber"

#: .\src\templates\_base_frontend.html:69
#: .\src\templates\_base_frontend.html:110
msgid "Companies"
msgstr "Unternehmen"

#: .\src\templates\_base_frontend.html:70
#: .\src\templates\_base_frontend.html:116
msgid "Suppliers"
msgstr "Lieferanten"

#: .\src\templates\_base_frontend.html:71
#: .\src\templates\_base_frontend.html:121
msgid "Master Vendors"
msgstr "Master Vendor"

#: .\src\templates\_base_frontend.html:77
#: .\src\templates\_base_frontend.html:129
#: .\src\templates\candidate\candidate_detail.html:24
#: .\src\templates\company\company_detail.html:46
#: .\src\templates\company\documents_overview.html:56
#: .\src\templates\company\documents_overview.html:112
#: .\src\templates\jobad\tab\worker_deployment.html:72
msgid "Documents"
msgstr "Dokumente"

#: .\src\templates\_base_frontend.html:81
#: .\src\templates\_base_frontend.html:145
msgid "Users"
msgstr "Benutzer"

#: .\src\templates\_base_frontend.html:138
#, fuzzy
#| msgid "Excel Report"
msgid "Reports"
msgstr "Excel-Bericht"

#: .\src\templates\_base_frontend.html:197
#: .\src\templates\userprofiles\profile_settings.html:3
msgid "Profile Settings"
msgstr "Profil Einstellungen"

#: .\src\templates\_base_frontend.html:218
msgid "All rights reserved."
msgstr "Alle Rechte vorbehalten."

#: .\src\templates\candidate\_candidate_list_partial.html:24
#: .\src\templates\candidate\list.html:96
msgid "From:"
msgstr "Von:"

#: .\src\templates\candidate\_candidate_list_partial.html:37
#: .\src\templates\candidate\list.html:108
#: .\src\templates\jobad\_jobad_list_partial.html:41
msgid "Load more"
msgstr ""

#: .\src\templates\candidate\add.html:3 .\src\templates\candidate\list.html:63
msgid "Add Candidate"
msgstr "Kandidat hinzufügen"

#: .\src\templates\candidate\add.html:8
msgid "Add New Candidate"
msgstr "Neue Bewerber hinzufügen"

#: .\src\templates\candidate\candidate_detail.html:4
msgid "Candidate Details"
msgstr "Bewerberdetails"

#: .\src\templates\candidate\candidate_detail.html:18
#: .\src\templates\company\company_detail.html:22
#: .\src\templates\jobad\jobad_detail.html:114
#: .\src\templates\userprofiles\profile_settings.html:17
msgid "Details"
msgstr "Details"

#: .\src\templates\candidate\list.html:68
msgid "Search candidates..."
msgstr "Kandidaten suchen..."

#: .\src\templates\candidate\list.html:113
#: .\src\templates\jobad\tab\history.html:59
msgid "No candidates found"
msgstr "Keine Kandidaten gefunden"

#: .\src\templates\candidate\list.html:121
#: .\src\templates\jobad\tab\candidate_profile.html:197
msgid "Create Candidate"
msgstr "Kandidat erstellen"

#: .\src\templates\candidate\list.html:146
#: .\src\templates\jobad\tab\candidate_profile.html:225
#, fuzzy
#| msgid "Search candidates..."
msgid "Search companies..."
msgstr "Kandidaten suchen..."

#: .\src\templates\candidate\list.html:186
#: .\src\templates\candidate\tab\documents_tab.html:26
#: .\src\templates\jobad\tab\candidate_profile.html:287
msgid "Work Permit"
msgstr "Arbeitserlaubnis"

#: .\src\templates\candidate\list.html:192 .\src\templates\company\add.html:10
#: .\src\templates\company\add.html:32 .\src\templates\company\list.html:42
#: .\src\templates\company\list.html:74
#: .\src\templates\jobad\tab\candidate_profile.html:294
msgid "Create"
msgstr "Erstellen"

#: .\src\templates\candidate\list.html:193
#: .\src\templates\candidate\tab\details_tab.html:123
#: .\src\templates\candidate\tab\documents_tab.html:48
#: .\src\templates\candidate\tab\documents_tab.html:143
#: .\src\templates\company\add.html:33
#: .\src\templates\company\documents_overview.html:251
#: .\src\templates\company\documents_overview.html:360
#: .\src\templates\company\list.html:75
#: .\src\templates\company\tabs\_user_registration.html:141
#: .\src\templates\company\tabs\documents_tab.html:31
#: .\src\templates\company\tabs\position_list_view.html:26
#: .\src\templates\company\tabs\position_list_view.html:91
#: .\src\templates\jobad\jobad_detail.html:341
#: .\src\templates\jobad\jobad_detail.html:385
#: .\src\templates\jobad\jobad_detail.html:416
#: .\src\templates\jobad\jobad_detail.html:538
#: .\src\templates\jobad\tab\_edit_modal.html:1190
#: .\src\templates\jobad\tab\candidate_profile.html:187
#: .\src\templates\jobad\tab\candidate_profile.html:295
#: .\src\templates\jobad\tab\worker_deployment.html:125
#: .\src\templates\jobad\tab\worker_deployment.html:163
#: .\src\templates\userprofiles\_user_registration_modal.html:164
msgid "Cancel"
msgstr "Abbrechen"

#: .\src\templates\candidate\tab\details_tab.html:113
msgid "Delete Candidate"
msgstr "Kandidat löschen"

#: .\src\templates\candidate\tab\details_tab.html:114
#: .\src\templates\company\company_edit.html:24
#: .\src\templates\company\tabs\details_tab.html:65
#: .\src\templates\userprofiles\tab\details_tab.html:35
msgid "Save Changes"
msgstr "Änderungen speichern"

#: .\src\templates\candidate\tab\details_tab.html:120
#: .\src\templates\candidate\tab\documents_tab.html:123
#, fuzzy
#| msgid "Confirm Rejection"
msgid "Confirm Deletion"
msgstr "Ablehnung bestätigen"

#: .\src\templates\candidate\tab\details_tab.html:121
#, fuzzy
#| msgid "Are you sure you want to delete this Jobad?"
msgid "Are you sure you want to delete this candidate?"
msgstr "Sind Sie sicher, dass Sie diese Jobad löschen möchten?"

#: .\src\templates\candidate\tab\documents_tab.html:7
#: .\src\templates\candidate\tab\documents_tab.html:15
#: .\src\templates\company\documents_overview.html:208
#: .\src\templates\company\documents_overview.html:327
#: .\src\templates\company\tabs\documents_tab.html:17
#: .\src\templates\company\tabs\documents_tab.html:24
msgid "Upload Document"
msgstr "Dokument hochladen"

#: .\src\templates\candidate\tab\documents_tab.html:22
msgid "Document Type"
msgstr "Art des Dokuments"

#: .\src\templates\candidate\tab\documents_tab.html:27
#: .\src\templates\jobad\tab\candidate_profile.html:282
msgid "Car License"
msgstr "Führerschein"

#: .\src\templates\candidate\tab\documents_tab.html:28
#: .\src\templates\jobad\tab\candidate_profile.html:277
msgid "Forklift License"
msgstr "Staplerschein"

#: .\src\templates\candidate\tab\documents_tab.html:32
#, fuzzy
#| msgid "Choose Supplier"
msgid "Choose File"
msgstr "Lieferant wählen"

#: .\src\templates\candidate\tab\documents_tab.html:38
#: .\src\templates\candidate\tab\documents_tab.html:73
#: .\src\templates\company\documents_overview.html:172
#: .\src\templates\company\documents_overview.html:291
#: .\src\templates\company\tabs\documents_tab.html:54
msgid "Expiration Date"
msgstr "Ablaufdatum"

#: .\src\templates\candidate\tab\documents_tab.html:45
#: .\src\templates\company\tabs\documents_tab.html:30
msgid "Save Document"
msgstr "Dokument speichern"

#: .\src\templates\candidate\tab\documents_tab.html:63
#: .\src\templates\company\tabs\documents_tab.html:44
msgid "Drop your files here"
msgstr "Dateien hierher ziehen"

#: .\src\templates\candidate\tab\documents_tab.html:70
#: .\src\templates\company\documents_overview.html:169
#: .\src\templates\company\documents_overview.html:288
#: .\src\templates\company\tabs\documents_tab.html:51
msgid "File Name"
msgstr "Dateiname"

#: .\src\templates\candidate\tab\documents_tab.html:71
#: .\src\templates\company\documents_overview.html:170
#: .\src\templates\company\documents_overview.html:289
#: .\src\templates\company\tabs\documents_tab.html:52
msgid "Uploaded By"
msgstr "Hochgeladen von"

#: .\src\templates\candidate\tab\documents_tab.html:72
#: .\src\templates\company\documents_overview.html:171
#: .\src\templates\company\documents_overview.html:290
#: .\src\templates\company\tabs\documents_tab.html:53
msgid "Uploaded On"
msgstr "Hochgeladen am"

#: .\src\templates\candidate\tab\documents_tab.html:97
#: .\src\templates\company\tabs\documents_tab.html:67
#: .\src\templates\jobad\jobad_detail.html:224
msgid "Download"
msgstr "Herunterladen"

#: .\src\templates\candidate\tab\documents_tab.html:112
#: .\src\templates\company\documents_overview.html:198
#: .\src\templates\company\tabs\documents_tab.html:82
msgid "No documents available."
msgstr "Keine Dokumente verfügbar."

#: .\src\templates\candidate\tab\documents_tab.html:124
#, fuzzy
#| msgid "Are you sure you want to delete this Jobad?"
msgid "Are you sure you want to delete this document?"
msgstr "Sind Sie sicher, dass Sie diese Jobad löschen möchten?"

#: .\src\templates\candidate\tab\documents_tab.html:140
#: .\src\templates\jobad\jobad_detail.html:382
#: .\src\templates\jobad\tab\_edit_modal.html:1078
#: .\src\templates\jobad\tab\_edit_modal.html:1164
#: .\src\templates\jobad\tab\worker_deployment.html:131
#: .\src\templates\jobad\tab\worker_deployment.html:166
msgid "Confirm"
msgstr "Bestätigen"

#: .\src\templates\candidate\tab\history.html:50
#: .\src\templates\company\tabs\structure.html:136
msgid "Save Structure"
msgstr "Struktur speichern"

#: .\src\templates\company\company_detail.html:4
msgid "Company Details"
msgstr "Firmendetails"

#: .\src\templates\company\company_detail.html:30
#: .\src\templates\company\tabs\position_list_view.html:52
msgid "Locations"
msgstr "Standorte"

#: .\src\templates\company\company_detail.html:38
msgid "Candidates/Employees"
msgstr "Kandidaten/Mitarbeiter"

#: .\src\templates\company\company_detail.html:54
msgid "Terms and Conditions"
msgstr "Allgemeine Geschäftsbedingungen"

#: .\src\templates\company\company_detail.html:63
msgid "Company Structure"
msgstr "Unternehmensstruktur"

#: .\src\templates\company\company_detail.html:73
msgid "Supplier Connection"
msgstr "Lieferanten-Anbindung"

#: .\src\templates\company\company_edit.html:3
msgid "Edit Company"
msgstr "Unternehmen bearbeiten"

#: .\src\templates\company\documents_overview.html:6
msgid "Document Overview"
msgstr "Dokumente"

#: .\src\templates\company\documents_overview.html:25
msgid "Search companies…"
msgstr "Unternehmen suchen..."

#: .\src\templates\company\documents_overview.html:68
#, fuzzy
#| msgid "Documents"
msgid "View All Documents"
msgstr "Dokumente"

#: .\src\templates\company\documents_overview.html:88
msgid "docs"
msgstr ""

#: .\src\templates\company\documents_overview.html:133
msgid "Admin"
msgstr ""

#: .\src\templates\company\documents_overview.html:133
msgid "Self"
msgstr ""

#: .\src\templates\company\documents_overview.html:158
#, fuzzy
#| msgid "Documents"
msgid "Document"
msgstr "Dokumente"

#: .\src\templates\company\documents_overview.html:223
#: .\src\templates\company\documents_overview.html:343
#, fuzzy
#| msgid "Select value"
msgid "Select File"
msgstr "Wähle Wert"

#: .\src\templates\company\documents_overview.html:228
msgid "Division"
msgstr ""

#: .\src\templates\company\documents_overview.html:230
msgid "None (Company-wide)"
msgstr ""

#: .\src\templates\company\documents_overview.html:317
#, fuzzy
#| msgid "No documents available."
msgid "No documents available for this division."
msgstr "Keine Dokumente verfügbar."

#: .\src\templates\company\documents_overview.html:373
msgid "Ihr Unternehmen hat keine Dokumente"
msgstr ""

#: .\src\templates\company\list.html:34
msgid "found"
msgstr "gefunden"

#: .\src\templates\company\tabs\_user_registration.html:79
#: .\src\templates\userprofiles\_user_registration_modal.html:92
msgid "Active account"
msgstr "Aktives Konto"

#: .\src\templates\company\tabs\_user_registration.html:81
#: .\src\templates\jobad\index.html:17
#: .\src\templates\userprofiles\_user_registration_modal.html:94
msgid "Active"
msgstr "Aktiv"

#: .\src\templates\company\tabs\_user_registration.html:82
#: .\src\templates\userprofiles\_user_registration_modal.html:95
msgid "Inactive"
msgstr "Inaktiv"

#: .\src\templates\company\tabs\_user_registration.html:87
#: .\src\templates\userprofiles\_user_registration_modal.html:110
#: .\src\templates\userprofiles\profile.html:161
msgid "Usergroup"
msgstr "Benutzergruppen"

#: .\src\templates\company\tabs\_user_registration.html:102
#: .\src\templates\userprofiles\_user_registration_modal.html:125
msgid "First name"
msgstr "Vorname"

#: .\src\templates\company\tabs\_user_registration.html:106
#: .\src\templates\userprofiles\_user_registration_modal.html:129
msgid "Last name"
msgstr "Nachname"

#: .\src\templates\company\tabs\_user_registration.html:113
#: .\src\templates\company\tabs\users.html:18
#: .\src\templates\userprofiles\_user_registration_modal.html:136
#: .\src\templates\userprofiles\profile.html:149
msgid "Username"
msgstr "Benutzername"

#: .\src\templates\company\tabs\_user_registration.html:117
#: .\src\templates\userprofiles\_user_registration_modal.html:140
msgid "Initial"
msgstr "Initial"

#: .\src\templates\company\tabs\_user_registration.html:123
#: .\src\templates\userprofiles\_user_registration_modal.html:146
msgid "E-Mail"
msgstr "E-Mail"

#: .\src\templates\company\tabs\_user_registration.html:128
#: .\src\templates\userprofiles\_user_registration_modal.html:151
#: .\src\templates\userprofiles\profile_settings.html:23
msgid "Password"
msgstr "Passwort*"

#: .\src\templates\company\tabs\_user_registration.html:133
#: .\src\templates\userprofiles\_user_registration_modal.html:156
msgid "Repeat Password"
msgstr "Passwort wiederholen"

#: .\src\templates\company\tabs\_user_registration.html:137
#: .\src\templates\userprofiles\_user_registration_modal.html:160
msgid "Passwords do not match!"
msgstr "Die Passwörter stimmen nicht überein!"

#: .\src\templates\company\tabs\_user_registration.html:140
#: .\src\templates\jobad\select_company.html:160
#: .\src\templates\userprofiles\_user_registration_modal.html:163
msgid "Continue"
msgstr "Weiter"

#: .\src\templates\company\tabs\_user_registration.html:153
#: .\src\templates\userprofiles\_user_registration_modal.html:176
msgid "Postal code"
msgstr "Postleitzahl"

#: .\src\templates\company\tabs\_user_registration.html:171
#: .\src\templates\userprofiles\_user_registration_modal.html:194
msgid "Mobile number"
msgstr "Handynummer"

#: .\src\templates\company\tabs\_user_registration.html:177
#: .\src\templates\userprofiles\_user_registration_modal.html:200
msgid "Back"
msgstr "Zurück"

#: .\src\templates\company\tabs\documents_tab.html:89
msgid "Drag and drop files here"
msgstr "Dateien hierher ziehen und ablegen"

#: .\src\templates\company\tabs\employees.html:31
msgid "Rejection Reasons"
msgstr "Gründe für die Ablehnung"

#: .\src\templates\company\tabs\employees.html:34
msgid "View as dropdown menu in job ads"
msgstr "Ansicht als Dropdown-Menü in Stellenanzeigen"

#: .\src\templates\company\tabs\employees.html:42
#: .\src\templates\company\tabs\employees.html:68
msgid "Add New Reason"
msgstr "Neuen Grund hinzufügen"

#: .\src\templates\company\tabs\employees.html:57
msgid "No reasons found."
msgstr "Keine Gründe gefunden."

#: .\src\templates\company\tabs\position_list_view.html:6
#: .\src\templates\index.html:269
msgid "Positions"
msgstr "Positionen"

#: .\src\templates\company\tabs\position_list_view.html:8
#: .\src\templates\company\tabs\position_list_view.html:54
msgid "will be enabled to choose when creating a job ad"
msgstr "können beim Erstellen einer Stellenanzeige ausgewählt werden"

#: .\src\templates\company\tabs\position_list_view.html:12
#: .\src\templates\company\tabs\position_list_view.html:18
msgid "Add New Position"
msgstr "Neue Position hinzufügen"

#: .\src\templates\company\tabs\position_list_view.html:25
msgid "Save Position"
msgstr "Position speichern"

#: .\src\templates\company\tabs\position_list_view.html:43
msgid "No positions found."
msgstr "Keine Positionen gefunden."

#: .\src\templates\company\tabs\position_list_view.html:58
#: .\src\templates\company\tabs\position_list_view.html:64
msgid "Add New Location"
msgstr "Neuen Standort hinzufügen"

#: .\src\templates\company\tabs\position_list_view.html:90
#, fuzzy
#| msgid "Save Position"
msgid "Save Location"
msgstr "Position speichern"

#: .\src\templates\company\tabs\position_list_view.html:108
msgid "No locations found."
msgstr "Keine Standorte gefunden."

#: .\src\templates\company\tabs\supplier.html:69
msgid "Existing Connections"
msgstr "Vorhandene Verbindungen"

#: .\src\templates\company\tabs\supplier.html:77
#, fuzzy
#| msgid "Select Supplier"
msgid "Search supplier..."
msgstr "Lieferant auswählen"

#: .\src\templates\company\tabs\supplier.html:91
msgid "Supplier:"
msgstr "Lieferant:"

#: .\src\templates\company\tabs\supplier.html:100
msgid "Overview"
msgstr "Übersicht"

#: .\src\templates\company\tabs\supplier.html:126
msgid "No connections yet."
msgstr "Noch keine Verbindungen."

#: .\src\templates\company\tabs\supplier.html:150
msgid "Add Connection"
msgstr "Neue Verbindung"

#: .\src\templates\company\tabs\supplier.html:159
msgid "Link Supplier Without Master Vendor"
msgstr "Verknüpfung von Lieferanten ohne Master Vendor"

#: .\src\templates\company\tabs\supplier.html:165
msgid "Link Supplier With Master Vendor"
msgstr "Verknüpfen Sie den Lieferanten mit dem Master Vendor"

#: .\src\templates\company\tabs\supplier.html:175
msgid "Select Supplier"
msgstr "Lieferant auswählen"

#: .\src\templates\company\tabs\supplier.html:177
msgid "Choose Supplier"
msgstr "Lieferant wählen"

#: .\src\templates\company\tabs\supplier.html:186
msgid "Select Master Vendor (Optional)"
msgstr "Hauptlieferant auswählen (optional)"

#: .\src\templates\company\tabs\supplier.html:188
msgid "Choose Master Vendor"
msgstr "Wählen Sie Master Vendor"

#: .\src\templates\company\tabs\supplier.html:196
msgid "Next"
msgstr "Weiter"

#: .\src\templates\company\tabs\supplier.html:202
msgid "Select Structure for Supplier"
msgstr "Struktur für Lieferanten auswählen"

#: .\src\templates\company\tabs\supplier.html:231
msgid "Link Supplier"
msgstr "Link zum Lieferanten"

#: .\src\templates\company\tabs\terms.html:7
msgid "Create new term"
msgstr "Neuen Begriff erstellen"

#: .\src\templates\company\tabs\terms.html:16
msgid "Version"
msgstr "Version"

#: .\src\templates\company\tabs\users.html:7
msgid "Create user"
msgstr "Benutzer anlegen"

#: .\src\templates\company\tabs\users.html:17
#: .\src\templates\userprofiles\profile.html:145
msgid "Name"
msgstr "Vorname"

#: .\src\templates\company\tabs\users.html:19
msgid "Role"
msgstr "Rolle"

#: .\src\templates\company\tabs\users.html:31
msgid "No users found."
msgstr "Keine Benutzer gefunden."

#: .\src\templates\company\upload_document.html:9
#: .\src\templates\jobad\tab\candidate_profile.html:64
msgid "Upload"
msgstr "Hochladen"

#: .\src\templates\index.html:87
msgid "Guest"
msgstr "Gast"

#: .\src\templates\index.html:89
msgid "Hello, Welcome back!"
msgstr "Hallo, Willkommen zurück!"

#: .\src\templates\index.html:106
msgid "Excel Report"
msgstr "Excel-Bericht"

#: .\src\templates\index.html:178
msgid "Choose Evaluation Type:"
msgstr "Wählen Sie den Auswertungstyp:"

#: .\src\templates\index.html:182
#: .\src\templates\jobad\tab\worker_deployment.html:14
msgid "Workers"
msgstr "Mitarbeiter"

#: .\src\templates\index.html:187
msgid "Select Company:"
msgstr "Unternehmen auswählen:"

#: .\src\templates\index.html:190
msgid "All Companies"
msgstr "Alle Firmen"

#: .\src\templates\index.html:200
msgid "From Date:"
msgstr "Von Datum:"

#: .\src\templates\index.html:205
msgid "To Date:"
msgstr "Bis Datum:"

#: .\src\templates\index.html:215
msgid "Generate Excel"
msgstr "Excel generieren"

#: .\src\templates\index.html:224
msgid "To-do-List"
msgstr "To-do-Liste"

#: .\src\templates\index.html:248
msgid "No pending actions."
msgstr "Weitere Aktionen"

#: .\src\templates\index.html:258
msgid "Job ad List"
msgstr "Offene Job Anzeigen"

#: .\src\templates\index.html:270 .\src\templates\index.html:316
#: .\src\templates\jobad\_jobad_list_partial.html:15
#: .\src\templates\jobad\index.html:129 .\src\templates\jobad\index.html:222
#: .\src\templates\jobad\index.html:291
#: .\src\templates\jobad\jobad_detail.html:188
msgid "Location"
msgstr "Standort"

#: .\src\templates\index.html:271 .\src\templates\index.html:317
#: .\src\templates\jobad\_jobad_list_partial.html:23
#: .\src\templates\jobad\index.html:230 .\src\templates\jobad\index.html:299
#: .\src\templates\jobad\jobad_detail.html:209
msgid "Department"
msgstr "Abteilung"

#: .\src\templates\index.html:295
msgid "No job ads available."
msgstr "Keine Stellenanzeigen verfügbar."

#: .\src\templates\index.html:305
msgid "New Applications"
msgstr "Offene Profile"

#: .\src\templates\index.html:314
#: .\src\templates\jobad\tab\candidate_profile.html:162
msgid "Candidate"
msgstr "Bewerber"

#: .\src\templates\index.html:315 .\src\templates\jobad\add.html:45
msgid "Position"
msgstr "Position"

#: .\src\templates\index.html:318
msgid "Created"
msgstr "Erstellt"

#: .\src\templates\index.html:343
msgid "No candidate available."
msgstr "Kein Kandidat verfügbar."

#: .\src\templates\jobad\_jobad_list_partial.html:7
#: .\src\templates\jobad\index.html:211 .\src\templates\jobad\index.html:280
msgid "STATUS:"
msgstr "STATUS:"

#: .\src\templates\jobad\_jobad_list_partial.html:19
#: .\src\templates\jobad\index.html:226 .\src\templates\jobad\index.html:295
#: .\src\templates\jobad\jobad_detail.html:432
msgid "Open Positions"
msgstr "Offene Positionen"

#: .\src\templates\jobad\_jobad_list_partial.html:27
#: .\src\templates\jobad\index.html:234 .\src\templates\jobad\index.html:303
msgid "Filled Positions"
msgstr "Besetzte Positionen"

#: .\src\templates\jobad\_jobad_list_partial.html:28
#: .\src\templates\jobad\index.html:235 .\src\templates\jobad\index.html:304
msgid "of"
msgstr "von"

#: .\src\templates\jobad\add.html:3
msgid "Add Jobad"
msgstr "Job Anzeige hinzufügen"

#: .\src\templates\jobad\add.html:17
#: .\src\templates\jobad\select_company.html:107
msgid "Create Jobad"
msgstr "Job Anzeige erstellen"

#: .\src\templates\jobad\add.html:33
msgid "Use saved template"
msgstr "Gespeicherte Vorlage nutzen"

#: .\src\templates\jobad\add.html:37
msgid "— Templates —"
msgstr "— Vorlagen —"

#: .\src\templates\jobad\add.html:52
msgid "Enter position title"
msgstr "Position titel"

#: .\src\templates\jobad\add.html:66
msgid "Job Location"
msgstr "Arbeitsort"

#: .\src\templates\jobad\add.html:80
msgid "Weekly Working Hours (Hs)"
msgstr "Wöchentliche Arbeitszeit in Stunden"

#: .\src\templates\jobad\add.html:81
msgid "Enter working hours"
msgstr "Wöchentliche Arbeitszeit eingeben in Stunden"

#: .\src\templates\jobad\add.html:84
msgid "Time Period (Monate)"
msgstr "Geplante Einsatzdauer in Monaten"

#: .\src\templates\jobad\add.html:85
msgid "Deployment time in months"
msgstr "Einsatzdauer in Monaten"

#: .\src\templates\jobad\add.html:92
#: .\src\templates\jobad\jobad_detail.html:269
#: .\src\templates\jobad\jobad_detail.html:292
msgid "Tasks"
msgstr "Aufgaben"

#: .\src\templates\jobad\add.html:93
msgid "List the tasks"
msgstr "die Aufgaben auflisten"

#: .\src\templates\jobad\add.html:96
#: .\src\templates\jobad\jobad_detail.html:250
#: .\src\templates\jobad\jobad_detail.html:288
msgid "Requirements"
msgstr "Anforderungen"

#: .\src\templates\jobad\add.html:97
msgid "List the requirements"
msgstr "die Anforderungen auflisten"

#: .\src\templates\jobad\add.html:103
#: .\src\templates\jobad\jobad_detail.html:234
#: .\src\templates\jobad\jobad_detail.html:238
msgid "Description"
msgstr "Beschreibung"

#: .\src\templates\jobad\add.html:104
msgid "List the description"
msgstr "Beschreibung hinzufügen"

#: .\src\templates\jobad\add.html:110
msgid "Contact Information"
msgstr "Kontaktinformationen"

#: .\src\templates\jobad\add.html:113
msgid "Contact Name"
msgstr "Ansprechpartner"

#: .\src\templates\jobad\add.html:114
msgid "Contact Phone"
msgstr "Telefonnummer"

#: .\src\templates\jobad\add.html:124
#: .\src\templates\jobad\jobad_detail.html:311
msgid "Working Hour Shifts"
msgstr "Arbeitszeiten"

#: .\src\templates\jobad\add.html:127
#, fuzzy
#| msgid "Start time:"
msgid "Start time (HH:MM)"
msgstr "Beginn:"

#: .\src\templates\jobad\add.html:128
msgid "End time (HH:MM)"
msgstr "End:"

#: .\src\templates\jobad\add.html:135
#: .\src\templates\jobad\jobad_detail.html:328
msgid "Add Shift"
msgstr "Schicht hinzufügen"

#: .\src\templates\jobad\add.html:138
msgid "Job Start Date"
msgstr "Startdatum des Auftrags"

#: .\src\templates\jobad\add.html:144
#, fuzzy
#| msgid "Start date:"
msgid "Select start date"
msgstr "Startdatum:"

#: .\src\templates\jobad\add.html:151
#, fuzzy
#| msgid "Upload Document"
msgid "Upload Job Ad Document"
msgstr "Dokument hochladen"

#: .\src\templates\jobad\add.html:159
msgid "Number of Employees"
msgstr "Anzahl der Mitarbeiter"

#: .\src\templates\jobad\add.html:160
msgid "Enter total positions"
msgstr "Benötigte Mitarbeiteranzahl eingeben"

#: .\src\templates\jobad\add.html:163
msgid "Cost Center"
msgstr "Kostenstelle"

#: .\src\templates\jobad\add.html:164
msgid "Enter cost department"
msgstr "Kostenstelle eingeben"

#: .\src\templates\jobad\add.html:167
#: .\src\templates\jobad\jobad_detail.html:459
msgid "Employee Group"
msgstr "Mitarbeitergruppe"

#: .\src\templates\jobad\add.html:169 .\src\templates\jobad\index.html:91
#: .\src\templates\jobad\jobad_detail.html:463
#: .\src\templates\jobad\jobad_detail.html:473
msgid "Office"
msgstr "Kaufmännisch"

#: .\src\templates\jobad\add.html:170 .\src\templates\jobad\index.html:92
#: .\src\templates\jobad\jobad_detail.html:465
#: .\src\templates\jobad\jobad_detail.html:474
msgid "Production"
msgstr "Gewerblich"

#: .\src\templates\jobad\add.html:174
msgid "Add Creator"
msgstr "Ersteller hinzufügen"

#: .\src\templates\jobad\add.html:193
msgid "Car License?"
msgstr "Führerschein"

#: .\src\templates\jobad\add.html:232
msgid "Save as template…"
msgstr "Als Vorlage speichern"

#: .\src\templates\jobad\add.html:250
msgid "Save + Create Template"
msgstr "Speichern + Vorlage erstellen"

#: .\src\templates\jobad\add.html:251
msgid "Save Jobad"
msgstr "Job Anzeige erstellen"

#: .\src\templates\jobad\index.html:27
msgid "Archived"
msgstr "Archiviert"

#: .\src\templates\jobad\index.html:42 .\src\templates\jobad\index.html:90
#: .\src\templates\jobad\index.html:132
msgid "Any"
msgstr "Alles"

#: .\src\templates\jobad\index.html:77 .\src\templates\jobad\index.html:119
#: .\src\templates\jobad\index.html:162
msgid "Apply"
msgstr "Anwenden"

#: .\src\templates\jobad\index.html:175
msgid "Search jobads..."
msgstr "Suche Jobanzeigen..."

#: .\src\templates\jobad\index.html:189
msgid "Sort By"
msgstr "Sortieren nach"

#: .\src\templates\jobad\index.html:196
msgid "+ Add Jobad"
msgstr "+ Job Anzeige hinzufügen"

#: .\src\templates\jobad\index.html:246 .\src\templates\jobad\index.html:315
#: .\src\templates\jobad\jobad_detail.html:119
msgid "Candidate Profile"
msgstr "Bewerberprofile"

#: .\src\templates\jobad\index.html:250 .\src\templates\jobad\index.html:319
#: .\src\templates\jobad\jobad_detail.html:124
msgid "Worker Deployment"
msgstr "Aktive Mitarbeiter"

#: .\src\templates\jobad\index.html:262 .\src\templates\jobad\index.html:331
#, fuzzy
#| msgid "Add Creator"
msgid "No creator"
msgstr "Ersteller hinzufügen"

#: .\src\templates\jobad\index.html:268
#, fuzzy
#| msgid "No jobads found"
msgid "No active job ads found"
msgstr "Keine Job Anzeige gefunden"

#: .\src\templates\jobad\index.html:337
#, fuzzy
#| msgid "No jobads found"
msgid "No archived job ads found"
msgstr "Keine Job Anzeige gefunden"

#: .\src\templates\jobad\jobad_detail.html:157
msgid "Months"
msgstr "Geplante Einsatzdauer"

#: .\src\templates\jobad\jobad_detail.html:171
#: .\src\templates\jobad\tab\_edit_modal.html:1133
msgid "Start Date"
msgstr "Startdatum"

#: .\src\templates\jobad\jobad_detail.html:196
msgid "Weekly Working Hours"
msgstr "Wöchentliche Arbeitszeit"

#: .\src\templates\jobad\jobad_detail.html:220
#, fuzzy
#| msgid "Upload Document"
msgid "Download this document"
msgstr "Dokument hochladen"

#: .\src\templates\jobad\jobad_detail.html:299
msgid "Shifts"
msgstr "Schichten"

#: .\src\templates\jobad\jobad_detail.html:315
#: .\src\templates\jobad\tab\_edit_modal.html:819
#: .\src\templates\jobad\tab\_edit_modal.html:834
#: .\src\templates\jobad\tab\_edit_modal.html:981
msgid "Start Time"
msgstr "Startzeit"

#: .\src\templates\jobad\jobad_detail.html:319
#: .\src\templates\jobad\tab\_edit_modal.html:823
#: .\src\templates\jobad\tab\_edit_modal.html:838
msgid "End Time"
msgstr "Endzeit"

#: .\src\templates\jobad\jobad_detail.html:361
msgid "Approve Jobad"
msgstr "Job Anzeige genehmigen"

#: .\src\templates\jobad\jobad_detail.html:364
msgid "Reject Jobad"
msgstr "Job Anzeige ablehnen"

#: .\src\templates\jobad\jobad_detail.html:374
msgid "You are about to reject a job ad"
msgstr "Sie sind dabei, eine Jobausschreibung abzulehnen."

#: .\src\templates\jobad\jobad_detail.html:377
msgid "Are you sure?"
msgstr "Sind Sie sicher?"

#: .\src\templates\jobad\jobad_detail.html:395
msgid "Change Status"
msgstr "Status ändern"

#: .\src\templates\jobad\jobad_detail.html:403
msgid "Change Jobad Status"
msgstr "Job Anzeige-Status ändern"

#: .\src\templates\jobad\jobad_detail.html:406
msgid "Select New Status"
msgstr "Wählen Sie Neuer Status aus"

#: .\src\templates\jobad\jobad_detail.html:408
msgid "Open for Everyone"
msgstr "Offen"

#: .\src\templates\jobad\jobad_detail.html:409
msgid "Filled"
msgstr "Besetzt"

#: .\src\templates\jobad\jobad_detail.html:439
msgid "Total"
msgstr "Gesamt"

#: .\src\templates\jobad\jobad_detail.html:450
msgid "Cost Center:"
msgstr "Kostenstelle"

#: .\src\templates\jobad\jobad_detail.html:479
msgid "Driver License:"
msgstr "Führerschein:"

#: .\src\templates\jobad\jobad_detail.html:482
msgid "No License"
msgstr "Keine Lizenz"

#: .\src\templates\jobad\jobad_detail.html:484
msgid "Car, Forklift"
msgstr "Führerschein, Staplerschein"

#: .\src\templates\jobad\jobad_detail.html:486
msgid "Car"
msgstr "Führerschein"

#: .\src\templates\jobad\jobad_detail.html:488
msgid "Forklift"
msgstr "Staplerschein"

#: .\src\templates\jobad\jobad_detail.html:532
msgid "Are you sure you want to delete this Jobad?"
msgstr "Sind Sie sicher, dass Sie diese Jobad löschen möchten?"

#: .\src\templates\jobad\jobad_detail.html:533
msgid ""
"This action cannot be undone and will permanently delete all related "
"information."
msgstr ""
"Diese Aktion kann nicht rückgängig gemacht werden und löscht alle "
"zugehörigen Informationen dauerhaft."

#: .\src\templates\jobad\tab\_edit_modal.html:2
#: .\src\templates\jobad\tab\_edit_modal.html:552
#: .\src\templates\jobad\tab\_edit_modal.html:575
#: .\src\templates\jobad\tab\_edit_modal.html:917
msgid "Your candidate has been rejected"
msgstr "Ihr Kandidat wurde abgelehnt"

#: .\src\templates\jobad\tab\_edit_modal.html:3
msgid "Deployment has been confirmed"
msgstr "Einsatz wurde bestätigt"

#: .\src\templates\jobad\tab\_edit_modal.html:549
msgid "Candidate approved"
msgstr "Kandidat genehmigt"

#: .\src\templates\jobad\tab\_edit_modal.html:554
msgid "Your candidate has been accepted"
msgstr "Ihr Kandidat wurde genehmigt"

#: .\src\templates\jobad\tab\_edit_modal.html:570
msgid "Arrange interview/trial day"
msgstr "Interview/Probetag vereinbaren"

#: .\src\templates\jobad\tab\_edit_modal.html:572
msgid "Interview has been skipped"
msgstr "Interview wurde übersprungen"

#: .\src\templates\jobad\tab\_edit_modal.html:581
msgid "Date:"
msgstr "Date:"

#: .\src\templates\jobad\tab\_edit_modal.html:590
msgid "Choose one of the proposed dates or suggest an alternative."
msgstr ""
"Wählen Sie einen der vorgeschlagenen Termine oder schlagen Sie "
"einenalternativen Termin vor."

#: .\src\templates\jobad\tab\_edit_modal.html:596
msgid "Date 1:"
msgstr "Termin 1:"

#: .\src\templates\jobad\tab\_edit_modal.html:600
#: .\src\templates\jobad\tab\_edit_modal.html:610
msgid "O'clock"
msgstr "Uhr"

#: .\src\templates\jobad\tab\_edit_modal.html:606
msgid "Date 2:"
msgstr "Termin 2:"

#: .\src\templates\jobad\tab\_edit_modal.html:623
msgid "Suggest alternate interview date"
msgstr "Schlagen Sie einen alternativen Interview/Probetag termin vor"

#: .\src\templates\jobad\tab\_edit_modal.html:631
#: .\src\templates\jobad\tab\_edit_modal.html:749
#: .\src\templates\jobad\tab\_edit_modal.html:778
msgid "Alternative Date"
msgstr "Alternatives Datum"

#: .\src\templates\jobad\tab\_edit_modal.html:635
#: .\src\templates\jobad\tab\_edit_modal.html:753
#: .\src\templates\jobad\tab\_edit_modal.html:782
msgid "Alternative Start"
msgstr "Alternativer Start"

#: .\src\templates\jobad\tab\_edit_modal.html:639
#: .\src\templates\jobad\tab\_edit_modal.html:757
#: .\src\templates\jobad\tab\_edit_modal.html:786
msgid "Alternative End"
msgstr "Alternatives Ende"

#: .\src\templates\jobad\tab\_edit_modal.html:717
#: .\src\templates\jobad\tab\_edit_modal.html:854
msgid "Send"
msgstr "Senden"

#: .\src\templates\jobad\tab\_edit_modal.html:721
msgid "Your interview suggestions have been sent:"
msgstr "Ihre Interviewvorschläge wurden gesendet:"

#: .\src\templates\jobad\tab\_edit_modal.html:724
msgid "Date 1: "
msgstr "Termin 1: "

#: .\src\templates\jobad\tab\_edit_modal.html:727
msgid "Date 2: "
msgstr "Termin 2: "

#: .\src\templates\jobad\tab\_edit_modal.html:730
msgid "Please wait for the supplier's response."
msgstr "Bitte warten Sie auf die Antwort des Lieferanten."

#: .\src\templates\jobad\tab\_edit_modal.html:740
msgid "The supplier has suggested a new date:"
msgstr "Der Lieferant hat einen neuen Termin vorgeschlagen:"

#: .\src\templates\jobad\tab\_edit_modal.html:741
#: .\src\templates\jobad\tab\_edit_modal.html:770
#: .\src\templates\jobad\tab\_edit_modal.html:799
msgid "from "
msgstr "von "

#: .\src\templates\jobad\tab\_edit_modal.html:742
#: .\src\templates\jobad\tab\_edit_modal.html:771
#: .\src\templates\jobad\tab\_edit_modal.html:800
msgid "to "
msgstr "bis "

#: .\src\templates\jobad\tab\_edit_modal.html:745
#: .\src\templates\jobad\tab\_edit_modal.html:774
msgid "Please confirm or suggest another date"
msgstr "Bitte bestätigen Sie oder schlagen Sie einen anderen Termin vor"

#: .\src\templates\jobad\tab\_edit_modal.html:763
#: .\src\templates\jobad\tab\_edit_modal.html:792
msgid "Accept Date"
msgstr "Datum akzeptieren"

#: .\src\templates\jobad\tab\_edit_modal.html:764
#: .\src\templates\jobad\tab\_edit_modal.html:793
msgid "Suggest Another"
msgstr "Schlagen Sie einen anderen vor"

#: .\src\templates\jobad\tab\_edit_modal.html:769
msgid "Alternative interview suggestion received: "
msgstr "Alternativer Interviewvorschlag: "

#: .\src\templates\jobad\tab\_edit_modal.html:798
msgid "Alternative interview suggestion sent: "
msgstr "Alternativer Interviewvorschlag gesendet: "

#: .\src\templates\jobad\tab\_edit_modal.html:803
msgid "Awaiting confirmation..."
msgstr "Warten auf Bestätigung..."

#: .\src\templates\jobad\tab\_edit_modal.html:812
msgid "Propose one or two interview dates and times to the supplier."
msgstr ""
"Schlagen Sie dem Lieferanten ein oder zwei Interview/Probetag termin vor."

#: .\src\templates\jobad\tab\_edit_modal.html:815
msgid "Date 1"
msgstr "Termin 1"

#: .\src\templates\jobad\tab\_edit_modal.html:830
msgid "Date 2"
msgstr "Termin 2"

#: .\src\templates\jobad\tab\_edit_modal.html:844
msgid "Contact Person Name"
msgstr "Ansprechpartner"

#: .\src\templates\jobad\tab\_edit_modal.html:848
msgid "Contact Person Phone"
msgstr "Telefonnummer"

#: .\src\templates\jobad\tab\_edit_modal.html:883
msgid "Skip interview"
msgstr "Vorstellungsgespräch überspringen"

#: .\src\templates\jobad\tab\_edit_modal.html:885
#: .\src\templates\jobad\tab\_edit_modal.html:1011
#: .\src\templates\jobad\tab\_edit_modal.html:1079
msgid "Reject profile"
msgstr "Profil ablehnen"

#: .\src\templates\jobad\tab\_edit_modal.html:889
msgid "No interviews have been arranged yet."
msgstr "Es wurden noch keine Interviews arrangiert."

#: .\src\templates\jobad\tab\_edit_modal.html:912
msgid "Candidate selection"
msgstr "Kandidat bestätigen / ablehnen"

#: .\src\templates\jobad\tab\_edit_modal.html:919
msgid "Your candidate has been selected"
msgstr "Ihr Kandidat wurde bestätigt"

#: .\src\templates\jobad\tab\_edit_modal.html:962
msgid "Select a start- and end date for the worker's contract."
msgstr "Wählen Sie ein Start- und Enddatum für den Kandidat aus."

#: .\src\templates\jobad\tab\_edit_modal.html:965
msgid "Begin"
msgstr "Start"

#: .\src\templates\jobad\tab\_edit_modal.html:969
#: .\src\templates\jobad\tab\_edit_modal.html:976
#: .\src\templates\jobad\tab\_edit_modal.html:985
#: .\src\templates\jobad\tab\_edit_modal.html:995
#: .\src\templates\jobad\tab\_edit_modal.html:1002
msgid "This field is required"
msgstr "Dies ist ein Pflichtfeld."

#: .\src\templates\jobad\tab\_edit_modal.html:972
msgid "End"
msgstr "Ende"

#: .\src\templates\jobad\tab\_edit_modal.html:988
msgid "Contact Person"
msgstr "Ansprechpartner"

#: .\src\templates\jobad\tab\_edit_modal.html:999
#: .\src\templates\jobad\tab\_edit_modal.html:1158
msgid "Phone"
msgstr "Telefon:"

#: .\src\templates\jobad\tab\_edit_modal.html:1009
msgid "Accept profile"
msgstr "Profil akzeptieren"

#: .\src\templates\jobad\tab\_edit_modal.html:1015
msgid "Customer has not yet entered a planned start and end date..."
msgstr "Der Kunde hat noch kein geplantes Start- und Enddatum eingegeben..."

#: .\src\templates\jobad\tab\_edit_modal.html:1024
#: .\src\templates\jobad\tab\_edit_modal.html:1067
#: .\src\templates\jobad\tab\_edit_modal.html:1086
msgid "Start date:"
msgstr "Startdatum:"

#: .\src\templates\jobad\tab\_edit_modal.html:1031
#: .\src\templates\jobad\tab\_edit_modal.html:1069
#: .\src\templates\jobad\tab\_edit_modal.html:1088
msgid "Start time:"
msgstr "Beginn:"

#: .\src\templates\jobad\tab\_edit_modal.html:1035
#: .\src\templates\jobad\tab\_edit_modal.html:1073
#: .\src\templates\jobad\tab\_edit_modal.html:1092
msgid "End date:"
msgstr "Enddatum:"

#: .\src\templates\jobad\tab\_edit_modal.html:1045
msgid "Please wait for approval."
msgstr "Bitte warten Sie auf die Antwort des Lieferanten."

#: .\src\templates\jobad\tab\_edit_modal.html:1051
msgid "Approve Candidate"
msgstr "Kandidaten freigeben"

#: .\src\templates\jobad\tab\_edit_modal.html:1054
#: .\src\templates\jobad\tab\_edit_modal.html:1177
msgid "Reject Profile"
msgstr "Profil ablehnen"

#: .\src\templates\jobad\tab\_edit_modal.html:1064
msgid "Candidate has been accepted. Please approve start- and end-date."
msgstr ""
"Der Kandidat wurde akzeptiert. Bitte bestätigen Sie das Start- und Enddatum."

#: .\src\templates\jobad\tab\_edit_modal.html:1068
#: .\src\templates\jobad\tab\_edit_modal.html:1087
msgid "Contact person:"
msgstr "Ansprechpartner:"

#: .\src\templates\jobad\tab\_edit_modal.html:1074
#: .\src\templates\jobad\tab\_edit_modal.html:1093
msgid "Phone:"
msgstr "Telefon:"

#: .\src\templates\jobad\tab\_edit_modal.html:1083
msgid "Candidate has been selected"
msgstr "Der Kandidat wurde ausgewählt"

#: .\src\templates\jobad\tab\_edit_modal.html:1121
msgid "Confirm start of deployment"
msgstr "Mitarbeiterstart bestätigen"

#: .\src\templates\jobad\tab\_edit_modal.html:1129
msgid "Fill in the exact start date."
msgstr "Geben Sie das tatsächliche Startdatum ein."

#: .\src\templates\jobad\tab\_edit_modal.html:1137
msgid "Planned start date: "
msgstr "Geplanter Starttermin: "

#: .\src\templates\jobad\tab\_edit_modal.html:1141
msgid "Planned end date: "
msgstr "Geplantes Enddatum: "

#: .\src\templates\jobad\tab\_edit_modal.html:1145
msgid "Planned start time: "
msgstr "Geplante Startzeit: "

#: .\src\templates\jobad\tab\_edit_modal.html:1168
msgid "Candidate has not started the deployment yet"
msgstr "Der Kandidat hat die Bereitstellung noch nicht gestartet"

#: .\src\templates\jobad\tab\_edit_modal.html:1178
msgid "Please select a reason for rejecting the profile:"
msgstr "Bitte wählen Sie einen Grund für die Ablehnung des Profils aus:"

#: .\src\templates\jobad\tab\_edit_modal.html:1182
msgid "No reason"
msgstr "Keine Gründe"

#: .\src\templates\jobad\tab\_edit_modal.html:1193
msgid "Confirm Rejection"
msgstr "Ablehnung bestätigen"

#: .\src\templates\jobad\tab\_edit_modal.html:1209
msgid "Application finished"
msgstr "Anwendung abgeschlossen"

#: .\src\templates\jobad\tab\_edit_modal.html:1213
msgid "Rejection Reason"
msgstr "Ablehnungsgrund"

#: .\src\templates\jobad\tab\_edit_modal.html:1222
#: .\src\templates\jobad\tab\_edit_modal.html:1227
msgid "Cancel Application"
msgstr "Bewerbung abbrechen"

#: .\src\templates\jobad\tab\_edit_modal.html:1228
msgid ""
"You are about to cancel the application of this candidate. Is that right?"
msgstr ""
"Sie sind dabei, die Bewerbung dieses Kandidaten zu stornieren. Stimmt das?"

#: .\src\templates\jobad\tab\_edit_modal.html:1231
msgid "Yes, Cancel"
msgstr "Ja, abbrechen"

#: .\src\templates\jobad\tab\_edit_modal.html:1234
msgid "No, Go Back"
msgstr "Nein, zurück gehen"

#: .\src\templates\jobad\tab\candidate_profile.html:41
msgid "Candidate Applied"
msgstr "Kandidat beworben"

#: .\src\templates\jobad\tab\candidate_profile.html:43
msgid "Candidates Applied"
msgstr "Beworbene Kandidaten"

#: .\src\templates\jobad\tab\candidate_profile.html:47
#: .\src\templates\jobad\tab\history.html:15
#: .\src\templates\jobad\tab\worker_deployment.html:18
msgid "Search..."
msgstr "Suchen..."

#: .\src\templates\jobad\tab\candidate_profile.html:91
#: .\src\templates\jobad\tab\history.html:24
#: .\src\templates\jobad\tab\history.html:73
#: .\src\templates\jobad\tab\worker_deployment.html:29
msgid "Full Name"
msgstr "Vollständiger Name"

#: .\src\templates\jobad\tab\candidate_profile.html:102
#: .\src\templates\jobad\tab\worker_deployment.html:30
msgid "VS"
msgstr ""

#: .\src\templates\jobad\tab\candidate_profile.html:104
#: .\src\templates\jobad\tab\history.html:26
msgid "Uploaded"
msgstr "Hochgeladen"

#: .\src\templates\jobad\tab\candidate_profile.html:105
#: .\src\templates\jobad\tab\history.html:27
#: .\src\templates\jobad\tab\history.html:76
#: .\src\templates\jobad\tab\worker_deployment.html:33
msgid "Status"
msgstr "Status"

#: .\src\templates\jobad\tab\candidate_profile.html:141
msgid "No candidates found for this job ad"
msgstr "Keine Kandidaten für diese Stellenanzeige gefunden"

#: .\src\templates\jobad\tab\candidate_profile.html:152
msgid "Import Candidates"
msgstr "Kandidaten importieren"

#: .\src\templates\jobad\tab\history.html:8
msgid "Candidate Rejected"
msgstr "Kandidat abgelehnt"

#: .\src\templates\jobad\tab\history.html:10
msgid "Candidates Rejected"
msgstr "Abgelehnte Kandidaten"

#: .\src\templates\jobad\tab\history.html:75
#: .\src\templates\jobad\tab\worker_deployment.html:32
msgid "Start Date / End Date"
msgstr "Startdatum / Enddatum"

#: .\src\templates\jobad\tab\history.html:107
#, fuzzy
#| msgid "No reasons found."
msgid "No worker history found"
msgstr "Keine Gründe gefunden."

#: .\src\templates\jobad\tab\worker_deployment.html:12
msgid "Worker"
msgstr "Arbeiter"

#: .\src\templates\jobad\tab\worker_deployment.html:46
#, fuzzy
#| msgid "Download"
msgid "Download CV"
msgstr "Herunterladen"

#: .\src\templates\jobad\tab\worker_deployment.html:75
msgid "Settings"
msgstr "Einstellungen"

#: .\src\templates\jobad\tab\worker_deployment.html:92
msgid "No workers found for this job ad"
msgstr "Für diese Stellenanzeige wurden keine Arbeitskräfte gefunden"

#: .\src\templates\jobad\tab\worker_deployment.html:107
msgid "Edit Worker"
msgstr "Mitarbeiter bearbeiten"

#: .\src\templates\jobad\tab\worker_deployment.html:108
msgid "What would you like to do?"
msgstr "Was möchtest Sie tun?"

#: .\src\templates\jobad\tab\worker_deployment.html:112
msgid "Request extension"
msgstr "Verlängerung beantragen"

#: .\src\templates\jobad\tab\worker_deployment.html:113
msgid "Pause deployment"
msgstr "Auftrag pausieren"

#: .\src\templates\jobad\tab\worker_deployment.html:114
msgid "Discontinue deployment and replace worker"
msgstr "Mitarbeiter beenden und nachbesetzen"

#: .\src\templates\jobad\tab\worker_deployment.html:115
msgid "Terminate deployment"
msgstr "Mitarbeiter beenden"

#: .\src\templates\jobad\tab\worker_deployment.html:116
msgid "Request internal employment"
msgstr "Übernahme beantragen"

#: .\src\templates\jobad\tab\worker_deployment.html:140
#, fuzzy
#| msgid "Terminate deployment"
msgid "Terminate Worker"
msgstr "Mitarbeiter beenden"

#: .\src\templates\jobad\tab\worker_deployment.html:141
#, fuzzy
#| msgid "Are you sure you want to delete this Jobad?"
msgid "Are you sure you want to discontinue this worker?"
msgstr "Sind Sie sicher, dass Sie diese Jobad löschen möchten?"

#: .\src\templates\jobad\tab\worker_deployment.html:145
#, fuzzy
#| msgid "Select action"
msgid "Select Termination Date"
msgstr "Aktion auswählen"

#: .\src\templates\jobad\tab\worker_deployment.html:152
#, fuzzy
#| msgid "Select a reason"
msgid "Select Termination Reason"
msgstr "Einen Grund wählen"

#: .\src\templates\jobad\tab\worker_deployment.html:155
msgid "Select a reason"
msgstr "Einen Grund wählen"

#: .\src\templates\userprofiles\_user_registration_modal.html:86
#: .\src\templates\userprofiles\profile.html:106
msgid "User registration"
msgstr "Benutzer Registrierung"

#: .\src\templates\userprofiles\_user_registration_modal.html:102
msgid "Select a company..."
msgstr "Wählen Sie ein Unternehmen aus..."

#: .\src\templates\userprofiles\create_user.html:3
#: .\src\templates\userprofiles\create_user.html:38
msgid "Create User"
msgstr "Benutzer erstellen"

#: .\src\templates\userprofiles\create_user.html:7
msgid "Create New User"
msgstr "Neuen Benutzer anlegen"

#: .\src\templates\userprofiles\create_user.html:10
msgid "User Information"
msgstr "Benutzer-Information"

#: .\src\templates\userprofiles\create_user.html:24
msgid "User Profile Information"
msgstr "Benutzerprofilinformationen"

#: .\src\templates\userprofiles\profile.html:96
msgid "Search by username or name"
msgstr "Suche nach Benutzername oder Name"

#: .\src\templates\userprofiles\profile.html:154
msgid "No company assigned"
msgstr "Keine Firma zugeordnet"

#: .\src\templates\userprofiles\profile.html:157
#: .\src\templates\userprofiles\tab\details_tab.html:14
msgid "Email"
msgstr "E-Mail"

#: .\src\templates\userprofiles\profile.html:177
msgid "Assign company area"
msgstr "Unternehmensbereich zuordnen"

#: .\src\templates\userprofiles\profile.html:283
#, fuzzy
#| msgid "Select a company..."
msgid "Select a company"
msgstr "Wählen Sie ein Unternehmen aus..."

#: .\src\templates\userprofiles\tab\details_tab.html:18
msgid "Phone Number"
msgstr "Telefonnummer"

#: .\src\templates\userprofiles\tab\password_tab.html:6
msgid "New Password"
msgstr "Passwort *"

#: .\src\templates\userprofiles\tab\password_tab.html:10
msgid "Confirm Password"
msgstr "Import bestätigen"

#: .\src\templates\userprofiles\tab\password_tab.html:16
msgid "At least 6 characters"
msgstr ""

#: .\src\templates\userprofiles\tab\password_tab.html:19
msgid "Contains letters and numbers"
msgstr "Geben Sie eine Zahl ein."

#: .\src\templates\userprofiles\tab\password_tab.html:22
msgid "Includes at least one special character"
msgstr ""

#: .\src\templates\userprofiles\tab\password_tab.html:28
msgid "Change Password"
msgstr "Passwort ändern"

#~ msgid "Candidate must have G25"
#~ msgstr "Der Kandidat muss über G25 verfügen"

#~ msgid "Interview"
#~ msgstr "Interview"

#~ msgid "Open Again"
#~ msgstr "Erneut öffnen"

#~ msgid "Additional Open Positions"
#~ msgstr "Zusätzliche offene Positionen"

#~ msgid "Published Date"
#~ msgstr "Veröffentlicht"

#~ msgid "Surname"
#~ msgstr "Nachname"

#~ msgid "Date of birth"
#~ msgstr "Geburtsdatum"

#~ msgid "Define interview"
#~ msgstr "Interview/Probetag vergeben"

#~ msgid "Complete interview phase"
#~ msgstr "Interviewphase abschließen"

#~ msgid "Define worker's contract"
#~ msgstr "Startdatum festlegen"

#~ msgid "Approve worker's contract"
#~ msgstr "Startdatum bestätigen"

#~ msgid "Finish the process"
#~ msgstr "Start des Kandidaten bestätigen"

#~ msgid "Group:"
#~ msgstr "Gruppe:"

#~ msgid "Type:"
#~ msgstr "Type:"

#~ msgid "Status:"
#~ msgstr "Status:"

#~ msgid "Location:"
#~ msgstr "Ort:"

#~ msgid "Favourites"
#~ msgstr "Favoriten"

#~ msgid "Jobads"
#~ msgstr "Job Anzeigen"

#~ msgid "My Company"
#~ msgstr "Mein Unternehmen"

#~ msgid "2 New"
#~ msgstr "2 Neu"

#~ msgid "English"
#~ msgstr "Englisch"

#~ msgid "Deutsch"
#~ msgstr "Deutsch"
