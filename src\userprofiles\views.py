# views.py
import json
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.contrib.auth.forms import UserCreationForm
from django.db.models import Q
from django.core.paginator import <PERSON><PERSON><PERSON>, PageNotAnInteger, EmptyPage
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib.auth.hashers import make_password
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView
from django.contrib.auth.models import User
from company.models import Company, CompanyPartner, CompanyStructure
from email_service.services import EmailService
from email_service.tasks import send_async_email
from userprofiles.models import UserProfile, UserProfileDepartments

from .forms import UserProfileForm

@login_required
def create_user(request):
    if request.method == 'POST':
        profile_form = UserProfileForm(request.POST)
        if profile_form.is_valid():
            profile_form.save()
            messages.success(request, 'UserProfile created successfully')
            return redirect('user-profile')  # Redirects to the profile page after successful creation
    else:
        profile_form = UserProfileForm()

    return render(request, 'userprofiles/create_user.html', {
        'profile_form': profile_form
    })

class UserCreateView(CreateView):
    model = UserProfile
    form_class = UserCreationForm
    second_form_class = UserProfileForm
    template_name = 'userprofiles/create_user.html'
    success_url = reverse_lazy('user-list')

    def get_context_data(self, **kwargs):
        context = super(UserCreateView, self).get_context_data(**kwargs)
        if self.request.POST:
            context['profile_form'] = self.second_form_class(self.request.POST)
        else:
            context['profile_form'] = self.second_form_class()
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        profile_form = context['profile_form']
        if form.is_valid() and profile_form.is_valid():
            user = form.save()
            profile = profile_form.save(commit=False)
            profile.user = user
            profile.save()
            return super(UserCreateView, self).form_valid(form)
        else:
            return self.form_invalid(form)
        
@csrf_exempt        
@login_required
def profile_view(request):
    profile_form = UserProfileForm()
    companies = Company.objects.all()

    try:
        user_profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        return JsonResponse({'error': 'UserProfile does not exist for the logged-in user'}, status=404)

    if user_profile.company.company_type == 'global':
        # No company filter for 'global' users
        active_users = UserProfile.objects.filter(active=True)
    else:
        # Filter by the logged-in user's company
        active_users = UserProfile.objects.filter(
            company=user_profile.company,
            active=True
        )

    query = request.GET.get('q', '')
    active_users = active_users.filter(
        Q(user__first_name__icontains=query) | 
        Q(user__last_name__icontains=query) | 
        Q(user__username__icontains=query),
        active=True
    )

    paginator = Paginator(active_users, 45)
    page = request.GET.get('page', 1)
    try:
        users_page = paginator.page(page)
    except PageNotAnInteger:
        users_page = paginator.page(1)
    except EmptyPage:
        users_page = paginator.page(paginator.num_pages)
        
    role_map = {
        1: 'Admin',
        2: 'Personnel Officer',
        3: 'Recruiter Master Vendor',
        4: 'Recruiter Lieferant',
        5: 'Proposer',
        6: 'Job ad approver',
        7: 'Worker approver',
        8: 'Super approver'
    }

    for user in users_page:
        user.role_name = role_map.get(user.role, 'Unknown role')
        if user.country:
            user.country_flag = f"https://flagcdn.com/w40/{user.country.code.lower()}.png"
        if user.role == 5:  # Proposer
            # Fetch the structures the user is already linked to
            linked = UserProfileDepartments.objects.filter(user_ref=user).values_list('structure', flat=True)
            user.linked_structures = list(linked)
            user.linked_structures_json = json.dumps(user.linked_structures)
            user.company_structure = CompanyStructure.objects.filter(company=user.company).order_by('id')

    if request.method == 'POST':
        if 'link_structure' in request.POST:
            user_id = request.POST.get('user_id')
            selected_nodes = json.loads(request.POST.get('selected_nodes', '[]'))

            if not user_id:
                return JsonResponse({'error': 'Missing user_id'}, status=400)
            # If selected_nodes is empty you may want to remove all links
            try:
                # Use pk here because in your UserProfile model, the PK is the user field.
                user = UserProfile.objects.get(pk=user_id, role=5)

                # Remove any existing structure links that are not in the incoming payload.
                UserProfileDepartments.objects.filter(user_ref=user) \
                    .exclude(structure__id__in=selected_nodes).delete()

                # Now add (or keep) the selected links.
                for node_id in selected_nodes:
                    structure = CompanyStructure.objects.get(id=node_id)
                    UserProfileDepartments.objects.get_or_create(user_ref=user, structure=structure)

                return JsonResponse({'success': True}, status=200)
            except UserProfile.DoesNotExist:
                return JsonResponse({'error': 'Invalid user or user does not have Proposer role'}, status=400)
            except CompanyStructure.DoesNotExist as e:
                return JsonResponse({'error': f'Invalid structure: {e}'}, status=400)
        else:
            profile_form = UserProfileForm(request.POST)
            if profile_form.is_valid():
                profile_form.save()
                messages.success(request, 'UserProfile created successfully')
                return redirect('user-profile')

    context = {
        'users': users_page,
        'profile_form': profile_form,
        'companies': companies,
        'search_query': query,
    }
    return render(request, 'userprofiles/profile.html', context)

@login_required
def create_user_profile(request):
    if request.method == 'POST':
        profile_form = UserProfileForm(request.POST)

        # Retrieve the user-related fields
        username = request.POST.get('username')
        status = request.POST.get('status', 'password-change')
        email = request.POST.get('email')
        password = request.POST.get('password')
        first_name = request.POST.get('firstname')
        last_name = request.POST.get('lastname')
        role = request.POST.get('role')
        company_id = request.POST.get('company')
        initial = request.POST.get('initial')
        street = request.POST.get('street') or None
        city = request.POST.get('city') or None
        post_code = request.POST.get('post_code') or None
        telephone_number = request.POST.get('telephone_number') or None
        mobile_number = request.POST.get('mobile_number') or None
        country = request.POST.get('country') or None

        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'password': make_password(password),
                'first_name': first_name,
                'last_name': last_name,
            }
        )

        if not created:
            # If the user already exists, update their information
            user.email = email
            user.set_password(password)
            user.first_name = first_name
            user.last_name = last_name
            user.save()

        # Check if a UserProfile already exists for this user
        user_profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'role': role,
                'company_id': company_id,
                'status': status,
                'initial': initial,
                'street': street or None,
                'post_code': post_code or None,
                'city': city or None,
                'telephone_number': telephone_number or None,
                'mobile_number': mobile_number or None,
                'country': country or None,
                'created_by': request.user,
            }
        )

        if not created:
            # If a UserProfile already exists, update it
            user_profile.role = role
            user_profile.company_id = company_id
            user_profile.initial = initial
            user_profile.street = street or None
            user_profile.post_code = post_code or None
            user_profile.city = city or None
            user_profile.telephone_number = telephone_number or None
            user_profile.mobile_number = mobile_number or None
            user_profile.country = country or None
            user_profile.save()

        # Notify relevant admins
        company = Company.objects.get(pk=company_id)
        if company.company_type in ['supplier', 'vendor']:
            # Notify admins of the current company
            current_company_admins = UserProfile.objects.filter(
                company=company,
                role=1  # Admin role
            )
            for admin in current_company_admins:
                EmailService.send_new_user_email( # type: ignore
                    user_profile=user_profile,
                    recipient_email=admin.user.email,
                    recipient_name=admin.user.get_full_name(),
                    action_by=request.user.get_full_name()
                )

            # Notify admins of the linked customer company
            linked_customers = CompanyPartner.objects.filter(company=company)
            if not linked_customers.exists():
                messages.warning(request, 'No linked customer company found for this vendor.')
            else:
                for link in linked_customers:
                    if link.customer:  # Check if `customer` is not None
                        customer_admins = UserProfile.objects.filter(
                            company=link.customer,
                            role=1  # Admin role
                        )
                        for admin in customer_admins:
                            EmailService.send_linked_customer_admin_email(
                                user_profile=user_profile,
                                recipient_email=admin.user.email,
                                action_by=request.user.get_full_name()
                            )

        messages.success(request, 'UserProfile created or updated successfully')
        return redirect('user-profile')

    else:
        profile_form = UserProfileForm()

    return render(request, 'userprofiles/profile.html', {
        'profile_form': profile_form
    })

@login_required
def edit_user_profile(request, user_id):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)

    if request.method == "POST":
        form = UserProfileForm(request.POST, instance=user_profile)
        if form.is_valid():
            form.save()
            messages.success(request, "User updated successfully")
            return redirect("user-profile")
        else:
            return render(request, "userprofiles/edit_user_profile.html", {"form": form})

    # Si se accede por GET vía AJAX, devolvemos los datos en JSON (opcional)
    if request.headers.get("x-requested-with") == "XMLHttpRequest":
        data = {
            "user_profile": {
                "id": user_profile.pk,
                "firstname": user_profile.user.first_name,
                "lastname": user_profile.user.last_name,
                "username": user_profile.user.username,
                "email": user_profile.user.email,
                "initial": user_profile.initial,
                "company": user_profile.company.id if user_profile.company else None,
                "telephone_number": user_profile.telephone_number,
                "mobile_number": user_profile.mobile_number,
                "street": user_profile.street,
                "post_code": user_profile.post_code,
                "city": user_profile.city,
                "country": user_profile.country.code if user_profile.country else "",
                "role": user_profile.role,
            }
        }
        return JsonResponse(data)

    return JsonResponse({"error": "Invalid request"}, status=404)

@login_required
def profile_settings(request):
    user = request.user
    try:
        user_profile = user.userprofile
    except UserProfile.DoesNotExist:
        messages.error(request, "User profile not found.")
        return redirect("/")

    tab = request.GET.get("tab", "details")  # Default tab is 'details'

    if request.method == "POST":
        if tab == "details":
            # Update user and profile data
            user.first_name = request.POST.get("first_name", user.first_name)
            user.last_name = request.POST.get("last_name", user.last_name)
            user.email = request.POST.get("email", user.email)
            user.save()

            user_profile.street = request.POST.get("street") or None
            user_profile.city = request.POST.get("city") or None

            # Validate and set post_code properly
            post_code = request.POST.get("post_code")
            user_profile.post_code = int(post_code) if post_code and post_code.isdigit() else None
            user_profile.telephone_number = request.POST.get("telephone_number") or None
            user_profile.save()

            messages.success(request, "Profile details updated successfully!")
        elif tab == "password":
            # Get the new password and confirm password
            new_password = request.POST.get("new_password")
            confirm_password = request.POST.get("confirm_password")

            # Validate the password
            if not new_password or not confirm_password:
                messages.error(request, "Please fill in all password fields.")
            elif new_password != confirm_password:
                messages.error(request, "New password and confirmation do not match.")
            elif len(new_password) < 6:
                messages.error(request, "Password must be longer than 6 characters.")
            elif not any(char.isdigit() for char in new_password):
                messages.error(request, "Password must include at least one number.")
            elif not any(char.isalpha() for char in new_password):
                messages.error(request, "Password must include at least one letter.")
            elif not any(char in "!@#$%^&*()-_=+[]{}|;:,.<>?/" for char in new_password):
                messages.error(request, "Password must include at least one special character.")
            else:
                # Update the user's password
                user.set_password(new_password)
                user.save()

                messages.success(request, "Passwort erfolgreich aktualisiert!")

    return render(request, "userprofiles/profile_settings.html", {"tab": tab, "user": user, "user_profile": user_profile})