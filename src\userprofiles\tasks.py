import logging
from django.utils import timezone
from django_q.models import Schedule
from contrib.decorators import register_schedule
from .models import UserProfile
from .services import anonymize_user_profile

logger = logging.getLogger(__name__)


@register_schedule(schedule_type=Schedule.DAILY, name="anonymize_user_profile")
def check_and_anonymize_users():
    """
    Check for user profiles that are scheduled for anonymization and anonymize them.

    This function will be executed hourly as scheduled by Django Q.
    """
    now = timezone.now()
    profiles = UserProfile.objects.filter(scheduledforanonymisation__lte=now, anonymous=False)
    for profile in profiles:
        try:
            anonymize_user_profile(profile)
            profile.anonymous = True
            profile.save()
            logger.info(f"UserProfile {profile.pk} anonymized.")
        except Exception as e:
            logger.error(f"Error anonymizing UserProfile {profile.pk}: {e}")
