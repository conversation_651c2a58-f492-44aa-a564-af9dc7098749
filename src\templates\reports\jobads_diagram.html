{% extends "_base_frontend.html" %}
{% load application_filters %}  

{% block title %}Job Ads Diagram (LGI Open Jobs){% endblock %}

{% block content %}
  <h1 class="text-2xl font-bold mb-4">
    Job Ads Diagram ({{ total_count }} jobads, ∞ = >40 open positions)
  </h1>

  {# (Optional) Show first‐10 slices for debugging #}
  <pre style="background: #f5f5f5; padding: 0.5rem; max-height: 120px; overflow-y: auto;">
    Labels (first 10): {{ labels|slice:":10" }}
    Actual Positions (first 10): {{ actual_positions|slice:":10" }}
    Capped Positions (first 10): {{ capped_positions|slice:":10" }}
    Infinite Mask (first 10): {{ infinite_mask|slice:":10" }}
    Candidates (first 10): {{ candidates|slice:":10" }}
  </pre>

  {# Scrollable container so each bar is ~50px wide #}
  <div style="overflow-x: auto; width: 100%; margin-top: 1rem; border: 1px solid #e2e8f0; padding: 0.5rem;">
    <canvas
      id="jobadsChart"
      width="{{ total_count|mul:50 }}"
      height="400"
      style="border: 1px solid #e2e8f0;">
    </canvas>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    (function() {
      // 1) Grab arrays from Django context
      const labels = {{ labels|safe }};
      const actualPositions = {{ actual_positions|safe }};
      const cappedPositions = {{ capped_positions|safe }};
      const infiniteMask = {{ infinite_mask|safe }};
      const candidateCounts = {{ candidates|safe }};

      console.log("Plotting bars:", labels.length);

      const ctx = document.getElementById('jobadsChart').getContext('2d');
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Open Positions (≤ 40)',
              data: cappedPositions,
              backgroundColor: 'rgba(54, 162, 235, 0.6)',    // blue
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1,
            },
            {
              label: 'Open Positions > 40 (∞)',
              data: infiniteMask,
              backgroundColor: 'rgba(255, 159, 64, 0.6)',   // orange
              borderColor: 'rgba(255, 159, 64, 1)',
              borderWidth: 1,
            },
            {
              label: '# of Candidates',
              data: candidateCounts,
              backgroundColor: 'rgba(255, 99, 132, 0.6)',   // pink
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 1,
            }
          ]
        },
        options: {
          responsive: false,           // fixed width for scroll
          maintainAspectRatio: false,
          scales: {
            x: {
              ticks: {
                autoSkip: true,
                maxRotation: 90,
                minRotation: 90
              }
            },
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Count'
              },
              max: 40,    // cap full scale at 40
              ticks: {
                callback: function(value) {
                  // If tick value is exactly 40, show “∞”
                  return (value === 40) ? '∞' : value;
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                label: function(context) {
                  const idx = context.dataIndex;
                  const dsLabel = context.dataset.label;
                  // If dataset is the blue “≤ 40” bar
                  if (dsLabel === 'Open Positions (≤ 40)') {
                    const actual = actualPositions[idx];
                    if (actual <= 40) {
                      return `Open Positions: ${actual}`;
                    }
                    return ''; // skip if actually > 40
                  }
                  // If dataset is the orange “∞” bar
                  if (dsLabel === 'Open Positions > 40 (∞)') {
                    if (actualPositions[idx] > 40) {
                      return 'Open Positions: ∞';
                    }
                    return '';
                  }
                  // # of Candidates
                  if (dsLabel === '# of Candidates') {
                    return `# of Candidates: ${context.parsed.y}`;
                  }
                  return '';
                }
              }
            }
          }
        }
      });
    })();
  </script>
{% endblock %}
