from datetime import datetime, timedelta
import requests
import json
import logging
import os
import re
from urllib.parse import quote

from django.conf import settings
from django.forms.models import model_to_dict
from jobad.utils.stage import format_stage
from django.utils.translation import gettext as _
from django.db.models import OuterRef, Subquery
from django.db.models import Value
from django.db.models.functions import Concat
from django.db import IntegrityError
from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential, AzureCliCredential
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.contrib import messages
from django import forms
from django.contrib.auth import get_user_model
from django.utils.dateparse import parse_date
from django.http import HttpResponseBadRequest, HttpResponseRedirect, JsonResponse, StreamingHttpResponse, HttpResponseNotAllowed, HttpResponse, FileResponse, Http404
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse_lazy, reverse
from django.views import View
from itertools import zip_longest
from django.views.decorators.http import require_GET
from django.views.generic import ListView, CreateView, DeleteView
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from email_service.tasks import send_async_email
from django.template.loader import render_to_string
from django.templatetags.static import static

#extract image from CV
import fitz
from PIL import Image
from io import BytesIO
from django.core.files.base import ContentFile

from common.validators import EUROPEAN_COUNTRIES
from company.models import CompanyDocument, CompanyPartner, CompanyPosition, CompanyLocation, Company, CompanyStructure, RejectionEndReason
from email_service.services import EmailService
from jobad.forms import CandidateForm, DocumentForm, JobadForm
from jobad.models import Candidate, CandidateDocument, DocumentType, Interview, Jobad, JobadApplication, JobadApplicationStage, JobadDepartment, JobaDocument, JobadStage, JobadTemplate, RejectedCandidateReason, Selection, SelectionStage, Worker, WorkerRejectionReason, WorkerStage
from django.views.generic import FormView
from company.models import Company
from userprofiles.models import UserProfile, UserProfileDepartments


# from userprofiles.models import UserProfile
EUROPEAN_COUNTRIES = [
    'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 'DE', 'GR', 
    'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 'PL', 'PT', 'RO', 'SK', 
    'SI', 'ES', 'SE'
]


class CandidateListView(View):
    template_name = 'candidate/list.html'

    def get_filtered_candidates(self, request):
        user = request.user
        user_profile = user.userprofile
        user_company = user_profile.company

        if user_company.company_type == 'global':
            # Global admin can see all candidates
            return Candidate.objects.all()
        elif user_company.company_type == 'customer':
            # Get suppliers linked to the customer's company
            linked_suppliers = CompanyPartner.objects.filter(
                customer=user_company
            ).values_list('company', flat=True)
            
            # Get candidates from the linked suppliers
            return Candidate.objects.filter(company__in=linked_suppliers)
        elif user_company.company_type in ['supplier', 'vendor']:
            # Get candidates belonging to the user's company
            return Candidate.objects.filter(company=user_company)
        else:
            # Fallback to an empty queryset for unexpected company types
            return Candidate.objects.none()
        
    def get_filtered_companies(self, request):
        user = request.user
        user_profile = user.userprofile
        user_company = user_profile.company

        if user_company.company_type == 'global':
            # Global admin can see all supplier/vendor companies
            return Company.objects.filter(company_type__in=['supplier', 'vendor'])
        elif user_company.company_type in ['supplier', 'vendor']:
            # Suppliers and vendors can only see their own company
            return Company.objects.filter(pk=user_company.pk)
        else:
            # Customers cannot create candidates
            return Company.objects.none()

    def extract_image_from_pdf(self, file):
        try:
            pdf_document = fitz.open(file)
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                images = page.get_images(full=True)
                for img_index, img in enumerate(images):
                    xref = img[0]
                    base_image = pdf_document.extract_image(xref)
                    image_bytes = base_image["image"]
                    image = Image.open(BytesIO(image_bytes))
                    output = BytesIO()
                    image.save(output, format="PNG")
                    output.seek(0)
                    return ContentFile(output.read(), name="extracted_image.png")
        except Exception as e:
            print(f"Error extracting image from PDF: {e}")
        return None
        
    def get(self, request, *args, **kwargs):
        candidates = self.get_filtered_candidates(request)
        companies = self.get_filtered_companies(request)
        query = request.GET.get('search')
        if query:
            candidates = candidates.filter(
                Q(firstname__icontains=query) | Q(lastname__icontains=query)
            )
        total_candidates = candidates.count()

        candidates = candidates.order_by('id')
        # Paginación
        paginator = Paginator(candidates, 45)  # 45 por página
        page_number = request.GET.get('page') or 1
        page_obj = paginator.get_page(page_number)
        companies_list = list(companies.values('pk', 'name'))
        form = CandidateForm()
        form.fields['company'].queryset = companies
        context = {
            'candidate_list': page_obj.object_list,
            'form': form,
            'companies_json': json.dumps(companies_list, cls=DjangoJSONEncoder),
            'EUROPEAN_COUNTRIES': list(EUROPEAN_COUNTRIES),
            'search': query or '',
            'has_next': page_obj.has_next(),
            'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
            'total_candidates': total_candidates
        }
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return render(request, 'candidate/_candidate_list_partial.html', context)

        return render(request, self.template_name, context)
        
    def post(self, request, *args, **kwargs):
        form = CandidateForm(request.POST, request.FILES)
        
        if form.is_valid():
            candidate = form.save(commit=False)
            document = request.FILES.get('documents')
            extracted_image = None
            errors = {}

            # Validate work permit if candidate is from outside Europe
            country_code = form.cleaned_data.get('country')

            if country_code not in EUROPEAN_COUNTRIES and not request.FILES.get('workpermit'):
                errors['workpermit'] = ['Work permit is required for candidates outside of Europe.']

            if document and document.name.endswith('.pdf'):
                extracted_image = self.extract_image_from_pdf(document)

            # Assign the extracted image to the profile_picture field
            if extracted_image:
                candidate.profile_picture.save(extracted_image.name, extracted_image)


            # Return errors if any
            if errors:
                candidates = Candidate.objects.all()
                query = request.GET.get('search')
                if query:
                    candidates = candidates.filter(Q(firstname__icontains=query))
                context = {
                    'candidate_list': candidates,
                    'form': form,
                    'keep_modal_open': True,  # Keep modal open if there are errors
                    'errors': errors,  # Pass errors to context
                }
                return render(request, self.template_name, context)
            
            # Assign the extracted image to the profile_picture field
            if extracted_image and not candidate.profile_picture:
                candidate.profile_picture.save(extracted_image.name, extracted_image)

            candidate.save()

            if document:
                CandidateDocument.objects.create(candidate=candidate, document=document)

            # Save work permit document if it exists
            workpermit = request.FILES.get('workpermit')
            if workpermit:
                CandidateDocument.objects.create(candidate=candidate, document=workpermit)

            messages.success(request, 'Candidate successfully created', extra_tags='bg-green-500 text-black')
            return redirect(reverse('candidate-detail', kwargs={'pk': candidate.pk}))

        else:
            # Handle form errors
            candidates = self.get_filtered_candidates(request)
            query = request.GET.get('search')
            if query:
                candidates = candidates.filter(Q(firstname__icontains=query))
            context = {
                'candidate_list': candidates,
                'form': form,
                'keep_modal_open': True,  # Keep modal open if there are errors
            }
            return render(request, self.template_name, context)


    def render_invalid_form(self, request, form):
        candidates = self.get_filtered_candidates(request)
        context = {
            'candidate_list': candidates,
            'form': form,
        }
        return render(request, self.template_name, context)

@csrf_exempt
def extract_profile_picture(request):
    if request.method == 'POST' and request.FILES.get('file'):
        file = request.FILES['file']
        if file.name.endswith('.pdf'):
            try:
                # Use a file-like object for PyMuPDF
                with BytesIO(file.read()) as file_like:
                    pdf_document = fitz.open(stream=file_like, filetype="pdf")
                    for page_num in range(len(pdf_document)):
                        page = pdf_document[page_num]
                        images = page.get_images(full=True)
                        for img_index, img in enumerate(images):
                            xref = img[0]
                            base_image = pdf_document.extract_image(xref)
                            image_bytes = base_image["image"]
                            image = Image.open(BytesIO(image_bytes))
                            # Convert image to PNG format
                            output = BytesIO()
                            image.save(output, format="PNG")
                            output.seek(0)
                            # Save the image temporarily and return its URL
                            file_path = default_storage.save(f"temp/extracted_image_{xref}.png", ContentFile(output.read()))
                            image_url = default_storage.url(file_path)
                            return JsonResponse({'image_url': image_url})
            except Exception as e:
                print(f"Error extracting image from PDF: {e}")
    return JsonResponse({'error': 'Failed to extract image'}, status=400)

def candidate_detail(request, pk):
    candidate = get_object_or_404(Candidate, pk=pk)
    tab = request.GET.get('tab', 'details')
    documents = CandidateDocument.objects.filter(candidate=candidate) if tab == 'documents' else []
    company_name = candidate.company.name

    if request.method == 'POST':
        if 'profile_picture' in request.FILES:
            form = CandidateForm(request.POST, request.FILES, instance=candidate)
        else:
            form = CandidateForm(request.POST, instance=candidate)

        if form.is_valid():
            form.save()
            messages.success(request, 'Candidate details updated successfully', extra_tags='success')
            return redirect(f"{reverse('candidate-detail', kwargs={'pk': pk})}?tab=details")
        else:
            document_form = DocumentForm(request.POST, request.FILES)
            if document_form.is_valid():
                document = document_form.save(commit=False)
                document.candidate = candidate
                document.uploaded_by = request.user.userprofile
                document.save()
                print(f'document', document.document)
                messages.success(request, 'Document uploaded successfully', extra_tags='success')
                return redirect(f"{reverse('candidate-detail', kwargs={'pk': pk})}?tab=documents")
    else:
        form = CandidateForm(instance=candidate)
        document_form = DocumentForm()

    context = {
        'candidate': candidate,
        'document_form': document_form,
        'form': form,
        'tab': tab,
        'company_name': company_name,
        'documents': documents,
    }

    return render(request, 'candidate/candidate_detail.html', context)

def delete_candidate(request, pk):
    candidate = get_object_or_404(Candidate, pk=pk)
    if request.method == 'POST':
        candidate.delete()
        messages.success(request, 'Candidate deleted successfully', extra_tags='success')
        return redirect('candidate-list') 
    return redirect('candidate-detail', pk=pk)

class CandidateCreateView(CreateView):
    model = Candidate
    form_class = CandidateForm
    template_name = 'candidate/add.html'
    success_url = reverse_lazy('candidate-list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Candidate successfully created', extra_tags='bg-green-500 text-black')
        return redirect('candidate-detail', pk=self.object.pk)

    def form_invalid(self, form):
        messages.error(self.request, 'Error creating candidate', extra_tags='bg-red-500 text-black')
        return super().form_invalid(form)


class JobadListView(ListView):
    model = Jobad
    template_name = 'jobad/index.html'
    context_object_name = 'jobad_list'
    paginate_by = 45

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-id')
        search_query = self.request.GET.get('search', '').strip()
        company_filter = self.request.GET.get('company', '')
        group_filter = self.request.GET.get('group', '')
        location_filter = self.request.GET.get('location', '')

        # Filtrar por company, group y location si se proporcionan.
        if company_filter:
            company_ids = [x for x in company_filter.split(',') if x]
            if company_ids:
                queryset = queryset.filter(company_id__in=company_ids)
        if group_filter:
            group_ids = [x for x in group_filter.split(',') if x]
            if group_ids:
                queryset = queryset.filter(employee_group__in=group_ids)
        if location_filter:
            location_ids = [x for x in location_filter.split(',') if x]
            if location_ids:
                queryset = queryset.filter(location_id__in=location_ids)

        # Obtener usuario, perfil y empresa logueada.
        user = self.request.user
        user_profile = user.userprofile
        user_company = user_profile.company

        # Filtrado según tipo de empresa.
        if user_company.company_type in ['supplier', 'vendor']:
            linked_structures = CompanyPartner.objects.filter(
                company__company_type__in=['supplier', 'vendor'],
                company=user_company
            ).values_list('structure', flat=True)
            queryset = queryset.filter(
                id__in=JobadDepartment.objects.filter(
                    structure__in=linked_structures
                ).values_list('jobad_id', flat=True)
            )
        elif user_company.company_type == 'customer':
            if user_profile.role == 5:  # Proposer
                linked_structures = UserProfileDepartments.objects.filter(
                    user_ref=user_profile
                ).values_list('structure', flat=True)
                queryset = queryset.filter(
                    id__in=JobadDepartment.objects.filter(
                        structure__in=linked_structures
                    ).values_list('jobad_id', flat=True)
                )
            else:
                queryset = queryset.filter(company=user_company)

        active_stages = [
            JobadStage.REQUESTED,
            JobadStage.APPROVED,
            JobadStage.OPEN_FOR_EVERYONE,
            JobadStage.OPEN_AGAIN
        ]
        archived_stages = [
            JobadStage.REJECTED,
            JobadStage.FILLED,
            JobadStage.COMPLETED,
            JobadStage.WITHDRAWN
        ]

        self.total_active_queryset = queryset.filter(stage__in=active_stages).distinct()
        self.total_archived_queryset = queryset.filter(stage__in=archived_stages).distinct()

        # Obtener la pestaña actual a través del parámetro GET (por defecto 'active').
        tab = self.request.GET.get('tab', 'active')
        base_queryset = self.total_active_queryset if tab == 'active' else self.total_archived_queryset

        # Aplicar el filtro de búsqueda sobre el queryset base según la pestaña.
        if search_query:
            q_filters = (
                Q(position__title__icontains=search_query) |
                Q(location__street__icontains=search_query) |
                Q(location__city__icontains=search_query)
            )
            if search_query.isdigit():
                q_filters |= Q(id=int(search_query))
            pos_loc_filter = base_queryset.filter(q_filters)

            candidates = Candidate.objects.filter(
                Q(firstname__icontains=search_query) | Q(lastname__icontains=search_query)
            )
            if candidates.exists():
                app_ids = JobadApplication.objects.filter(candidate__in=candidates).values_list('jobad_id', flat=True)
                candidate_filter = base_queryset.filter(id__in=app_ids)
                final_queryset = (pos_loc_filter | candidate_filter).distinct()
            else:
                final_queryset = pos_loc_filter.distinct()
        else:
            final_queryset = base_queryset

        return final_queryset.distinct()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_count'] = self.total_active_queryset.count()
        context['archived_count'] = self.total_archived_queryset.count()

        tab = self.request.GET.get('tab', 'active')
        context['tab'] = tab
        if tab == 'archived':
            context['archived_jobads'] = self.object_list.order_by('-id')
        else:
            # Si no, usamos el queryset total de archivados (o podrías dejarlo vacío)
            context['archived_jobads'] = self.total_archived_queryset.order_by('-id')[:self.paginate_by]

        user_company = self.request.user.userprofile.company
        # Filter companies based on the logged-in user's company type.
        if user_company.company_type == 'customer':
            # For a customer, show only his/her company.
            companies = Company.objects.filter(pk=user_company.pk)
        elif user_company.company_type in ['supplier', 'vendor']:
            # For a supplier/vendor, get the linked customer companies using the customer_id field.
            partner_company_ids = CompanyPartner.objects.filter(
                company=user_company
            ).values_list('customer_id', flat=True)
            companies = Company.objects.filter(pk__in=partner_company_ids)
        else:
            # Global: show all companies of type customer.
            companies = Company.objects.filter(company_type='customer')
        
        context['companies'] = companies
        # Now filter locations based on the companies list.
        # This will display only the CompanyLocation objects related to the companies determined above.
        context['locations'] = CompanyLocation.objects.filter(company__in=companies)
        context['form'] = CandidateForm()
        context['search'] = self.request.GET.get('search', '')
        context['can_add_jobad'] = user_company.company_type not in ['supplier']
        
        return context
    
User = get_user_model()

class JobadCreateView(CreateView):
    model = Jobad
    form_class = JobadForm
    template_name = 'jobad/add.html'

    def get_initial(self):
        initial = super().get_initial()
        company_id = self.kwargs['company_id']
        selected_department = self.request.session.get('selected_department')
        company_value = self.request.session.get('company_value')
        selected_department_structure = self.request.session.get('selected_department_structure')

        # Set the initial values for company and department
        initial['company'] = get_object_or_404(Company, id=company_id)
        initial['department_value'] = selected_department
        initial['company_value'] = company_value
        initial['created_by'] = f"{self.request.user.first_name} {self.request.user.last_name}"

        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company_id = self.kwargs['company_id']
        company = get_object_or_404(Company, id=company_id)
        company_value_id = self.request.session.get('company_value')
        structure_name = None
        if company_value_id:
            try:
                structure = CompanyStructure.objects.get(id=company_value_id)
                structure_name = structure.value
            except CompanyStructure.DoesNotExist:
                structure_name = None

        # Luego lo agregás al contexto:
        context['structure_name'] = structure_name
        # Add positions and locations related to the company
        context['positions'] = CompanyPosition.objects.filter(company=company)
        context['locations'] = CompanyLocation.objects.filter(company=company)
        context['company'] = company
        context['users'] = UserProfile.objects.filter(company=company) 
        context['default_contact_name'] = f"{self.request.user.first_name} {self.request.user.last_name}"
        context['default_contact_phone'] = getattr(self.request.user.userprofile, 'telephone_number', '') or ''

        templates = JobadTemplate.objects.filter(user_ref=self.request.user.userprofile)

        context['templates'] = []
        for t in templates:
            weekly = float(t.weekly_working_hours) if t.weekly_working_hours is not None else ''
            contact = t.contact or {}
            context['templates'].append({
                'id': t.id,
                'name': t.name,
                'position': t.position,
                'location': t.location,
                'time_period': t.time_period,
                'total': t.total,
                'description': t.description or '',
                'tasks': t.tasks or [],
                'requirements': t.requirements or [],
                'contact_name': contact.get('contact_name',''),
                'contact_phone': contact.get('contact_phone',''),
                'weekly_working_hours': weekly,
                'working_hours': t.working_hours or [],
                'cost_department': t.cost_department or '',
                'employee_group': t.employee_group,
            })

        context['templates_json'] = json.dumps(context['templates'])
        
        return context

    def form_valid(self, form):
        company = get_object_or_404(Company, id=self.kwargs['company_id'])
        form.instance.company = company
        form.instance.department_value = self.request.session.get('selected_department')
        form.instance.publisher = f"{self.request.user.first_name} {self.request.user.last_name} <{self.request.user.email}>"
        form.instance.created_by = self.request.user.userprofile
        form.instance.start_date = form.cleaned_data.get('start_date')

        working_hours_json = self.request.POST.get('working_hours')
        if working_hours_json:
            try:
                form.instance.working_hours = json.loads(working_hours_json)
            except json.JSONDecodeError:
                messages.error(self.request, 'Error parsing working hours', extra_tags='bg-red-500 text-black')
                return self.form_invalid(form)

        # position_id = self.request.POST.get('position')
        location_id = self.request.POST.get('location')
        # form.instance.position = get_object_or_404(CompanyPosition, id=position_id)
        form.instance.location = get_object_or_404(CompanyLocation, id=location_id)

        if company.id == 2:
            text = self.request.POST.get('position_text','').strip()
            if not text:
                messages.error(self.request, "Please enter a custom position title.", extra_tags="bg-red-500")
                return self.form_invalid(form)
            pos, _ = CompanyPosition.objects.get_or_create(company=company, title=text)
            form.instance.position = pos
        else:
            pid = self.request.POST.get('position')
            if not pid:
                messages.error(self.request, "Please select a position.", extra_tags="bg-red-500")
                return self.form_invalid(form)
            form.instance.position = get_object_or_404(CompanyPosition, id=pid)
            
        contact_name = self.request.POST.get('contact_name')
        contact_phone = self.request.POST.get('contact_phone')
        form.instance.contact = {
            'contact_name': contact_name,
            'contact_phone': contact_phone
        }

        created_by_user_id = self.request.POST.get('created_by')
        if created_by_user_id:
            form.instance.created_by = get_object_or_404(UserProfile, id=created_by_user_id)
        created_company_value = self.request.session.get('company_value')
        if created_company_value:
            structure = get_object_or_404(CompanyStructure, id=created_company_value)
            form.instance.company_value = structure.value
        selected_department_structure = self.request.session.get('selected_department_structure')
        if selected_department_structure:
            department_structure = get_object_or_404(CompanyStructure, id=selected_department_structure)
            form.instance.department_value = department_structure.value

        if self.request.POST.get('fuhrerschein_selected') == 'true':
            form.instance.fuhrerschein_selected = True

        position_title = form.instance.position.title.lower()
        company_name = company.name.lower()

        if 'staplerfahrer' in position_title and 'lgi' in company_name:
            form.instance.g25_selected = True
        if 'lkw' in position_title and 'lgi' in company_name:
            form.instance.fuhrerschein_selected = True

        # Checking company name to set stage
        if 'lgi' in company.name.lower():
            form.instance.stage = JobadStage.OPEN_FOR_EVERYONE  # Set stage to open_for_everyone
        else:
            form.instance.stage = JobadStage.REQUESTED  # Default stage
            
        jobad = form.save()
        if self.request.POST.get('create_template'):
            name = (self.request.POST.get('template_name') or jobad.position.title).strip()
            JobadTemplate.objects.create(
                user_ref = self.request.user.userprofile,
                name = name,
                position = jobad.position.title,
                location = str(jobad.location),
                time_period = jobad.time_period,
                total = jobad.total,
                description = jobad.description or '',
                tasks = jobad.tasks or '',
                requirements = jobad.requirements or '',
                contact = jobad.contact or {},
                working_hours = jobad.working_hours or [],
                weekly_working_hours = jobad.weekly_working_hours,
                cost_department = jobad.cost_department or '',
                employee_group = jobad.employee_group,
            )
            messages.success(self.request, 'Template saved!', extra_tags='bg-blue-500 text-white')
        if self.request.FILES.get('jobad_document'):
            jobad_document = self.request.FILES['jobad_document']
            JobaDocument.objects.create(
                jobad=jobad,
                document=jobad_document
            )
            print("JobaDocument created for jobad", jobad.pk)
        else:
            print("No jobad_document found in request.FILES")
        # Create JobadDepartment with the selected structure (if available)
        selected_department_structure = self.request.session.get('selected_department_structure')
        if selected_department_structure:
            JobadDepartment.objects.create(
                jobad=jobad,
                structure=selected_department_structure  # Save the structure, e.g., '8.0.0.0.0'
            )

        if jobad.stage == JobadStage.REQUESTED:
            self.notify_request_approval(jobad)
        else:
            self.notify_suppliers(jobad, selected_department_structure)
            

        # Call the super method after processing everything
        messages.success(self.request, 'Jobad successfully created', extra_tags='bg-green-500 text-black')
        return HttpResponseRedirect(reverse('jobad-detail', kwargs={'pk': jobad.pk}))
    def notify_suppliers(self, jobad, department_structure):
        """
        Notify all supplier users linked to the exact department about the new jobad.
        """
        # Fetch all CompanyPartners linked to the exact department structure
        company_partners = CompanyPartner.objects.filter(
            structure=department_structure,  # Match the exact structure
            company__company_type='supplier'  # Directly filter by company_type
        ).select_related('company')  # Prefetch supplier companies

        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}"
        formatted_shifts = "\n".join([f"{shift['start']} - {shift['end']}" for shift in jobad.working_hours])
        supplier_companies = [p.company for p in company_partners]

        supplier_users = UserProfile.objects.filter(
            company__in=supplier_companies,
            user__is_active=True
        ).distinct()
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        # 5) Combinar destinatarios en una sola lista
        recipients = list(supplier_users) + list(global_admins)
        subject = f"Neue Stellenanzeige {jobad.position.title} wurde hochgeladen"
        for user in recipients:
            context = {
                "jobad_title": jobad.position.title,
                "jobad_url": jobad_url,
                "company_value": jobad.company_value,
                "jobad_company": jobad.company,
                "jobad_location": f"{jobad.location.street} {jobad.location.street_number}, {jobad.location.city}",
                "supplier_name": user.user.get_full_name(),
                "supplier_company": user.company.name,
                "action_by": f"{self.request.user.first_name} {self.request.user.last_name}",
                "time_period": jobad.time_period,
                "total": jobad.total,
                "department_value": jobad.department_value,
                "jobad_link": f"{self.request.build_absolute_uri(reverse('jobad-detail', kwargs={'pk': jobad.id}))}",
                "logo_url": f"{self.request.build_absolute_uri(static('images/faviconn.png'))}",
                "start_date": jobad.start_date.strftime('%d-%m-%Y'),
                "tasks": jobad.tasks,
                "requirements": jobad.requirements,
                "shifts": formatted_shifts,
                "weekly_working_hours": jobad.weekly_working_hours,
            }
            body = render_to_string("emails/new_jobad_created.html", context)
            send_async_email.delay(user.user.email, subject, body)
    
    def notify_request_approval(self, jobad):
        """
        Notify all users of the company that created the jobad that approval is required.
        """
        # 1) Determine the department structure for this jobad
        try:
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        # 2) Base queryset: todos los usuarios activos de la empresa
        base_qs = UserProfile.objects.filter(
            company=jobad.company,
            user__is_active=True
        )

        # 3) Siempre notificar roles 1 y 8
        always_qs = base_qs.filter(role__in=[1, 8])

        # 4) Proposers (role 5) si hay estructura
        if dept_structure:
            proposers_qs = base_qs.filter(
                role=5,
                userprofiledepartments__structure=dept_structure  # your M2M or FK table
            ).distinct()
        else:
            proposers_qs = UserProfile.objects.none()

        # 5) Unir ambos querysets
        notify_qs = always_qs.union(proposers_qs)

        # 6) Queryset de global admins (role 1 en PPMS)
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        # 7) Combinar destinatarios
        recipients = list(notify_qs) + list(global_admins)

        # 8) Preparar datos comunes
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}"
        subject = f"Freigabe einer neuen Stellenanzeige erforderlich - {jobad.position.title}"

        # 9) Envío único de todos los emails
        for user_profile in recipients:
            context = {
                "recipient_name": user_profile.user.get_full_name(),
                "jobad_title": jobad.position.title,
                "jobad_url": jobad_url,
            }
            body = render_to_string("emails/jobad_requested.html", context)
            send_async_email.delay(user_profile.user.email, subject, body)

    def form_invalid(self, form):
        messages.error(self.request, 'Error creating jobad: ' + str(form.errors), extra_tags='bg-red-500 text-black')
        return super().form_invalid(form)

def approve_jobad(request, pk):
    """
    This function updates the jobad status from REQUESTED to OPEN_FOR_EVERYONE.
    """
    jobad = get_object_or_404(Jobad, pk=pk)

    if jobad.stage != 'requested':
        return JsonResponse({'success': False, 'message': 'Jobad is not in requested state.'}, status=400)

    jobad.stage = 'open_for_everyone'
    jobad.save()

    # Find the department structure
    department_structure = JobadDepartment.objects.filter(jobad=jobad).first()
    department_structure = department_structure.structure if department_structure else None

    # Notify suppliers
    notify_suppliers(jobad, department_structure, request)

    return JsonResponse({'success': True})

def reject_jobad(request, pk):
    """
    This function updates the jobad status from REQUESTED to REJECTED.
    """
    jobad = get_object_or_404(Jobad, pk=pk)

    if jobad.stage != 'requested':
        return JsonResponse({'success': False, 'message': 'Jobad is not in requested state.'}, status=400)

    jobad.stage = 'rejected'
    jobad.save()

    # Find the department structure
    department_structure = JobadDepartment.objects.filter(jobad=jobad).first()
    department_structure = department_structure.structure if department_structure else None

    # Notify suppliers
    notify_suppliers(jobad, department_structure, request)

    return JsonResponse({'success': True})

def notify_suppliers(jobad, department_structure, request):
    """
    Notify all supplier users linked to the exact department about the new jobad.
    """

    company_partners = CompanyPartner.objects.filter(
        structure=department_structure,
        company__company_type='supplier'
    ).select_related('company')

    base_url = "https://ppms.proserv-dl.de"
    jobad_url = f"{base_url}/jobads/{jobad.id}"
    formatted_shifts = "\n".join([f"{shift['start']} - {shift['end']}" for shift in jobad.working_hours])

    subject = f"Neue Stellenanzeige {jobad.position.title} wurde hochgeladen"
    for partner in company_partners:
        supplier = partner.company
        supplier_users = UserProfile.objects.filter(company=supplier, user__is_active=True)

        for user in supplier_users:
            context = {
                "jobad_title": jobad.position.title,
                "jobad_url": jobad_url,
                "jobad_company": jobad.company,
                "jobad_location": f"{jobad.location.street} {jobad.location.street_number}, {jobad.location.city}",
                "supplier_name": user.user.get_full_name(),
                "supplier_company": supplier.name,
                "action_by": f"{request.user.first_name} {request.user.last_name}",
                "time_period": jobad.time_period,
                "total": jobad.total,
                "department_value": jobad.department_value,
                "jobad_link": f"{request.build_absolute_uri(reverse('jobad-detail', kwargs={'pk': jobad.id}))}",
                "logo_url": f"{request.build_absolute_uri(static('images/faviconn.png'))}",
                "start_date": jobad.start_date.strftime('%d-%m-%Y'),
                "tasks": jobad.tasks,
                "requirements": jobad.requirements,
                "shifts": formatted_shifts,
                "weekly_working_hours": jobad.weekly_working_hours,
            }
            body = render_to_string("emails/new_jobad_created.html", context)

            # Send email asynchronously using Celery
            send_async_email.delay(user.user.email, subject, body)

class SelectCompanyForm(forms.Form):
    company = forms.ModelChoiceField(
        queryset=Company.objects.filter(company_type='customer'),
        label='Select Company',
        widget=forms.Select(attrs={
            'class': 'bg-white border-2 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-accent-500 focus:border-accent-500 block w-full p-2.5'
        })
    )

class JobadSelectCompanyView(FormView):
    template_name = 'jobad/select_company.html'
    form_class = SelectCompanyForm
    success_url = reverse_lazy('jobad-add-step2')
    
    def form_valid(self, form):
        print("hello")
        company = form.cleaned_data['company']
        selected_department = self.request.POST.get('selected_department')
        company_value = self.request.POST.get('company_value')
        selected_department_structure = self.request.POST.get('selected_department_structure')

        # Check if department values are valid
        if not selected_department or not selected_department_structure:
            messages.error(self.request, 'Please select a department.', extra_tags='bg-red-500 text-black')
            return self.form_invalid(form)

        # Store the selected company and department in the session (so we can retrieve it in Step 2)
        self.request.session['selected_company'] = company.id
        self.request.session['selected_department'] = selected_department
        self.request.session['company_value'] = company_value
        self.request.session['selected_department_structure'] = selected_department_structure
        
        print("Session data:", self.request.session['selected_company'])
        print("Form errors:", form.errors)
        return redirect('jobad-add-step2', company_id=company.id)

    def get_context_data(self, **kwargs):
        print("entering to get_context_data")
        context = super().get_context_data(**kwargs)
        user_profile = self.request.user.userprofile

        # Filter companies based on user's company type and role
        if user_profile.company.company_type == 'customer':
            # Restrict to the user's company
            companies = Company.objects.filter(id=user_profile.company.id)
        elif user_profile.company.company_type == 'vendor':
            linked_customer_ids = CompanyPartner.objects.filter(
                company=user_profile.company
            ).values_list('customer_id', flat=True)
            companies = Company.objects.filter(id__in=linked_customer_ids, company_type='customer')
        else:
            # Default behavior: show all companies of type 'customer'
            companies = Company.objects.filter(company_type='customer')

        context['companies'] = companies

        # Set a default structure to ensure the headers always exist
        selected_company_id = self.request.session.get('selected_company')
        if selected_company_id:
            company = get_object_or_404(Company, id=selected_company_id)
            context['structure'] = company.structure or ["Unternehmen", "Standort", "Gebäude", "Abteilung"]
        else:
            context['structure'] = ["Unternehmen", "Standort", "Gebäude", "Abteilung"]

        return context

def change_jobad_status(request, pk):
    if request.method == 'POST':
        jobad = get_object_or_404(Jobad, pk=pk)
        data = json.loads(request.body)
        new_status = data.get('new_status')

        if new_status == 'open_for_everyone':
            jobad.stage = 'open_for_everyone'
            jobad.total += 1
        elif new_status == 'filled':
            jobad.stage = 'filled'
            jobad.total = jobad.occupied
        elif new_status == 'completed':
            jobad.stage = 'completed'
        elif new_status == 'rejected':
            jobad.stage = 'rejected'

        jobad.save()

        # Retrieve the jobad's department structure from JobadDepartment
        jobad_department = JobadDepartment.objects.filter(jobad=jobad).first()
        structure_value = jobad_department.structure if jobad_department else None

        users_to_notify = UserProfile.objects.none()
        if structure_value:
            partner_qs = CompanyPartner.objects.filter(structure=structure_value)
            partner_company_ids = partner_qs.values_list('company_id', flat=True).distinct()
            users_to_notify = UserProfile.objects.filter(company_id__in=partner_company_ids,user__is_active=True)
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )
        recipients = list(users_to_notify) + list(global_admins)

        action_by = request.user.get_full_name() or "System"
        for user_profile in recipients:
            recipient_email = user_profile.user.email
            context = {
                "jobad_title": jobad.position.title,
                "jobad_status": jobad.stage,
                "jobad_url": f"https://ppms.proserv-dl.de/jobads/{jobad.id}/?tab=details",
                "open_positions": jobad.total - jobad.occupied if jobad.stage == "open_for_everyone" else None,
                "action_by": action_by,
            }
            subject = _("Statusänderung: Stellenanzeige: {jobad_title}, jetzt {jobad_status}").format(
                jobad_title=context["jobad_title"],
                jobad_status=format_stage(jobad.stage)
            )
            body = render_to_string("emails/jobad_status_changed.html", context)

            # ✅ Envío asincrónico para cada usuario
            send_async_email.delay(recipient_email, subject, body)

        return JsonResponse({'success': True})

    return JsonResponse({'success': False, 'error': 'Invalid request'}, status=400)

def jobad_detail(request, pk):
    jobad = get_object_or_404(Jobad, pk=pk)
    open_positions = jobad.total - jobad.occupied
    publisher = jobad.publisher
    workers = Worker.objects.filter(jobad=jobad)
    jobad_documents = JobaDocument.objects.filter(jobad=jobad)

    user = request.user
    user_company = user.userprofile.company

    # Filter companies for the Upload Modal based on the user's company type
    if user_company.company_type == 'global':
        filtered_companies = Company.objects.filter(company_type__in=['supplier', 'vendor'])
    elif user_company.company_type in ['supplier', 'vendor']:
        filtered_companies = Company.objects.filter(pk=user_company.pk)
    else:
        filtered_companies = Company.objects.none()

    candidate_form = CandidateForm()
    candidate_form.fields['company'].queryset = filtered_companies

    if request.method == 'POST':
        data = json.loads(request.body.decode('utf-8'))
        jobad.total = data.get('total', jobad.total)
        jobad.weekly_working_hours = data.get('weekly_working_hours', jobad.weekly_working_hours)
        jobad.employee_group = data.get('employee_group', jobad.employee_group)
        jobad.time_period = data.get('time_period', jobad.time_period)
        jobad.tasks = data.get('tasks', jobad.tasks)
        jobad.requirements = data.get('requirements', jobad.requirements)
        jobad.cost_department = data.get('cost_department', jobad.cost_department)
        jobad.working_hours = data.get('working_hours', [])
        start_date = data.get('start_date', None)
        if start_date:
            jobad.start_date = start_date

        current_contact = jobad.contact or {}
        jobad.contact = {
            'contact_name': data.get('contact_name', current_contact.get('contact_name', '')),
            'contact_phone': data.get('contact_phone', current_contact.get('contact_phone', '')),
        }
        jobad.save()
        return JsonResponse({'success': True})

    tab = request.GET.get('tab', 'details')
    sort_option = request.GET.get('sort', '-created_date') 
    allowed_tabs = ['details', 'candidate-profile', 'worker-deployment', 'history', 'jobad-settings']
    if tab not in allowed_tabs:
        tab = 'details'

    # Annotate candidate applications with rejection reason text.
    candidate_rejection_qs = RejectedCandidateReason.objects.filter(
        applicant=OuterRef('candidate')
    ).values('reason__text')[:1]
    jobad_applications = JobadApplication.objects.filter(jobad=jobad)\
        .select_related('candidate')\
        .annotate(
            rejection_reason_text=Subquery(
                RejectedCandidateReason.objects.filter(
                    applicant=OuterRef('candidate')
                ).values('reason__text')[:1]
            )
        )
        
    active_applications = jobad_applications.exclude(
        stage__in=['canceled', 'rejected', 'interview_rejected', 'selection_rejected', 'completed']
    )
    active_candidate_ids = list(active_applications.values_list('candidate_id', flat=True))
    filtered_applications = jobad_applications
    jobad_structure_ids = JobadDepartment.objects.filter(jobad=jobad).values_list('structure', flat=True)
    candidates = []
    imported_candidate_ids = []

    if tab == 'candidate-profile':
        if user_company.company_type == 'customer':
            linked_companies = list(
                CompanyPartner.objects.filter(customer=user_company)
                .values_list('company_id', flat=True)
            )
            candidates = Candidate.objects.filter(
                pk__in=Subquery(active_applications.values('candidate_id')),
                company__in=linked_companies
            ).distinct()
        elif user_company.company_type == 'supplier':
            candidates = Candidate.objects.filter(
                company=user_company
            ).distinct()
            active_applications = active_applications.filter(candidate__company=user_company)
        elif user_company.company_type in ['global', 'vendor']:
            linked_company_ids = CompanyPartner.objects.filter(
                customer=jobad.company,
                structure__in=jobad_structure_ids
            ).values_list('company_id', flat=True)
            imported_candidate_ids = jobad_applications.values_list('candidate_id', flat=True)
            candidates = Candidate.objects.filter(
                company_id__in=linked_company_ids
            ).exclude(id__in=imported_candidate_ids).distinct()

            filtered_applications = active_applications.order_by('-created_date')
        else:
            candidates = Candidate.objects.none()

        imported_candidate_ids = jobad_applications.values_list('candidate_id', flat=True)
        if sort_option in ['full_name', '-full_name']:
            active_applications = active_applications.annotate(
                full_name=Concat('candidate__firstname', Value(' '), 'candidate__lastname')
            ).order_by(sort_option)
        else:
            active_applications = active_applications.order_by(sort_option)

        filtered_applications = active_applications 

    elif tab == 'worker-deployment':
        if user_company.company_type == 'customer':
            linked_companies = list(
                CompanyPartner.objects.filter(customer=user_company).values_list('company', flat=True)
            )
            workers = Worker.objects.filter(jobad=jobad, candidate__company__in=linked_companies)
        elif user_company.company_type == 'supplier':
            workers = Worker.objects.filter(jobad=jobad, candidate__company=user_company)
        elif user_company.company_type in ['global', 'vendor']:
            workers = Worker.objects.filter(jobad=jobad)
        workers = workers.exclude(stage__in=['deployment_finished', 'deployment_discontinued', 'deployment_discontinued_as_supplier']).order_by('-start_date')

    elif tab == 'history':
        filtered_applications = jobad_applications.filter(
            stage__in=['canceled', 'rejected', 'interview_rejected', 'selection_rejected']
        ).order_by('-created_date')
        if user_company.company_type == 'supplier':
            filtered_applications = filtered_applications.filter(candidate__company=user_company)
        
        # Annotate worker history with rejection reason text.
        worker_rejection_qs = WorkerRejectionReason.objects.filter(
            worker=OuterRef('pk')
        ).values('reason__text')[:1]
        if user_company.company_type == 'customer':
            worker_history = Worker.objects.filter(jobad=jobad)
        elif user_company.company_type == 'supplier':
            worker_history = Worker.objects.filter(jobad=jobad, candidate__company=user_company)
        elif user_company.company_type in ['global', 'vendor']:
            worker_history = Worker.objects.filter(jobad=jobad)
        worker_history = worker_history.filter(
            stage__in=['deployment_finished', 'deployment_discontinued', 'deployment_discontinued_as_supplier']
        ).annotate(rejection_reason_text=Subquery(worker_rejection_qs))
        
        # Calculate the count of workers in history.
        worker_history_count = worker_history.count()
    else:
        worker_history = None

    tasks = [task.strip() for task in jobad.tasks.split('.') if task.strip()]
    requirements = [req.strip() for req in jobad.requirements.split('.') if req.strip()]
    tasks = jobad.tasks.splitlines() if jobad.tasks else []
    requirements = jobad.requirements.splitlines() if jobad.requirements else []
    combined_data = list(zip_longest(requirements, tasks, fillvalue=""))
    companies_list = list(filtered_companies.values('pk', 'name'))
    context = {
        'jobad': jobad,
        'open_positions': open_positions,
        'tab': tab,
        'jobad_documents': jobad_documents,
        'publisher': publisher,
        'allowed_tabs': allowed_tabs,
        'jobad_applications': filtered_applications,
        'filtered_count': filtered_applications.count(),
        'candidates': candidates,
        'tasks': tasks,
        'requirements': requirements,
        'imported_candidate_ids': imported_candidate_ids,
        'companies': filtered_companies,
        'form': candidate_form,
        'workers': workers,
        'worker_history': worker_history if tab == 'history' else None,
        'worker_history_count': worker_history_count if tab == 'history' else 0,
        'companies_json': json.dumps(companies_list, cls=DjangoJSONEncoder),
        'EUROPEAN_COUNTRIES': EUROPEAN_COUNTRIES,
        'user_role_number': request.user.userprofile.role,
        'combined_data': combined_data,
        'user_company_type': request.user.userprofile.company.company_type,
    }

    return render(request, 'jobad/jobad_detail.html', context)

def application_details(request, application_id):
    application = get_object_or_404(JobadApplication, id=application_id)
    data = model_to_dict(application)  # You can also build a custom dict

    # Include the rejection reason if the application was rejected/canceled
    if application.stage in ['rejected', 'canceled']:
        try:
            # Adjust this lookup as needed. Here, we assume the 'applicant' field on the model is a FK to Candidate.
            rejection = RejectedCandidateReason.objects.get(applicant=application.candidate)
            data['rejection_reason'] = rejection.reason.text
        except RejectedCandidateReason.DoesNotExist:
            data['rejection_reason'] = ''
    
    # (Add similar logic for WorkerRejectionReason if needed.)

    return JsonResponse(data)

def import_candidate(request, pk):
    jobad = get_object_or_404(Jobad, pk=pk)
    if request.method == 'POST':
        candidate_ids = request.POST.getlist('candidates')
        successfully_imported = []

        # Get structure of the jobad and all proposers linked to this structure
        structures = JobadDepartment.objects.filter(jobad=jobad).values_list('structure', flat=True)
        
        # Query proposers (role 5) using the structure filter.
        proposers = UserProfile.objects.filter(
            role=5,
            userprofiledepartments__structure__in=structures,
            user__is_active=True
        ).distinct()

        # Query admins from the job ad's company.
        admins_from_jobad = UserProfile.objects.filter(
            company=jobad.company,
            role=1,
            user__is_active=True
        )

        # Query admins from the PPMS company.
        admins_ppms = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        vendor_companies = Company.objects.filter(company_type='vendor')
        vendor_links = CompanyPartner.objects.filter(
            company__in=vendor_companies,
            structure__in=structures
        ).values_list('company_id', flat=True)
        vendor_users = UserProfile.objects.filter(
            company_id__in=vendor_links,
            user__is_active=True
        ).distinct()
        recipients = list(proposers) + list(admins_from_jobad) + list(admins_ppms) + list(vendor_users)

        for candidate_id in candidate_ids:
            candidate = get_object_or_404(Candidate, pk=candidate_id)
            if not JobadApplication.objects.filter(jobad=jobad, candidate=candidate).exists():
                try:
                    JobadApplication.objects.create(
                        jobad=jobad,
                        candidate=candidate,
                        stage='approved',
                        status='created',
                        created_date=timezone.now(),
                        created_by=request.user.userprofile
                    )
                    successfully_imported.append(candidate)
                    action_by = request.user.get_full_name()
                    base_url = "https://ppms.proserv-dl.de"
                    jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
                    print(f"department_value: {jobad.department_value}")
                    print(f"department_value: {jobad.location.street + " " + jobad.location.street_number + ", " + jobad.location.city}")

                    for recipient in recipients:
                        context = {
                            "jobad_title": jobad.position.title,
                            "jobad_location": jobad.location.street + " " + jobad.location.street_number + ", " + jobad.location.city,
                            "jobad_department": jobad.department_value,
                            "jobad_url": jobad_url,
                            "candidate_name": f"{candidate.firstname} {candidate.lastname}",
                            "company_name": candidate.company.name,
                            "publisher_name": recipient.user.get_full_name(),
                            "action_by": action_by,
                        }
                        subject = f"Kandidat importiert: {candidate.firstname} {candidate.lastname}"
                        body = render_to_string("emails/candidate_imported.html", context)
                        send_async_email.delay(recipient.user.email, subject, body)
                except IntegrityError as e:
                    print(f"Failed to import candidate {candidate_id}: {e}")

        messages.success(request, f'Imported {len(successfully_imported)} new candidates successfully.')
    return redirect(f"{reverse('jobad-detail', kwargs={'pk': pk})}?tab=candidate-profile")

def upload_candidate(request, pk):
    jobad = get_object_or_404(Jobad, pk=pk)
    if request.method == 'POST':
        form = CandidateForm(request.POST, request.FILES)
        if form.is_valid():
            firstname = form.cleaned_data['firstname']
            lastname = form.cleaned_data['lastname']
            company = form.cleaned_data['company']
            
            if Candidate.objects.filter(firstname=firstname, lastname=lastname, company=company).exists():
                return JsonResponse({
                    'success': False,
                    'errors': {'firstname': ['Candidate with this name already exists in the selected company.']}
                }, status=400)
            
            candidate = form.save(commit=False)
            g25_required = jobad.g25_selected
            fuhrerschein_required = jobad.fuhrerschein_selected
            country_code = form.cleaned_data.get('country')
            
            file_mappings = {
                'documents': DocumentType.CV,
                'workpermit': DocumentType.WORK_PERMIT,
                'fuhrerschein': DocumentType.FUHRERSCHEIN,
                'g25': DocumentType.STAPLERSCHEIN
            }
            
            errors = {}
            if g25_required and 'g25' not in request.FILES:
                errors['g25'] = ['Staplerschein (G25) document is required for this job ad.']
            if fuhrerschein_required and 'fuhrerschein' not in request.FILES:
                errors['fuhrerschein'] = ['Fuhrerschein document is required for this job ad.']
            if country_code not in EUROPEAN_COUNTRIES and 'workpermit' not in request.FILES:
                errors['workpermit'] = ['Work permit is required for candidates outside of Europe.']
            
            if errors:
                return JsonResponse({'success': False, 'errors': errors}, status=400)
            
            candidate.save()
            for field_name, doc_type in file_mappings.items():
                file = request.FILES.get(field_name)
                if file:
                    CandidateDocument.objects.create(
                        candidate=candidate,
                        document=file,
                        document_type=doc_type,
                        uploaded_by=request.user.userprofile
                    )

            JobadApplication.objects.create(
                jobad=jobad,
                candidate=candidate,
                stage='approved',
                created_by=request.user.userprofile,
                created_date=timezone.now()
            )
            
            # Notify proposers, admin from jobad and ppms
            structures = JobadDepartment.objects.filter(jobad=jobad).values_list('structure', flat=True)
            proposers = UserProfile.objects.filter(
                role=5,
                userprofiledepartments__structure__in=structures,
                user__is_active=True
            ).distinct()
            
            # Query admins from the job ad's company.
            admins_from_jobad = UserProfile.objects.filter(
                company=jobad.company,
                role=1,
                user__is_active=True
            )
            
            # Query admins from the PPMS company.
            admins_ppms = UserProfile.objects.filter(
                company__name__iexact="ppms",
                role=1,
                user__is_active=True
            )
            
            vendor_companies = Company.objects.filter(company_type='vendor')
            vendor_links = CompanyPartner.objects.filter(
                company__in=vendor_companies,
                structure__in=structures
            ).values_list('company_id', flat=True)

            vendor_users = UserProfile.objects.filter(
                company_id__in=vendor_links,
                user__is_active=True
            ).distinct()

            # Combine all recipients
            recipients = list(proposers) + list(admins_from_jobad) + list(admins_ppms) + list(vendor_users)

            
            action_by = f"{request.user.first_name} {request.user.last_name}"
            base_url = "https://ppms.proserv-dl.de"
            jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
            print(f"department_value: {jobad.department_value}")
            print(f"department_value: {jobad.location.street + " " + jobad.location.street_number + ", " + jobad.location.city}")
            for recipient in recipients:
                context = {
                    "jobad_title": jobad.position.title,
                    "jobad_location": jobad.location.street + " " + jobad.location.street_number + ", " + jobad.location.city,
                    "jobad_department": jobad.department_value,
                    "jobad_url": jobad_url, 
                    "candidate_name": f"{candidate.firstname} {candidate.lastname}",
                    "company_name": candidate.company.name,
                    "recipient_name": recipient.user.get_full_name(),
                    "action_by": action_by,
                }
                subject = f"Neuer Kandidat für Ihre Stellenanzeige: {candidate.firstname} {candidate.lastname}"
                body = render_to_string("emails/candidate_uploaded.html", context)
                send_async_email.delay(recipient.user.email, subject, body)
            
            messages.success(request, 'Candidate created successfully')
            return JsonResponse({'success': True})
        
        return JsonResponse({'success': False, 'errors': form.errors}, status=400)
    return JsonResponse({'success': False}, status=400)

class JobadFavouritesView(ListView):
    model = Jobad
    template_name = 'jobad/jobad_list.html'
    context_object_name = 'jobads'

    def get_queryset(self):
        return Jobad.objects.filter(favourite=True)

class JobadArchivedView(ListView):
    model = Jobad
    template_name = 'jobad/jobad_list.html'
    context_object_name = 'jobads'

    def get_queryset(self):
        return Jobad.objects.filter(status='archived')

class JobadDeleteView(DeleteView):
    model = Jobad
    template_name = 'jobad/jobad_detail.html'
    success_url = reverse_lazy('jobad-list')

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, 'Jobad successfully deleted')
        return super().delete(request, *args, **kwargs)

def jobad_application_details(request, pk):
    try:
        application = JobadApplication.objects.get(pk=pk)
        selection = Selection.objects.filter(application=application).first()  # Fetch related Selection if exists

        rejection_reason_text = None
        if application.stage == 'rejected':
            rejection_reason = RejectedCandidateReason.objects.filter(applicant=application.candidate).first()
            if rejection_reason:
                rejection_reason_text = rejection_reason.reason.text

        application_data = {
            'id': application.id,
            'candidate_name': f'{application.candidate.firstname} {application.candidate.lastname}',
            'stage': application.stage,
            'start_date': selection.start_date if selection else None,
            'end_date': selection.end_date if selection else None,
            'selection_stage': selection.stage if selection else None,
            'interview_skipped': application.interview_skipped,
            'start_time': selection.start_time if selection else None,
            'contact_person_name': selection.contact_person_name if selection else None,
            'contact_person_phone': selection.contact_person_phone if selection else None,
            'company_id': application.jobad.company_id,
            'rejection_reason': rejection_reason_text,
            'last_suggestion_by': application.last_suggestion_by,
        }

        return JsonResponse(application_data)
    except JobadApplication.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'JobadApplication not found'}, status=404)

@csrf_exempt
def create_selection(request, pk):
    if request.method == 'POST':
        data = json.loads(request.body)
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        start_time = data.get('start_time')
        contact_person_name = data.get('contact_person_name')
        contact_person_phone = data.get('contact_person_phone')

        # Find the JobadApplication
        try:
            application = JobadApplication.objects.get(pk=pk)
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'JobadApplication not found'})
        
        jobad = application.jobad  # Retrieve related job ad
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
        
        # Try to get the UserProfile if it exists
        user_profile = None
        try:
            user_profile = request.user.userprofile
        except AttributeError:
            pass

        requiresApproval = "lgi" not in jobad.company.name.lower()

        # Create the Selection related to the JobadApplication
        selection = Selection.objects.create(
            application=application,
            start_date=start_date,
            end_date=end_date,
            start_time=start_time,
            contact_person_name=contact_person_name,
            contact_person_phone=contact_person_phone,
            stage='approved',
            created_by=user_profile,
            modified_by=user_profile
        )

        application.stage = 'requested' if requiresApproval else 'candidate_selection'
        application.modified_by = user_profile
        application.modified_date = timezone.now()
        application.save()

        formatted_start_date = datetime.strptime(start_date, '%Y-%m-%d').strftime('%d-%m-%Y')
        formatted_end_date = datetime.strptime(end_date, '%Y-%m-%d').strftime('%d-%m-%Y')

        # Prepare a common context for emails
        common_context = {
            "candidate_name": f"{application.candidate.firstname} {application.candidate.lastname}",
            "start_date": formatted_start_date,
            "end_date": formatted_end_date,
            "start_time": start_time,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
        }
        subject = f"Zusage erteilt – Kandidat {common_context['candidate_name']}"

        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )
        # Notify all supplier users
        supplier_users = UserProfile.objects.filter(
            company=application.candidate.company,
            user__is_active=True
        ).distinct()
        recipients = list(supplier_users) + list(global_admins)
        print(f"Recipients for selection: {[user.user for user in recipients]}")
        for user in recipients:
            context = common_context.copy()
            context["recipient_name"] = user.user.get_full_name()
            # You can adjust this value based on your business logic.
            context["accepted_by"] = "Customer"
            body = render_to_string("emails/selection_details.html", context)
            send_async_email.delay(user.user.email, subject, body)

        # Extra vendor notification logic
        try:
            # Retrieve the JobadDepartment entry for this job ad.
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            # Use the 'structure' field inherited from StructureDependency.
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        if dept_structure:
            # Filter for CompanyPartner entries that already belong to vendor companies.
            company_partners = CompanyPartner.objects.filter(
                structure=dept_structure,
                company__company_type='vendor'
            )
            vendor_ids = company_partners.values_list('company', flat=True).distinct()
            # Since we already filtered by vendor, this should return only vendor companies.
            vendor_companies = Company.objects.filter(id__in=vendor_ids)
            vendor_users = UserProfile.objects.filter(
                company__in=vendor_companies,
                user__is_active=True
            ).distinct()
            recipients = list(vendor_users) + list(global_admins)

            for vendor_user in recipients:
                vendor_context = common_context.copy()
                vendor_context["recipient_name"] = vendor_user.user.get_full_name()
                # Customize based on your business rules—here, we set it based on requiresApproval.
                vendor_context["accepted_by"] = "Customer" if requiresApproval else "Supplier"
                body = render_to_string("emails/selection_details.html", vendor_context)
                send_async_email.delay(vendor_user.user.email, subject, body)

        return JsonResponse({'success': True, "requiresApproval": requiresApproval, "stage": application.stage})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


def approve_candidate(request, pk):
    if request.method == 'POST':
        try:
            application = JobadApplication.objects.get(pk=pk)
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'JobadApplication not found'})

        user_profile = request.user.userprofile

        if user_profile.company.company_type not in ['customer', 'global'] or user_profile.role == 5:
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Get job ad details
        jobad = application.jobad
        jobad_department = jobad.department_value

        try:
            department_structure = CompanyStructure.objects.get(
                company=jobad.company, value=jobad_department
            ).id
        except CompanyStructure.DoesNotExist:
            return JsonResponse({'success': False, 'error': f'Department {jobad_department} not found in CompanyStructure'})

        candidate_company = application.candidate.company
        supplier_users = UserProfile.objects.filter(
            company=candidate_company,
            user__is_active=True
        ).distinct()


        if not supplier_users.exists():
            return JsonResponse({'success': True, 'warning': 'No supplier users found'})

        application.stage = 'candidate_selection'
        application.modified_by = user_profile
        application.modified_date = timezone.now()
        application.save()

        selection = Selection.objects.filter(application=application).order_by('-id').first()
        formatted_start_date = selection.start_date.strftime('%d-%m-%Y') if selection and selection.start_date else "N/A"
        formatted_end_date = selection.end_date.strftime('%d-%m-%Y') if selection and selection.end_date else "N/A"

        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
        candidate_name = f"{application.candidate.firstname} {application.candidate.lastname}"
        jobad_title = jobad.position.title

        common_context = {
            "candidate_name": candidate_name,
            "jobad_title": jobad_title,
            "start_date": formatted_start_date,
            "end_date": formatted_end_date,
            "start_time": selection.start_time if selection else "N/A",
            "contact_person_name": selection.contact_person_name if selection else "N/A",
            "contact_person_phone": selection.contact_person_phone if selection else "N/A",
            "jobad_url": jobad_url,
        }

        subject = f"Candidate Approved – {candidate_name} ({jobad_title})"

        # 8) Global admins
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        # 9) Recipients = supplier_users + global_admins
        recipients = list(supplier_users) + list(global_admins)

        # 10) Envío de correos
        for user in recipients:
            context = common_context.copy()
            context["recipient_name"] = user.user.get_full_name()
            body = render_to_string("emails/candidate_approved.html", context)
            send_async_email.delay(user.user.email, subject, body)

        return JsonResponse({'success': True})

    return JsonResponse({'success': False, 'error': 'Invalid request method'}, status=400)

def skip_interview(request, pk):
    try:
        # Fetch the JobadApplication
        application = JobadApplication.objects.get(pk=pk)
        application.interview_skipped = True
        application.stage = 'interview_completed'
        application.save()

        candidate_name = f"{application.candidate.firstname} {application.candidate.lastname}"
        jobad = application.jobad
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
        subject = f"Kandidat akzeptiert ohne Interview/Probetag: {candidate_name}"

        # Build a common email context for both supplier and vendor notifications.
        common_context = {
            "candidate_name": candidate_name,
            "jobad_url": jobad_url,
            "jobad_title": jobad.position.title,
        }

        # 3) Query original de supplier_users
        supplier_users = UserProfile.objects.filter(
            company=application.candidate.company,
            user__is_active=True
        ).distinct()

        # 4) Query de global admins
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        recipients = list(supplier_users) + list(global_admins)
        print(f"Recipients for skip interview: {[user.user for user in recipients]}")
        for user in recipients:
            context = common_context.copy()
            context["recipient_name"] = user.user.get_full_name()
            body = render_to_string("emails/skip_interview.html", context)
            send_async_email.delay(user.user.email, subject, body)

        # Extra vendor notification logic:
        try:
            # Retrieve the JobadDepartment entry for this job ad.
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            # Retrieve the department structure (stored in 'structure' from StructureDependency)
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        if dept_structure:
            # Filter CompanyPartner entries to only those with the matching department structure
            # and where the related company has company_type 'vendor'
            company_partners = CompanyPartner.objects.filter(
                structure=dept_structure,
                company__company_type='vendor'
            )
            vendor_ids = company_partners.values_list('company', flat=True).distinct()
            vendor_companies = Company.objects.filter(id__in=vendor_ids)
            vendor_users = UserProfile.objects.filter(
                company__in=vendor_companies,
                user__is_active=True
            ).distinct()
            recipientsvendor = list(vendor_users) + list(global_admins)
            for vendor_user  in recipientsvendor:
                vendor_context = common_context.copy()
                vendor_context["recipient_name"] = vendor_user.user.get_full_name()
                body = render_to_string("emails/skip_interview.html", vendor_context)
                send_async_email.delay(vendor_user.user.email, subject, body)

        return JsonResponse({'success': True})
    except JobadApplication.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'JobadApplication not found'})
    except Exception as e:
        print(f"Error in skip_interview: {e}")
        return JsonResponse({'success': False, 'error': 'An unexpected error occurred'}, status=500)

@csrf_exempt
def confirm_selection(request, pk):
    if request.method == 'POST':
        try:
            # Retrieve the JobadApplication
            application = JobadApplication.objects.get(pk=pk)
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'JobadApplication not found'})

        try:
            # Retrieve the related Selection for the JobadApplication
            selection = Selection.objects.get(application=application)
        except Selection.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Selection not found'})

        # Update the Selection stage to 'completed'
        selection.stage = 'completed'
        selection.save()

        jobad = application.jobad
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"

        # Prepare a common email context
        common_context = {
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
            "candidate_name": f"{application.candidate.firstname} {application.candidate.lastname}",
            "company_name": application.candidate.company.name,
            "start_date": selection.start_date.strftime('%d-%m-%Y'),
            "end_date": selection.end_date.strftime('%d-%m-%Y'),
            "start_time": selection.start_time,
            "contact_person_name": selection.contact_person_name,
            "contact_person_phone": selection.contact_person_phone,
        }
        subject = f"Zusage {common_context['candidate_name']} Start {common_context['start_date']}"

        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )

        # Notify proposers linked to the job ad's structure
        structures = JobadDepartment.objects.filter(jobad=application.jobad).values_list('structure', flat=True)
        proposers = UserProfile.objects.filter(
            role=5,  # Assuming role 5 corresponds to proposers
            userprofiledepartments__structure__in=structures,
            user__is_active=True
        ).distinct()
        
        recipients = list(proposers) + list(global_admins)
        for user in recipients:
            context = common_context.copy()
            context["recipient_name"] = user.user.get_full_name()
            body = render_to_string("emails/selection_confirmed.html", context)
            send_async_email.delay(user.user.email, subject, body)
        print(f"proposers: {proposers}")

        # Extra vendor notification logic
        try:
            # Retrieve the JobadDepartment entry for this job ad.
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            # Use the inherited field 'structure'
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        if dept_structure:
            # Filter for CompanyPartner entries that match the department structure
            # and are linked to companies with company_type 'vendor'
            company_partners = CompanyPartner.objects.filter(
                structure=dept_structure,
                company__company_type='vendor'
            )
            vendor_ids = company_partners.values_list('company', flat=True).distinct()
            vendor_companies = Company.objects.filter(id__in=vendor_ids)
            vendor_users = UserProfile.objects.filter(
                company__in=vendor_companies,
                user__is_active=True
            ).distinct()
            recipients = list(vendor_users) + list(global_admins)
            for user in recipients:
                vendor_context = common_context.copy()
                vendor_context["recipient_name"] = user.user.get_full_name()
                body = render_to_string("emails/selection_confirmed.html", vendor_context) 
                send_async_email.delay(user.user.email, subject, body)

        # Update the JobadApplication stage to 'tentatively_occupied'
        application.stage = 'tentatively_occupied'
        application.modified_date = timezone.now()
        application.save()

        return JsonResponse({'success': True})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@csrf_exempt
def confirm_deployment(request, pk):
    if request.method == 'POST':
        data = json.loads(request.body)
        final_start_date = data.get('final_start_date')
        
        # Parse the date string into a timezone-aware datetime object
        try:
            final_start_date = timezone.make_aware(
                datetime.strptime(final_start_date, '%Y-%m-%d')
            )
        except ValueError:
            return JsonResponse({'success': False, 'error': 'Invalid date format'})

        # Retrieve the JobadApplication
        try:
            application = JobadApplication.objects.get(pk=pk)
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'JobadApplication not found'})

        # Retrieve the related Selection for this application
        try:
            selection = Selection.objects.get(application=application)
        except Selection.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Selection not found'})

        # Update Selection and JobadApplication stages
        selection.start_date = final_start_date  # Set the exact start date
        selection.stage = 'completed'
        selection.save()

        application.stage = 'completed'
        application.save()

        # Create a new Worker entity (i.e., candidate becomes a worker)
        worker = Worker.objects.create(
            jobad=application.jobad,
            candidate=application.candidate,
            start_date=selection.start_date,
            end_date=selection.end_date,
            stage=WorkerStage.APPROVED,
        )
        
        # Update the Jobad's occupied value and stage, if needed
        jobad = application.jobad
        jobad.occupied += 1  # Increment occupied count

        if jobad.occupied >= jobad.total:
            jobad.stage = 'filled'
        jobad.save()

        # Prepare base URL and job ad URL for emails
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=worker-deployment"
        formatted_final_start_date = final_start_date.strftime('%d-%m-%Y')

        # Build a common email context used by both supplier and vendor notifications
        common_context = {
            "candidate_name": f"{application.candidate.firstname} {application.candidate.lastname}",
            "final_start_date": formatted_final_start_date,
            "start_date": selection.start_date.strftime('%d-%m-%Y'),
            "end_date": selection.end_date.strftime('%d-%m-%Y'),
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
        }
        subject = f"{common_context['candidate_name']} hat seinen Einsatz begonnen"

        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )
        # Notify all supplier users about deployment confirmation
        supplier_users = UserProfile.objects.filter(
            company=application.candidate.company,
            user__is_active=True
        ).distinct()
        recipients = list(supplier_users) + list(global_admins)
        for user in recipients:
            context = common_context.copy()
            context["recipient_name"] = user.user.get_full_name()
            body = render_to_string("emails/deployment_confirmation.html", context)
            send_async_email.delay(user.user.email, subject, body)

        # Extra Vendor Notification Logic
        # Instead of retrieving all CompanyPartner entries first, we filter by vendor at the ORM level.
        try:
            # Retrieve the JobadDepartment entry for this job ad.
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            # The department structure (from the abstract StructureDependency) is stored in 'structure'
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        if dept_structure:
            # Filter CompanyPartner records by department structure and ensure that the partner's company is a vendor.
            company_partners = CompanyPartner.objects.filter(
                structure=dept_structure,
                company__company_type='vendor'
            )
            # Get distinct vendor company IDs from these records.
            vendor_ids = company_partners.values_list('company', flat=True).distinct()
            vendor_companies = Company.objects.filter(id__in=vendor_ids)
            vendor_users = UserProfile.objects.filter(
                company__in=vendor_companies,
                user__is_active=True
            ).distinct()
            recipients = list(vendor_users) + list(global_admins)
            for user in recipients:
                vendor_context = common_context.copy()
                vendor_context["recipient_name"] = user.user.get_full_name()
                body = render_to_string("emails/deployment_confirmation.html", vendor_context)
                send_async_email.delay(user.user.email, subject, body)

        return JsonResponse({'success': True})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

def create_interview(request, application_id):
    if request.method == 'POST':
        data = json.loads(request.body)

        try:
            application = JobadApplication.objects.get(id=application_id)

            proposed_dates = [
                datetime.strptime(date, "%Y-%m-%d").strftime("%d/%m/%Y") 
                for date in data.get('interview_dates', []) if date
            ]

            proposed_dates_times = []
            for date, start, end in zip(proposed_dates, data.get('interview_start_times', []), data.get('interview_end_times', [])):
                if start and end:  # Only add if both times are present
                    proposed_dates_times.append({"date": date, "time": f"{start} - {end}"})

            contact_person_name = data.get('contact_person_name')
            contact_person_phone = data.get('contact_person_phone')

            # Create the interview entity with the correct field name
            Interview.objects.create(
                application=application,  # Use the correct field name here
                interview_dates=data.get('interview_dates', []),
                interview_start_times=data.get('interview_start_times', []),
                interview_end_times=data.get('interview_end_times', []),
                contact_person_name=contact_person_name,
                contact_person_phone=contact_person_phone,
                status=data.get('status', 'created')
            )

            # Update the application stage
            application.stage = data.get('stage', 'interview')
            application.save()

            # Prepare email content
            candidate_name = f"{application.candidate.firstname} {application.candidate.lastname}"
            jobad = application.jobad
            base_url = "https://ppms.proserv-dl.de"
            jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"
            email_context = {
                "candidate_name": candidate_name,
                "proposed_dates_times": proposed_dates_times,
                "jobad_url": jobad_url,
                "jobad_title": jobad.position.title,
                "contact_person_name": contact_person_name,
                "contact_person_phone": contact_person_phone,
            }
            subject = f"Einladung Interview/Probetag – Kandidat {candidate_name}"

            global_admins = UserProfile.objects.filter(
                company__name__iexact="ppms",
                role=1,
                user__is_active=True
            )
            supplier_company = application.candidate.company
            supplier_users = UserProfile.objects.filter(
                company=supplier_company,
                user__is_active=True
            )
            recipients = list(supplier_users) + list(global_admins)
            for user in recipients:
                context = email_context.copy()
                # You might want to personalize or add more info per supplier user.
                context["recipient_name"] = user.user.get_full_name()
                body = render_to_string("emails/interview_proposal.html", context)
                send_async_email.delay(user.user.email, subject, body)

            try:
                # Retrieve the JobadDepartment entry for this job ad.
                jobad_dept = JobadDepartment.objects.get(jobad=jobad)
                # Adjust the field name if necessary (here we assume it is stored in a field called 'department_structure')
                department_structure = jobad_dept.structure
                print(f"jobad_dept: {jobad_dept}")
                print(f"department_structure: {department_structure}")
            except JobadDepartment.DoesNotExist:
                department_structure = None  # Or handle accordingly if no department is set.

            if department_structure:
                # Find all CompanyPartner entries where the structure matches this department structure.
                company_partners = CompanyPartner.objects.filter(structure=department_structure)
                # Get distinct vendor company IDs from these entries.
                vendor_ids = company_partners.values_list('company', flat=True).distinct()
                # Query all vendor companies (with company_type 'vendor') that match.
                vendor_companies = Company.objects.filter(id__in=vendor_ids, company_type='vendor')
                # Get all active users from these vendor companies.
                vendor_users = UserProfile.objects.filter(
                    company__in=vendor_companies,
                    user__is_active=True
                )
                recipients = list(vendor_users) + list(global_admins)
                for user in recipients:
                    vendor_context = email_context.copy()
                    vendor_context["recipient_name"] = user.user.get_full_name()
                    vendor_body = render_to_string("emails/interview_proposal.html", vendor_context)
                    send_async_email.delay(user.user.email, subject, vendor_body)

            return JsonResponse({'success': True})
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Application not found'}, status=404)
        except Exception as e:
            # Log the exception for debugging
            print(f"Error in create_interview: {e}")
            return JsonResponse({'success': False, 'error': 'An unexpected error occurred'}, status=500)
    return JsonResponse({'success': False, 'error': 'Invalid request'}, status=400)

def interview_details(request, application_id):
    try:
        interview = Interview.objects.get(application_id=application_id)
        return JsonResponse({
            'interview_dates': interview.interview_dates,
            'interview_start_times': interview.interview_start_times,
            'interview_end_times': interview.interview_end_times,
            'stage': interview.stage,
            'contact_person_name': interview.contact_person_name,
            'contact_person_phone': interview.contact_person_phone
        })
    except Interview.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Interview not found'}, status=404)
    
def update_interview(request, application_id):
    if request.method == 'POST':
        data = json.loads(request.body)

        try:
            # Fetch the existing interview entity linked to the application
            interview = Interview.objects.get(application_id=application_id)
            
            # Update the interview with the new suggested date and times
            interview.interview_dates = [data.get('alternative_date')]  # Replace with the alternative date
            interview.interview_start_times = [data.get('alternative_start_time')]
            interview.interview_end_times = [data.get('alternative_end_time')]
            interview.stage = 'requested_again'  # Update stage to 'requested_again'
            interview.save()

            jobad_application = JobadApplication.objects.get(pk=application_id)
            jobad_application.last_suggestion_by = data.get('last_suggestion_by')  # Update last_suggestion_by
            jobad_application.save()

            jobad = jobad_application.jobad  # Retrieve related job ad
            base_url = "https://ppms.proserv-dl.de"
            jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"

            # Prepare some common email context data
            # Convert the alternative date to the desired format if present.
            alternative_date_formatted = ""
            if interview.interview_dates and interview.interview_dates[0]:
                alternative_date_formatted = datetime.strptime(interview.interview_dates[0], "%Y-%m-%d").strftime("%d/%m/%Y")
            
            common_context = {
                "candidate_name": f"{jobad_application.candidate.firstname} {jobad_application.candidate.lastname}",
                "alternative_date": alternative_date_formatted,
                "start_time": interview.interview_start_times[0] if interview.interview_start_times else "",
                "end_time": interview.interview_end_times[0] if interview.interview_end_times else "",
                "jobad_title": jobad.position.title,
                "jobad_url": jobad_url,
            }
            # Use a subject base that can be reused.
            subject = f"Interview/Probetag muss verschoben werden {common_context['candidate_name']}"
            global_admins = UserProfile.objects.filter(
                company__name__iexact="ppms",
                role=1,
                user__is_active=True
            )
            if data.get('last_suggestion_by') == 'supplier':
                # Notify proposers in the related department(s)
                structures = JobadDepartment.objects.filter(jobad=jobad_application.jobad).values_list('structure', flat=True)
                proposers = UserProfile.objects.filter(
                    role=5,
                    userprofiledepartments__structure__in=structures,
                    user__is_active=True
                ).distinct()
                print(f"we notify proposers here: {proposers}")
                recipients = list(proposers) + list(global_admins)
                for user in recipients:
                    context = common_context.copy()
                    # For the proposer branch, include additional information if needed.
                    context["recipient_name"] = user.user.get_full_name()
                    context["company_name"] = jobad_application.candidate.company.name
                    context["suggested_by"] = "supplier"
                    body = render_to_string("emails/alternative_suggestion.html", context)
                    send_async_email.delay(user.user.email, subject, body)
            else:
                # Notify all active supplier users of the candidate's company
                supplier_users = UserProfile.objects.filter(
                    company=jobad_application.candidate.company,
                    user__is_active=True
                ).distinct()
                print(f"we notify supplier_users here: {supplier_users}")
                recipients = list(supplier_users) + list(global_admins)
                for user in recipients:
                    context = common_context.copy()
                    context["recipient_name"] = user.user.get_full_name()
                    context["suggested_by"] = "customer"
                    body = render_to_string("emails/alternative_suggestion.html", context)
                    send_async_email.delay(user.user.email, subject, body)

            # =====================================================================
            # Extra notification: Notify vendor users based on the department structure.
            # =====================================================================
            try:
                # Retrieve the JobadDepartment entry for this job ad.
                jobad_dept = JobadDepartment.objects.get(jobad=jobad)
                # The field from StructureDependency is named "structure"
                dept_structure = jobad_dept.structure
                print(f"jobad_dept: {jobad_dept}")
                print(f"dept_structure: {dept_structure}")
            except JobadDepartment.DoesNotExist:
                dept_structure = None

            if dept_structure:
                # Find CompanyPartner entries where the structure matches the job ad's department structure.
                company_partners = CompanyPartner.objects.filter(structure=dept_structure)
                # Get distinct vendor company IDs from these entries.
                vendor_ids = company_partners.values_list('company', flat=True).distinct()
                # Query vendor companies that match and have company_type 'vendor'.
                vendor_companies = Company.objects.filter(id__in=vendor_ids, company_type='vendor')
                # Get all active users from these vendor companies.
                vendor_users = UserProfile.objects.filter(
                    company__in=vendor_companies,
                    user__is_active=True
                ).distinct()
                print(f"vendor_users: {vendor_users}")
                recipients = list(vendor_users) + list(global_admins)
                for user in recipients:
                    vendor_context = common_context.copy()
                    vendor_context["recipient_name"] = user.user.get_full_name()
                    # You might want to indicate who made the alternative suggestion.
                    vendor_context["suggested_by"] = data.get('last_suggestion_by', 'customer')
                    # Optionally add the candidate's company if needed.
                    vendor_context["company_name"] = jobad_application.candidate.company.name
                    vendor_body = render_to_string("emails/alternative_suggestion.html", vendor_context)
                    send_async_email.delay(user.user.email, subject, vendor_body)

            return JsonResponse({'success': True})
        except Interview.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Interview not found'}, status=404)
        except Exception as e:
            # Log the exception for debugging
            print(f"Error in update_interview: {e}")
            return JsonResponse({'success': False, 'error': 'An unexpected error occurred'}, status=500)

    return JsonResponse({'success': False, 'error': 'Invalid request'}, status=400)

@csrf_exempt
@require_POST
def update_interview_status(request, application_id):
    try:
        # Get the JobadApplication instance
        jobad_application = JobadApplication.objects.get(id=application_id)

        # Parse the JSON request body
        data = json.loads(request.body)
        accepted_date = data.get('chosen_date')
        accepted_start_time = data.get('chosen_start_time')
        accepted_end_time = data.get('chosen_end_time')

        # Update the Interview instance
        interview = Interview.objects.get(application=jobad_application)
        interview.stage = 'approved'
        interview.interview_dates = [accepted_date]  # Assuming it's stored as a list
        interview.interview_start_times = [accepted_start_time]
        interview.interview_end_times = [accepted_end_time]
        interview.save()

        contact_person_name = interview.contact_person_name
        contact_person_phone = interview.contact_person_phone

        # Update the JobadApplication instance
        jobad_application.stage = 'interview_completed'  # The corrected stage
        jobad_application.interview_skipped = False  # Ensure interview skipped is set to False
        jobad_application.save()

        jobad = jobad_application.jobad  # Retrieve related job ad
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"

        # Determine who is accepting the suggestion
        candidate_name = f"{jobad_application.candidate.firstname} {jobad_application.candidate.lastname}"
        last_suggestion_by = jobad_application.last_suggestion_by
        accepting_party = "supplier" if last_suggestion_by == "customer" else "customer"

        # Prepare a common context to reuse across notifications.
        common_context = {
            "candidate_name": candidate_name,
            "interview_date": accepted_date,
            "start_time": accepted_start_time,
            "end_time": accepted_end_time,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
        }
        subject = f"Interview/Probetag akzeptiert – Kandidat {candidate_name}"
        global_admins = UserProfile.objects.filter(
            company__name__iexact="ppms",
            role=1,
            user__is_active=True
        )
        # Primary notifications
        if accepting_party == "supplier":
            # Notify all active supplier users of the candidate's company
            supplier_users = UserProfile.objects.filter(
                company=jobad_application.candidate.company,
                user__is_active=True
            ).distinct()
            print(f"supplier_users: {supplier_users}")
            recipients = list(supplier_users) + list(global_admins)
            for user in recipients:
                context = common_context.copy()
                context["recipient_name"] = user.user.get_full_name()
                context["accepted_by"] = "Customer"  # Indicates that the customer accepted the alternative date
                body = render_to_string("emails/accepted_suggestion.html", context)
                send_async_email.delay(user.user.email, subject, body)
        else:
            # Notify all proposers of the related department
            structures = JobadDepartment.objects.filter(jobad=jobad_application.jobad).values_list('structure', flat=True)
            proposers = UserProfile.objects.filter(
                role=5,
                userprofiledepartments__structure__in=structures,
                user__is_active=True
            ).distinct()
            print(f"proposers: {proposers}")
            recipients = list(proposers) + list(global_admins)
            for user in recipients:
                context = common_context.copy()
                context["recipient_name"] = user.user.get_full_name()
                context["accepted_by"] = "Supplier"  # Indicates that the supplier accepted the alternative date
                body = render_to_string("emails/accepted_suggestion.html", context)
                send_async_email.delay(user.user.email, subject, body)

        # Extra vendor notification logic
        try:
            # Retrieve the JobadDepartment entry for this job ad.
            jobad_dept = JobadDepartment.objects.get(jobad=jobad)
            # Use the 'structure' field inherited from StructureDependency.
            dept_structure = jobad_dept.structure
        except JobadDepartment.DoesNotExist:
            dept_structure = None

        if dept_structure:
            # Find all CompanyPartner entries where the structure matches the job ad's department structure.
            company_partners = CompanyPartner.objects.filter(structure=dept_structure, company__company_type='vendor')
            # Get distinct vendor company IDs from these entries.
            vendor_ids = company_partners.values_list('company', flat=True).distinct()
            print(f"company_partners: {company_partners}")
            print(f"vendor_ids: {vendor_ids}")
            # Query vendor companies (ensure they have company_type = 'vendor').
            vendor_companies = Company.objects.filter(id__in=vendor_ids)
            print(f"vendor_companies: {vendor_companies}")
            # Get all active users from these vendor companies.
            vendor_users = UserProfile.objects.filter(
                company__in=vendor_companies,
                user__is_active=True
            ).distinct()
            print(f"vendor_users: {vendor_users}")
            recipients = list(vendor_users) + list(global_admins)
            for user in recipients:
                vendor_context = common_context.copy()
                vendor_context["recipient_name"] = user.user.get_full_name()
                # You can customize this text based on your business logic.
                vendor_context["accepted_by"] = "Customer" if accepting_party == "supplier" else "Supplier"
                body = render_to_string("emails/accepted_suggestion.html", vendor_context)
                send_async_email.delay(user.user.email, subject, body)

        # Return success response
        return JsonResponse({'success': True})
    
    except JobadApplication.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'JobadApplication not found'}, status=404)
    except Interview.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Interview not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
        
@csrf_exempt
def cancel_jobad_application(request, pk):
    if request.method == 'POST':
        try:
            application = JobadApplication.objects.get(pk=pk)
            application.stage = 'canceled'
            application.save()
            return JsonResponse({'success': True})
        except JobadApplication.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'JobadApplication not found'}, status=404)
    return JsonResponse({'success': False, 'error': 'Invalid request method'})        

def rejection_reasons(request, company_id):
    settings_id = request.GET.get('settings_id')
    reasons = RejectionEndReason.objects.filter(company_id=company_id)

    if settings_id:
        reasons = reasons.filter(setting_id=settings_id)

    return JsonResponse({'reasons': list(reasons.values('id', 'text'))})

@csrf_exempt
def reject_profile(request, application_id):
    if request.method == 'POST':
        data = json.loads(request.body)
        rejection_reason_id = data.get('rejection_reason_id')
        
        try:
            # Update the application stage to rejected
            application = JobadApplication.objects.get(id=application_id)
            application.stage = 'rejected'
            application.save()

            # Get the candidate (applicant) associated with this application and his company
            applicant = application.candidate
            company = applicant.company

            # Create RejectedCandidateReason entry
            rejection_reason = RejectionEndReason.objects.get(id=rejection_reason_id)
            RejectedCandidateReason.objects.create(applicant=applicant, reason=rejection_reason)
            
            # Prepare the base URL and job ad URL for email links
            jobad = application.jobad
            base_url = "https://ppms.proserv-dl.de"
            jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=history"

            # Build a common email context
            common_context = {
                "candidate_name": f"{applicant.firstname} {applicant.lastname}",
                "rejection_reason": rejection_reason.text,
                "jobad_title": jobad.position.title,
                "jobad_url": jobad_url,
            }
            subject = f"Absage – Kandidat: {applicant.firstname} {applicant.lastname}"
            global_admins = UserProfile.objects.filter(
                company__name__iexact="ppms",
                role=1,
                user__is_active=True
            )
            # Notify all supplier users
            supplier_users = UserProfile.objects.filter(
                company=company,
                user__is_active=True
            ).distinct()
            print(f"supplier_users: {supplier_users}")
            recipients = list(supplier_users) + list(global_admins)
            for user in recipients:
                context = common_context.copy()
                context["recipient_name"] = user.user.get_full_name()
                body = render_to_string("emails/reject_profile.html", context)
                send_async_email.delay(user.user.email, subject, body)

            # Extra vendor notification logic
            try:
                # Retrieve the JobadDepartment entry for this job ad.
                jobad_dept = JobadDepartment.objects.get(jobad=jobad)
                # Use the inherited field 'structure'
                dept_structure = jobad_dept.structure
            except JobadDepartment.DoesNotExist:
                dept_structure = None

            if dept_structure:
                # Filter CompanyPartner entries that match the department structure
                # and that are linked to vendor companies.
                company_partners = CompanyPartner.objects.filter(
                    structure=dept_structure,
                    company__company_type='vendor'
                )
                vendor_ids = company_partners.values_list('company', flat=True).distinct()
                vendor_companies = Company.objects.filter(id__in=vendor_ids)
                vendor_users = UserProfile.objects.filter(
                    company__in=vendor_companies,
                    user__is_active=True
                ).distinct()
                print(f"vendor_ids: {vendor_ids}")
                print(f"vendor_companies: {vendor_companies}")
                print(f"vendor_users: {vendor_users}")
                recipients = list(vendor_users) + list(global_admins)
                for user in recipients:
                    vendor_context = common_context.copy()
                    vendor_context["recipient_name"] = user.user.get_full_name()
                    body = render_to_string("emails/reject_profile.html", vendor_context)
                    send_async_email.delay(user.user.email, subject, body)

            return JsonResponse({'success': True})

        except (JobadApplication.DoesNotExist, RejectionEndReason.DoesNotExist):
            return JsonResponse({'success': False, 'error': 'Invalid application or rejection reason ID'})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})

@login_required
def candidate_document_download(request, doc_id):
    document = get_object_or_404(CandidateDocument, id=doc_id)

    container_name = os.getenv("STORAGE_CONTAINER_NAME")
    raw_blob_name = document.document.name  # this may contain `#`, spaces, etc.
    blob_name = quote(raw_blob_name, safe='')  # encode everything

    print("Raw blob name:", raw_blob_name)
    print("Encoded blob name:", blob_name)

    if os.getenv("ENV") == "local":
        credential = AzureCliCredential()
    else:
        credential = DefaultAzureCredential()

    try:
        blob_service_client = BlobServiceClient(
            account_url=f"https://{settings.AZURE_ACCOUNT_NAME}.blob.core.windows.net",
            credential=credential
        )
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)
        # blob_client = blob_service_client.get_blob_client(container=container_name, blob=raw_blob_name)
        download_stream = blob_client.download_blob()

        filename = os.path.basename(raw_blob_name)

        response = HttpResponse(download_stream.readall(), content_type="application/octet-stream")
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    except Exception as e:
        print(f"Download error: {str(e)}")
        return HttpResponse("Failed to download document", status=500)
    
DOCUMENT_MODELS = {
    "candidate": CandidateDocument,
    "company": CompanyDocument,
}

def download_document(request, doc_type, pk):
    # 1. Resolve model
    model = DOCUMENT_MODELS.get(doc_type)
    if not model:
        raise Http404("Invalid document type")

    # 2. Fetch document
    try:
        doc = model.objects.get(pk=pk)
    except model.DoesNotExist:
        raise Http404("Document not found")

    # 3. Prepare blob parameters
    raw_blob_name = doc.document.name
    blob_name = f"{settings.AZURE_LOCATION}/{raw_blob_name}"
    container_name = settings.AZURE_CONTAINER
    account_name = settings.AZURE_ACCOUNT_NAME
    account_url = f"https://{account_name}.blob.core.windows.net"
    print("raw_blob_name:", raw_blob_name)
    print("Blob name:", blob_name)
    print("Container:", container_name)
    print("Account URL:", account_url)
    # 4. Authenticate and download with Azure SDK
    try:
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(account_url=account_url, credential=credential)
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        stream = blob_client.download_blob().chunks()

    except Exception as e:
        print("Blob download error:", e)
        raise Http404("Failed to access blob")

    filename = raw_blob_name.split("/")[-1]

    # 5. Stream to browser
    response = StreamingHttpResponse(
        stream,
        content_type="application/octet-stream"
    )
    response["Content-Disposition"] = f'attachment; filename="{filename}"'
    return response

def candidate_document_delete(request, doc_id):
    document = get_object_or_404(CandidateDocument, id=doc_id)
    print(f"document {document}")
    # if request.user.userprofile.role not in [1, 4, 3]:
    #     return HttpResponseForbidden("You don't have permission to delete this document.")
    
    candidate_id = document.candidate.pk
    document.delete()
    return redirect(reverse('candidate-detail', kwargs={'pk': candidate_id}) + '?tab=documents')

def jobad_document_download(request, doc_id):
    document = get_object_or_404(JobaDocument, id=doc_id)
    return FileResponse(document.document.open('rb'),
                        as_attachment=True,
                        filename=document.document.name)

def terminate_deployment(request, worker_id):
    # Only allow POST requests.
    if request.method != 'POST':
        return HttpResponseNotAllowed(['POST'])
    
    # Get the worker or return 404.
    worker = get_object_or_404(Worker, id=worker_id)
    
    # Load the JSON payload.
    try:
        data = json.loads(request.body.decode('utf-8'))
    except json.JSONDecodeError:
        return HttpResponseBadRequest("Invalid JSON payload.")
    
    # Read the 'end_date' from the payload (expected format: YYYY-MM-DD).
    new_end_date_str = data.get('end_date')
    if new_end_date_str:
        new_end_date = parse_date(new_end_date_str)
        if new_end_date is None:
            return HttpResponseBadRequest("Invalid end_date format. Expected YYYY-MM-DD.")
    else:
        # Default to yesterday if no end_date is provided.
        new_end_date = timezone.now().date() - timedelta(days=1)
    
    # Update the worker: set stage to DISCONTINUED and update end_date.
    worker.stage = WorkerStage.DEPLOYMENT_DISCONTINUED
    # Combine the date with midnight and make it timezone aware.
    worker.end_date = timezone.make_aware(datetime.combine(new_end_date, datetime.min.time()))
    worker.modified_date = timezone.now()
    worker.save(update_fields=['stage', 'end_date', 'modified_date'])
    
    # If a rejection_reason_id is provided, create a WorkerRejectionReason record.
    rejection_reason_id = data.get('rejection_reason_id')
    if rejection_reason_id:
        WorkerRejectionReason.objects.create(
            worker=worker,
            reason_id=rejection_reason_id  # This assigns the foreign key by id.
        )
    
    # Optionally: send a confirmation email here.
    return JsonResponse({'success': True})