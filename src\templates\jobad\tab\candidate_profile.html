{% load static %}
{% load i18n %}
{% load application_filters %}
<style>
    [x-cloak] { display: none !important; }
    .profile-image-container {
        position: relative;
        width: 130px;
        height: 80px;
        margin-bottom: 1rem;
        overflow: hidden;
    }
    .profile-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        cursor: pointer;
        max-width: 130px;
        max-height: 80px;
    }
    .profile-image-container input {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
</style>
<div class="container mx-auto mt-4 px-4" x-data="{ jobad: { stage: '{{ jobad.stage|escapejs }}' }, openEditModal: false, openImportModal: false, openUploadModal: false, search: '', selectedCountry: '', europeanCountries: Alpine.store('europeanCountries') }" x-init="openImportModal = false; openUploadModal = false; openEditModal = false;">
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.store('europeanCountries', {{ EUROPEAN_COUNTRIES|safe }});
        });
    </script>
    <!-- Display the total count of candidates applied -->
    <div class="flex justify-between items-center mb-4">
        <div class="flex justify-center items-center">
            <h2 class="text-lg font-semibold text-gray-700">
                {% if filtered_count == 1 %} 
                    1 {% trans "Candidate Applied" %}
                {% else %} 
                    {{ filtered_count}} {% trans "Candidates Applied" %}
                {% endif %}
            </h2>
            <div class="ml-4">
                <input type="text" x-model="search" placeholder="{% trans 'Search...' %}"
                       class="w-64 border border-gray-300 rounded-lg p-2" />
            </div>
        </div>
        <div>
            <button 
                @click="openImportModal = true" 
                :disabled="jobad.stage === 'filled' || '{{ user.userprofile.company.company_type }}' === 'customer'" 
                :class="{'opacity-50 cursor-not-allowed': jobad.stage === 'filled' || '{{ user.userprofile.company.company_type }}' === 'customer'}" 
                class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-2">
                <i class="fas fa-plus"></i> {% trans "Import" %}
            </button>
            <button 
                @click="openUploadModal = true" 
                :disabled="jobad.stage === 'filled' || '{{ user.userprofile.company.company_type }}' === 'customer' || jobad.stage === 'requested'"
                :class="{'opacity-50 cursor-not-allowed': jobad.stage === 'filled' || '{{ user.userprofile.company.company_type }}' === 'customer'}" 
                class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-upload"></i> {% trans "Upload" %}
            </button>
        </div>
    </div>     
    
    <!-- Candidate Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-x-auto mb-8">
        <table class="min-w-full text-left bg-gray-100">
            <thead>
                <tr class="bg-gray-200">
                    <th class="py-3 px-4 border-b-2 text-sm text-gray-700"></th> <!-- Icons Row -->
                    <th class="py-3 px-4 border-b text-sm font-semibold text-gray-700">
                        <div x-data="{
                                // Use the current GET parameter if it's 'full_name' or '-full_name',
                                // otherwise default to 'full_name'
                                sort: (['full_name', '-full_name'].includes('{{ request.GET.sort|default:"" }}') 
                                       ? '{{ request.GET.sort }}' 
                                       : 'full_name')
                            }">
                          <a href="#" 
                             x-on:click.prevent="
                               // Toggle the sort value
                               sort = (sort === 'full_name' ? '-full_name' : 'full_name');
                               // Update the browser's URL so that the page reloads with the new sort
                               window.location.href = '?tab=candidate-profile&sort=' + sort;
                             "
                             class="inline-flex items-center">
                             {% trans "Full Name" %}
                             <!-- Show arrow icons based on the current state -->
                             <template x-if="sort === 'full_name'">
                               <i class="fas fa-arrow-up ml-1"></i>
                             </template>
                             <template x-if="sort === '-full_name'">
                               <i class="fas fa-arrow-down ml-1"></i>
                             </template>
                          </a>
                    </div>
                    </th>                                                      
                    <th class="py-3 px-4 border-b-2 text-sm font-semibold text-gray-700">{% trans "VS" %}</th>
                    <th class="py-3 px-4 border-b-2 text-sm font-semibold text-gray-700">{% trans "Company" %}</th>
                    <th class="py-3 px-4 border-b-2 text-sm font-semibold text-gray-700">{% trans "Uploaded" %}</th>
                    <th class="py-3 px-4 border-b-2 text-sm font-semibold text-gray-700">{% trans "Status" %}</th>
                    <th class="py-3 px-4 border-b-2 text-sm font-semibold text-gray-700"></th> <!-- Edit Button -->
                </tr>
            </thead>
            <tbody>
                {% for application in jobad_applications %}
                    {% if application.stage not in "canceled rejected selection_rejected completed" %}
                        <tr class="bg-white hover:bg-gray-50" x-show="search === '' || ('{{ application.candidate.firstname }} {{ application.candidate.lastname }}'.toLowerCase()).includes(search.toLowerCase())">
                            <td class="py-5 pr-4 pl-6 border-b">
                                {% if application.needs_attention %}
                                    <i class="fas fa-exclamation-circle text-red-500"></i>
                                {% endif %}
                                <a href="{% url 'candidate-profile' application.candidate.id %}" class="text-accent-500">
                                    <i class="fas fa-user"></i>
                                </a>
                                {% with cv_docs=application.candidate.candidatedocument_set|filter_by:"document_type=CV" %}
                                    {% if cv_docs %}
                                        <a href="{% url 'document_download' 'candidate' cv_docs.0.id %}" class="text-blue-500 ml-2" title="Download CV">
                                            <i class="fas fa-file-download"></i>
                                        </a>
                                    {% endif %}
                                {% endwith %}                                                                 
                            </td>
                            <td class="py-3 px-4 border-b text-gray-900">{{ application.candidate.firstname|title }} {{ application.candidate.lastname|title }}</td>
                            <td class="py-3 px-4 border-b text-gray-900">{{ application.candidate.hourly_rate|default_if_none:"-" }} €</td>
                            <td class="py-3 px-4 border-b text-gray-900">{{ application.candidate.company.name }}</td>
                            <td class="py-3 px-4 border-b text-gray-900">{{ application.created_date|date:"d.m.Y" }}</td>
                            <td class="py-3 px-4 border-b text-gray-900">{{ application.stage|format_stage }}</td>
                            <td class="py-3 px-4 border-b text-center">
                                {% include 'jobad/tab/_edit_modal.html' %}
                            </td>
                              <script>
                                console.log("This application’s hourly rate is:", "{{ application.candidate.hourly_rate }}");
                            </script>
                        </tr>
                    {% endif %}
                {% endfor %}
                {% if not jobad_applications|length %}
                    <tr class="bg-white hover:bg-gray-50">
                        <td colspan="7" class="py-3 px-4 text-center text-gray-500">{% trans "No candidates found for this job ad" %}</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Import Modal -->
    <div x-show="openImportModal" @click.away="openImportModal = false" x-cloak class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-3xl max-h-[80vh]" @click.stop x-data="{ search: '' }">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-700">{% trans "Import Candidates" %}</h3>
                <!-- Search Bar -->
                <input type="text" x-model="search" placeholder="Search candidates..." class="border border-gray-300 rounded-lg py-1 px-3 w-64 focus:outline-none focus:ring focus:border-accent-500">
            </div>
            <form method="post" action="{% url 'import-candidate' jobad.pk %}">
                {% csrf_token %}
                <div class="overflow-y-auto" style="max-height: 60vh;">
                    <table class="min-w-full divide-y divide-gray-200 text-sm">
                        <thead class="bg-accent-500 text-white font-bold">
                            <tr>
                                <th class="py-3 px-4 text-left text-sm font-semibold">{% trans "Candidate" %}</th>
                                <th class="py-3 px-4 text-left text-sm font-semibold">{% trans "Last Name" %}</th>
                                <th class="py-3 px-4 text-left text-sm font-semibold">{% trans "Date of Birth" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for candidate in candidates %}
                            <tr class="hover:bg-gray-50 transition-colors" x-show="(search === '' || '{{ candidate.firstname }}'.toLowerCase().includes(search.toLowerCase()) || '{{ candidate.lastname }}'.toLowerCase().includes(search.toLowerCase()))">
                                <td class="py-3 px-4 border-b border-gray-200">
                                    <input type="checkbox" name="candidates" class="rounded-lg" value="{{ candidate.pk }}"
                                        {% if candidate.pk in imported_candidate_ids %} checked disabled {% endif %}>
                                    <label class="ml-2">{{ candidate.firstname }} {{ candidate.lastname }}</label>
                                </td>
                                <td class="py-3 px-4 border-b border-gray-200">{{ candidate.company.name }}</td>
                                <td class="py-3 px-4 border-b border-gray-200">{{ candidate.date_of_birth }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="flex justify-end mt-4">
                    <button type="submit" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-6 rounded mr-2 transition-all">
                        {% trans "Import" %}
                    </button>
                    <button type="button" @click="openImportModal = false" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition-all">
                        {% trans "Cancel" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- Upload Modal -->
    <div x-show="openUploadModal" @click.away="openUploadModal = false" x-cloak class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50 overflow-hidden">
        <div class="bg-white p-10 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] relative" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold mb-4">{% trans "Create Candidate" %}</h3>
                <button @click="openUploadModal = false" class="hover:bg-gray-200 p-1 rounded-full text-gray-500 hover:text-gray-700 focus:outline-none">
                    <i class="fas fa-times text-1xl"></i>
                </button>
            </div>
            <form id="upload-candidate-form" method="post" action="{% url 'upload-candidate' jobad.pk %}" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="candidate_form" value="1">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Profile Image Field -->
                    <div class="input-container">
                        <div class="profile-image-container">
                            <img id="profile-image-preview" src="{% static 'images/avatar3.jpg' %}" alt="Profile Image" onclick="document.getElementById('profile-picture-input').click();">
                            <input type="file" name="profile_picture" accept="image/*" id="profile-picture-input" class="hidden" onchange="previewImage(event)">
                        </div>
                    </div>
                    <!-- Custom Searchable Dropdown for Company -->
                    <div class="input-container" x-data='searchableDropdown({{ companies_json|safe }}, {{ user.userprofile.company.id }})' @click.away="open = false">
                        <label for="company_search" class="block text-sm font-semibold text-gray-700">{% trans "Company" %}</label>
                    
                        <!-- A container for our "display" + "chevron" icon (optional) -->
                        <div class="relative">
                        <!-- This input is both the display of the selected company
                            AND the place the user types to filter. -->
                        <input
                            type="text"
                            id="company_search"
                            x-model="query"
                            placeholder="{% trans 'Search companies...' %}"
                            class="border border-gray-300 rounded p-2 w-full"
                            @focus="open = true"
                        />
                    
                        <!-- (Optional) A chevron icon to toggle the dropdown on click -->
                        <button
                            type="button"
                            class="absolute top-0 right-0 mt-2 mr-2 text-gray-500"
                            @click="toggle()"
                        >
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    
                        <!-- The dropdown list -->
                        <template x-if="open">
                            <ul class="absolute z-10 bg-white border border-gray-300 w-full mt-1 max-h-60 overflow-auto">
                            <template x-for="company in filteredCompanies" :key="company.pk">
                                <li
                                @click="select(company)"
                                class="p-2 hover:bg-gray-100 cursor-pointer"
                                x-text="company.name"
                                ></li>
                            </template>
                            </ul>
                        </template>
                        </div>
                    
                        <!-- Hidden input to store selected company value -->
                        <input type="hidden" name="company" :value="selected ? selected.pk : ''">
                    </div>
                    <!-- Render other fields (except profile_picture and company) -->
                    {% for field in form %}
                        {% if field.name != "profile_picture" and field.name != "company" %}
                            <div class="input-container">
                                <label for="{{ field.id_for_label }}" class="block text-sm font-semibold text-gray-700">{{ field.label }}</label>
                                <div>
                                    {{ field }}
                                </div>
                                {% for error in field.errors %}
                                    <p class="text-red-500 text-xs mt-1">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    {% endfor %}
                    <!-- CV Documents Field -->
                    <div class="input-container mb-4">
                        <label for="documents" class="block text-sm font-semibold text-gray-700 mb-1">{% trans "CV" %}</label>
                        <input type="file" name="documents" id="documents" multiple>
                    </div>
                    <!-- G25 Document Field -->
                    <div class="input-container mb-4" id="g25-document-container" style="display: none;">
                        <label for="g25" class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Forklift License" %}</label>
                        <input type="file" name="g25" id="g25">
                    </div>
                    <!-- Fuhrerschein Document Field -->
                    <div class="input-container mb-4" id="fuhrerschein-document-container" style="display: none;">
                        <label for="fuhrerschein" class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Car License" %}</label>
                        <input type="file" name="fuhrerschein" id="fuhrerschein">
                    </div>
                    <!-- Work Permit Document Field -->
                    <div class="input-container mb-4" x-show="selectedCountry && !europeanCountries.includes(selectedCountry)">
                        <label for="workpermit" class="block text-sm font-semibold text-gray-700 mb-1">{% trans "Work Permit" %}</label>
                        <input type="file" name="workpermit" id="workpermit">
                    </div>
                </div>
                <input type="hidden" id="g25-required" value="{{ jobad.g25_selected|yesno:'true,false' }}">
                <input type="hidden" id="fuhrerschein-required" value="{{ jobad.fuhrerschein_selected|yesno:'true,false' }}">
                <div class="flex justify-end">
                    <button type="button" id="upload-candidate-button" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-2">{% trans "Create" %}</button>
                    <button type="button" @click="openUploadModal = false" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">{% trans "Cancel" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function toggleAll() {
        let checkboxes = document.querySelectorAll('input[name="candidates"]');
        let allChecked = true;
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                allChecked = false;
            }
        });
        checkboxes.forEach(checkbox => checkbox.checked = !allChecked);
    }

    document.getElementById('upload-candidate-button').addEventListener('click', function () {
        const form = document.getElementById('upload-candidate-form');
        const g25Required = document.getElementById('g25-required').value === 'true';
        const fuhrerscheinRequired = document.getElementById('fuhrerschein-required').value === 'true';

        let errors = [];

        // Check if G25 document is required
        if (g25Required) {
            const g25Input = document.querySelector('input[name="g25"]');
            if (g25Required && (!g25Input || !g25Input.files.length)) {
                errors.push("G25 document is required for this job ad.");
            }
        }

        // Check if Fuhrerschein document is required
        if (fuhrerscheinRequired) {
            const fuhrerscheinInput = document.querySelector('input[name="fuhrerschein"]');
            if (fuhrerscheinRequired && (!fuhrerscheinInput || !fuhrerscheinInput.files.length)) {
                errors.push("Car License document is required for this job ad.");
            }
        }

        if (errors.length > 0) {
            alert(errors.join("\n"));
            return; // Stop the submission if there are errors
        }

        const formData = new FormData(form);
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Refresh the page or update the list to show the newly added candidate
                location.reload();
            } else {
                // Handle errors and display them below the respective fields
                if (data.errors) {
                    for (let field in data.errors) {
                        const fieldElement = document.querySelector(`[name="${field}"]`);
                        if (fieldElement) {
                            const errorMessage = document.createElement('p');
                            errorMessage.className = 'text-red-500 text-xs mt-1 error-message';
                            errorMessage.textContent = data.errors[field].join(', ');
    
                            fieldElement.parentElement.appendChild(errorMessage);
                        }
                    }
                }
            }
        })
        .catch(error => console.error('Error:', error));
    });
    document.addEventListener('DOMContentLoaded', function() {
        const g25Required = document.getElementById('g25-required').value === 'true';
        const fuhrerscheinRequired = document.getElementById('fuhrerschein-required').value === 'true';
        const countryField = document.querySelector('select[name="country"]');

        // render file inputs 
        if (g25Required) {
            document.getElementById('g25-document-container').style.display = 'block';
        }
        if (fuhrerscheinRequired) {
            document.getElementById('fuhrerschein-document-container').style.display = 'block';
        }    
        if (countryField) {
            countryField.setAttribute('x-model', 'selectedCountry');
        }
    });

    function previewImage(event) {
        const input = event.target;
        const reader = new FileReader();
        reader.onload = function() {
            const dataURL = reader.result;
            const output = document.getElementById('profile-image-preview');
            output.src = dataURL;
        };
        reader.readAsDataURL(input.files[0]);
    }
    function searchableDropdown(companies, preselectedId = null) {
        return {
            companies: companies,
            query: '',
            selected: null,
            open: false,
            init() {
                if (preselectedId) {
                    const preselected = this.companies.find(c => c.pk === preselectedId);
                    if (preselected) {
                        this.selected = preselected;
                        this.query = preselected.name;
                    }
                }
            },
            get filteredCompanies() {
                if (this.query.trim() === '') {
                    return this.companies;
                }
                return this.companies.filter(company =>
                    company.name.toLowerCase().includes(this.query.toLowerCase())
                );
            },
            select(company) {
                this.selected = company;
                this.query = company.name;
                this.open = false;
            },
            toggle() {
                this.open = !this.open;
            }
        }
    }
</script>
<style>

    .table-auto th, .table-auto td {
        padding: 0.75rem 1rem;
    }

    .table-auto thead th {
        background-color: #f3f4f6;
        font-size: 0.875rem;
        text-transform: uppercase;
    }

    .table-auto tbody tr:hover {
        background-color: #f1f5f9;
    }

    .table-auto tbody td {
        border-bottom: 1px solid #e5e7eb;
    }

    button {
        transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    button:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .profile-image-container {
        position: relative;
        width: 150px;
        height: 120px;
        margin-bottom: 1rem;
        overflow: hidden;
    }
    .profile-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        cursor: pointer;
        max-width: 150px;
        max-height: 150px;
    }
    .profile-image-container input {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
</style>