from django.contrib.auth.models import User
from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
from phonenumber_field.modelfields import PhoneNumberField

from common.models import Country, BusinessData, Settings, AllowedSettingValues, Operations, Document
from common.validators import validate_phone_number
from django.utils import timezone


class Company(BusinessData):
    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name'], name='companies_name_key'),
            models.UniqueConstraint(fields=['id', 'company_type'], name='companies_id_type_key'),
        ]


class Supplier(BusinessData):
    pass


def get_default_date():
    return timezone.now().date()


class CompanyPosition(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    title = models.CharField(max_length=100)

    def __str__(self):
        return self.title


class CompanyLocation(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    street = models.CharField(max_length=50)
    street_number = models.CharField(max_length=50)
    post_code = models.CharField(max_length=5)
    city = models.CharField(max_length=50)
    company_telephone = PhoneNumberField(null=True, blank=True)

    def __str__(self):
        return f'{self.street} {self.street_number}, {self.post_code}, {self.city}'

    document = models.FileField(upload_to='company', null=True, blank=True)


class CompanyStructure(models.Model):
    id = models.CharField(max_length=255, primary_key=True)  # Store the hierarchical ID
    parent = models.CharField(max_length=255, null=True,
                              blank=True)  # String-based reference for parent (can be company ID or structure ID)
    value = models.CharField(max_length=255)  # Node name
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    dependencies = models.IntegerField(default=0)

    def __str__(self):
        return f'{self.id}'


class CompanyDocument(Document):
    company = models.ForeignKey('Company', on_delete=models.CASCADE)
    document = models.FileField(upload_to='company/', max_length=455)
    structure = models.ForeignKey(
        CompanyStructure,
        null=True, blank=True,
        on_delete=models.PROTECT,
        help_text="Which LGI division this doc is for"
    )
    
class StructureDependency(models.Model):
    structure = models.CharField(max_length=50)

    class Meta:
        abstract = True


class CompanySupplier(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='suppliers')
    supplier = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='companies_as_supplier')
    master_vendor = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='master_vendor_suppliers',
                                      null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['company', 'supplier'], name='company_supplier_unique')
        ]

    def __str__(self):
        return f'{self.company} - {self.supplier} (Master Vendor: {self.master_vendor})'


class CompanyPartner(StructureDependency):
    customer = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='customer_companypartner')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='company_companypartner')
    group_id = models.TextField()
    is_vendor = models.BooleanField(default=False)
    no_vendor = models.BooleanField(default=False)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['customer', 'company', 'structure'],
                                    name='company_partners_customer_company_structure_key'),
        ]


class CompanySettings(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    setting = models.ForeignKey(Settings, on_delete=models.CASCADE)
    global_setting = models.BooleanField(default=False)
    allowed_setting_value = models.ForeignKey(AllowedSettingValues, on_delete=models.SET_NULL, null=True, blank=True)
    value = models.CharField(max_length=255, null=True, blank=True)
    schema = models.JSONField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['company', 'setting'], name='company_settings_company_setting_key'),
        ]


class WorkingHours(models.Model):
    start_time = models.CharField(max_length=50, blank=True, null=True)
    end_time = models.CharField(max_length=50, blank=True, null=True)
    type = models.CharField(max_length=50, blank=True, null=True)
    location = models.IntegerField()
    company = models.ForeignKey(Company, on_delete=models.CASCADE)


class RejectionEndReason(models.Model):
    text = models.CharField(max_length=30)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    setting = models.ForeignKey(Settings, on_delete=models.CASCADE)
    edited = models.BooleanField(default=False)
    previous_id = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return self.text


class CompanyRoles(models.Model):
    name = models.CharField(max_length=50)
    type = models.CharField(
        choices=[('vendor', 'vendor'), ('customer', 'customer'), ('supplier', 'supplier'), ('global', 'global')],
        max_length=8)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    access_rights = ArrayField(models.CharField(max_length=50, choices=Operations.choices), default=list)
    allowed_routes = models.JSONField(null=True, blank=True)
    denied_routes = models.JSONField(null=True, blank=True)
    modified_by = models.CharField(max_length=50, blank=True, null=True)
    modified_date = models.DateTimeField(default=timezone.now)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['name', 'company', 'type'], name='roles_name_company_type_key'),
        ]

    def __str__(self):
        return self.name


class CompanyTermsAndConditions(models.Model):
    title = models.CharField(max_length=150)
    text = models.TextField()
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True,
                                related_name='company_toc_company')
    child = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True,
                              related_name='company_toc_child')
    roles = models.ManyToManyField(CompanyRoles)
    version = models.IntegerField(default=1)
    created_by = models.ForeignKey('userprofiles.UserProfile', related_name='terms_created_by',
                                   on_delete=models.SET_NULL, null=True,
                                   blank=True)
    modified_by = models.ForeignKey('userprofiles.UserProfile', related_name='terms_modified_by',
                                    on_delete=models.SET_NULL, null=True,
                                    blank=True)
    deleted_by = models.ForeignKey('userprofiles.UserProfile', related_name='terms_deleted_by',
                                   on_delete=models.SET_NULL, null=True,
                                   blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title
