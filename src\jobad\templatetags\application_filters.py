from django import template
from django.utils.translation import gettext_lazy as _
from jobad.utils.stage import format_stage
import os

register = template.Library()

# @register.filter
# def format_stage(value):
#     stage_mapping = {
#         "requested": _("Requested"),
#         "approved": _("Approved"),
#         "rejected": _("Rejected"),
#         "interview": _("Interview"),
#         "interview_completed": _("Interview Completed"),
#         "interview_rejected": _("Interview Rejected"),
#         "candidate_selection": _("Candidate Selection"),
#         "tentatively_occupied": _("Tentatively Occupied"),
#         "candidate_selection_rejected": _("Candidate Selection Rejected"),
#         "completed": _("Completed"),
#         "canceled": _("Canceled"),
#         "selection_rejected": _("Selection Rejected"),
#         "open_for_everyone": _("Offen"),
#         "filled": _("Besetzt"),
#         "withdrawn": _("Withdrawn"),
#         "open_again": _("Offen"),
#         "retry": _("Retry"),
#     }
#     return stage_mapping.get(value, value)  

register.filter('format_stage', format_stage)

@register.filter
def filter_by(queryset, arg):
    """
    Usage: queryset|filter_by:"field=value"
    Example: application.candidate.candidatedocument_set|filter_by:"document_type=CV"
    """
    try:
        field, value = arg.split("=")
        return queryset.filter(**{field: value})
    except Exception:
        return queryset

@register.filter
def after_hash(filename):
    """Return everything after the first '#' in the filename, or the full filename if no '#'."""
    if not filename:
        return ""
    parts = filename.split('#', 1)
    if len(parts) == 2:
        return parts[1]
    return filename

@register.filter
def after_jobad_hash(value, max_length=None):
    """
    Returns the portion of the filename after the '#' character.
    If no '#' is found, returns the basename without its extension.
    If max_length is provided and the resulting string is longer than max_length,
    returns '...' followed by the last max_length characters.
    """
    if not isinstance(value, str):
        value = str(value)
    base = os.path.basename(value)
    # Use the part after '#' if present; otherwise, remove extension.
    if '#' in base:
        result = base.split('#', 1)[1]
    else:
        result, _ = os.path.splitext(base)
    # Remove extension from result, if still present.
    result, _ = os.path.splitext(result)
    if max_length:
        try:
            max_length = int(max_length)
            if len(result) > max_length:
                result = '...' + result[-max_length:]
        except (ValueError, TypeError):
            pass
    return result

@register.filter
def subtract(value, arg):
    """
    Subtracts the arg from the value.
    Example: {{ jobad.total|subtract:jobad.occupied }}
    """
    try:
        return int(value) - int(arg)
    except (ValueError, TypeError):
        return value
    
@register.filter
def mul(value, arg):
    return int(value) * int(arg)