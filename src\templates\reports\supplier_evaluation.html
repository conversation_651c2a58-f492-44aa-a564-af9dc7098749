{% extends "_base_frontend.html" %}

{% block title %}Supplier Evaluation{% endblock %}

{% block custom_header %}
  <style>
    .nav-item a {
      position: relative;
    }
    .nav-item a::after {
      content: '';
      position: absolute;
      left: 0; bottom: -5px;
      width: 0;
      height: 2px;
      background-color: #ffd200;
      transition: width 0.3s ease-in-out;
    }
    .nav-item a:hover::after {
      width: 100%;
    }
    .chart-container {
      background: white;
      padding: 1rem;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
  </style>
{% endblock %}

{% block content %}
  <div class="flex items-center mb-4">
    <li class="nav-item inline-block mb-4">
      <a href="{% url 'reports-reaction-time' %}" class="inline-flex items-center text-gray-700 hover:text-gray-900">
          <i class="fa-solid fa-arrow-left mr-2"></i> Reaktionszeiten
      </a>
    </li>
  </div>

  {# — Supplier picker — #}
  <form method="get" class="mb-6">
    {% comment %} <label for="supplier" class="font-medium mr-2">Supplier:</label> {% endcomment %}
    <div class="flex items-center mb-4">
      <select name="supplier" id="supplier" style="width:250px">
        <option value="" {% if not selected_supplier %}selected{% endif %}>All</option>
        {% for sup in suppliers %}
          <option value="{{ sup.id }}"
            {% if sup.id|stringformat:"s" == selected_supplier %}selected{% endif %}>
            {{ sup.name }}
          </option>
        {% endfor %}
      </select>
        {% if selected_supplier_obj %}
          <div class="flex flex-wrap ml-4">
            <div class="inline-flex items-center bg-gray-200 rounded-full px-3 py-1 text-sm bg-white shadow">
              {{ selected_supplier_obj.name }}
              <a href="{% url 'reports-supplier-evaluation' %}"
                class="ml-2 text-gray-600 hover:text-red-500">
                <i class="fa fa-times"></i>
              </a>
            </div>
          </div>
        {% endif %}
    </div>
  </form>

  {# — Summary cards — #}
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8">
    <div class="p-4 bg-white rounded shadow text-center">
      <p class="text-sm text-gray-500">Total Candidates</p>
      <p class="text-xl font-semibold">{{ total_cands }}</p>
    </div>
    <div class="p-4 bg-white rounded shadow text-center">
      <p class="text-sm text-gray-500">Total Workers</p>
      <p class="text-xl font-semibold">{{ total_workers }}</p>
    </div>
    <div class="p-4 bg-white rounded shadow text-center">
      <p class="text-sm text-gray-500">Active Workers</p>
      <p class="text-xl font-semibold">{{ active_workers }}</p>
    </div>
    <div class="p-4 bg-white rounded shadow text-center">
      <p class="text-sm text-gray-500">Terminated</p>
      <p class="text-xl font-semibold">{{ terminated_workers }}</p>
    </div>
    <div class="p-4 bg-white rounded shadow text-center">
      <p class="text-sm text-gray-500">Rejected</p>
      <p class="text-xl font-semibold">{{ total_declined }}</p>
    </div>
  </div>

  {# — Two-column charts: reasons & nationalities — #}
  <div class="mb-12 grid grid-cols-2 lg:grid-cols-2 gap-6">
    {% if has_candidate_rejections or has_worker_rejections %}
      <div class="gap-6 mt-8">
        {% if has_candidate_rejections %}
          <div class="chart-container">
            <h2 class="font-semibold mb-2">Candidate Rejection Reasons</h2>
            <canvas id="candReasonsChart"></canvas>
          </div>
        {% endif %}
        {% if has_worker_rejections %}
          <div class="chart-container">
            <h2 class="font-semibold mb-2">Worker Rejection Reasons</h2>
            <canvas id="workerReasonsChart"></canvas>
          </div>
        {% endif %}
      </div>
    {% else %}
      <p class="text-center text-gray-500 italic">No rejection reasons</p>
    {% endif %}

    <div class="gap-6 mt-8">
      <div class="chart-container">
        <h2 class="font-semibold mb-2">Candidate Nationality</h2>
        <canvas id="natChart"></canvas>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // ── Rejection Reasons (bar) ─────────────────────────────────────────
    {% if has_candidate_rejections %}
    new Chart(document.getElementById('candReasonsChart'), {
      type: 'pie',
      data: {
        labels: {{ cand_reason_labels_json|safe }},
        datasets: [{
          data: {{ cand_reason_values_json|safe }},
          backgroundColor: [
            'rgba(239, 68, 68, 0.6)',
            'rgba(245, 158, 11, 0.6)',
            'rgba(37, 99, 235, 0.6)',
            // … add more colors if you want
          ]
        }]
      },
      options: {
        plugins: {
          legend: { position: 'right' }
        }
      }
    });
    {% endif %}

    // ── Worker Rejections Pie ───────────────────────────────────────────
    {% if has_worker_rejections %}
    new Chart(document.getElementById('workerReasonsChart'), {
      type: 'pie',
      data: {
        labels: {{ worker_reason_labels_json|safe }},
        datasets: [{
          data: {{ worker_reason_values_json|safe }},
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            // … etc
          ]
        }]
      },
      options: {
        plugins: {
          legend: { position: 'right' }
        }
      }
    });
    {% endif %}

    // ── Nationalities (pie) ─────────────────────────────────────────────
    new Chart(
      document.getElementById('natChart'),
      {
        type: 'pie',
        data: {
          labels: {{ nat_labels_json|safe }},
          datasets: [{
            data: {{ nat_values_json|safe }}
          }]
        },
        options: {
          plugins: { legend: { position: 'right' } }
        }
      }
    );
    $(document).ready(function(){
    $('#supplier').select2({
      placeholder: 'Search suppliers…',
      allowClear: true,
      width: 'resolve'
    }).on('change', function(){
      this.form.submit();
    });
  });
  </script>
{% endblock %}
