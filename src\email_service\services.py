from django.conf import settings
import requests
from django.template.loader import render_to_string
import json
from django.utils.translation import gettext as _
from django.urls import reverse
from django.templatetags.static import static

class EmailService:
    @staticmethod
    def send_email(recipient_email, subject, body):
        """Send an email using the provided Mail API."""
        api_url = settings.EMAIL_API_URL
        auth_key = settings.EMAIL_AUTH_KEY
        sender_email = settings.EMAIL_SENDER

        payload = {
            "Body": body,
            "From": sender_email,
            "Receiver": recipient_email,
            "Subject": subject,
            "IsBodyHtml": True,
            "SenderEmail": sender_email,
            "CC": "",
            "UseAlternateView": False,
        }

        headers = {
            "Content-Type": "application/json",
            "X-functions-key": auth_key,
        }

        try:
            response = requests.post(api_url, headers=headers, data=json.dumps(payload))
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Error sending email: {e}")
            return None
        
    # @staticmethod
    # def send_email_by_type(email_type, recipient_email, context):
    #     """Handle email construction by type."""
    #     if email_type not in EMAIL_TEMPLATES:
    #         raise ValueError(f"Unknown email type: {email_type}")

    #     template = EMAIL_TEMPLATES[email_type]['template']
    #     subject = EMAIL_TEMPLATES[email_type]['subject'].format(**context)
    #     body = render_to_string(template, context)

    #     EmailService.send_email(recipient_email, subject, body)
        
    @staticmethod
    def send_import_candidate_email(jobad, candidate, recipient_email, action_by, publisher_name):
        """Send an email for imported candidates."""
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"

        # Render email body using a template
        context = {
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
            "candidate_name": f"{candidate.firstname} {candidate.lastname}",
            "company_name": candidate.company.name,
            "publisher_name": publisher_name,
            "action_by": action_by,
        }
        subject = _("Candidate Imported: {candidate_name}").format(candidate_name=context["candidate_name"])
        body = render_to_string("emails/candidate_imported.html", context)

        # Use send_email
        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_upload_candidate_email(jobad, candidate, recipient_email, action_by, recipient_name):
        """Send an email for uploaded candidates."""

        # Determine base URL based on environment
        base_url = "https://ppms.proserv-dl.de"
        
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=candidate-profile"

        context = {
            "jobad_title": jobad.position.title,
            "jobad_url": jobad_url,
            "candidate_name": f"{candidate.firstname} {candidate.lastname}",
            "company_name": candidate.company.name,
            "recipient_name": recipient_name,
            "action_by": action_by,
        }

        # 🔍 Debugging: Print the jobad URL and context before rendering the email
        print(f"\n==== DEBUG: Job Ad URL in send_upload_candidate_email ====\n{jobad_url}\n")
        print(f"\n==== DEBUG: Email Context ====\n{context}\n")

        subject = _("Neuer Kandidat für Ihre Stellenanzeige {candidate_name}").format(candidate_name=context["candidate_name"])
        body = render_to_string("emails/candidate_uploaded.html", context)

        # Send email
        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_new_jobad_email(jobad, supplier_name, recipient_email, action_by):
        """Send an email to supplier users about a new jobad."""
        
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}"

        context = {
            "jobad_title": jobad.position.title,
            "jobad_location": jobad.location.street + " " + jobad.location.street_number + ", " + jobad.location.city,
            "supplier_name": supplier_name,
            "action_by": action_by,
            "time_period": jobad.time_period,
            "total": jobad.total,
            "department_value": jobad.department_value,
            "jobad_url": jobad_url,
            "jobad_company": jobad.company,
            "logo_url": f"{settings.BASE_URL}{static('images/faviconn.png')}",
            "start_date": jobad.start_date,
            "tasks": jobad.tasks,
            "requirements": jobad.requirements,
            "shifts": jobad.working_hours,
            "weekly_working_hours": jobad.weekly_working_hours,
        }
        subject = _("Neue Stellenanzeige {jobad_title} wurde hochgeladen").format(jobad_title=jobad.position.title)
        body = render_to_string("emails/new_jobad_created.html", context)

        # Use send_email
        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_skip_interview_email(candidate_name, recipient_email, jobad_title, jobad_url, recipient_name):
        """Send an email for skipping the interview."""
        context = {
            "candidate_name": candidate_name,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
            "recipient_name": recipient_name,
        }
        subject = _("Kandidat akzeptiert ohne Interview/Probetag: {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/skip_interview.html", context)

        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_interview_proposal_email(candidate_name, recipient_email, proposed_dates_times, jobad_title, jobad_url, contact_person_name, recipient_name, contact_person_phone):
        """Send an email for interview proposal."""
        context = {
            "candidate_name": candidate_name,
            "jobad_title": jobad_title,
            "recipient_name": recipient_name,
            "candidate_name": candidate_name,
            "jobad_url": jobad_url,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
        }
        subject = _("Einladung Interview/Probetag – Kandidat {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/interview_proposal.html", context)

        # Use send_email to dispatch the email
        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_alternative_suggestion_email(candidate_name, recipient_email, company_name, alternative_date, start_time, end_time, suggested_by, jobad_title, jobad_url, recipient_name):
        """Send an email about an alternative interview suggestion."""
        context = {
            "candidate_name": candidate_name,
            "recipient_name": recipient_name,
            "alternative_date": alternative_date,
            "company_name": company_name,
            "start_time": start_time,
            "end_time": end_time,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
            "suggested_by": "Supplier" if suggested_by == "supplier" else "Customer",
        }
        subject = _("Interview/Probetag muss verschoben werden {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/alternative_suggestion.html", context)

        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_accepted_suggestion_email(candidate_name, recipient_email, recipient_name, interview_date, start_time, end_time, accepted_by, jobad_title, jobad_url, contact_person_name, contact_person_phone):
        """Send an email about an accepted interview suggestion."""
        context = {
            "candidate_name": candidate_name,
            "interview_date": interview_date,
            "recipient_name": recipient_name,
            "start_time": start_time,
            "end_time": end_time,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
            "accepted_by": "Supplier" if accepted_by == "supplier" else "Proposer",
        }
        subject = _("Interview/Probetag akzeptiert – Kandidat {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/accepted_suggestion.html", context)

        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_selection_details_email(candidate_name, recipient_email, start_date, end_date, start_time, contact_person_name, contact_person_phone, recipient_name, jobad_title, jobad_url):
        """Send an email with selection details."""
        context = {
            "candidate_name": candidate_name,
            "start_date": start_date,
            "end_date": end_date,
            "start_time": start_time,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
            "recipient_name": recipient_name,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
        }
        subject = _("Zusage erteilt – Kandidat {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/selection_details.html", context)

        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_selection_confirmation_email(
        recipient_email,
        jobad_title,
        candidate_name,
        start_date,
        end_date,
        start_time,
        recipient_name,
        contact_person_name,
        contact_person_phone,
        company_name,
        jobad_url
    ):
        """Send an email to proposers confirming the selection by the supplier."""
        context = {
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
            "company_name": company_name,
            "candidate_name": candidate_name,
            "recipient_name": recipient_name,
            "start_date": start_date,
            "end_date": end_date,
            "start_time": start_time,
            "contact_person_name": contact_person_name,
            "contact_person_phone": contact_person_phone,
        }
        subject = _("Zusage {candidate_name} Start {start_date}").format(candidate_name=candidate_name, start_date=start_date)
        body = render_to_string("emails/selection_confirmed.html", context)

        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_deployment_confirmation_email(
        recipient_email,
        candidate_name,
        final_start_date,
        recipient_name,
        start_date,
        end_date,
        jobad_title,
        jobad_url
    ):
        """Send an email to supplier users confirming the candidate's deployment."""
        context = {
            "candidate_name": candidate_name,
            "recipient_name": recipient_name,
            "final_start_date": final_start_date,
            "start_date": start_date,
            "end_date": end_date,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
        }
        subject = _("{candidate_name} hat seinen Einsatz begonnen").format(candidate_name=candidate_name)
        body = render_to_string("emails/deployment_confirmation.html", context)

        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_reject_profile_email(candidate_name, recipient_email, rejection_reason, jobad_title, jobad_url, recipient_name):
        """Send an email to supplier users notifying them of a candidate rejection."""
        context = {
            "candidate_name": candidate_name,
            "recipient_name": recipient_name,
            "rejection_reason": rejection_reason,
            "jobad_title": jobad_title,
            "jobad_url": jobad_url,
        }
        subject = _("Absage – Kandidat: {candidate_name}").format(candidate_name=candidate_name)
        body = render_to_string("emails/reject_profile.html", context)

        return EmailService.send_email(recipient_email, subject, body)
    
    @staticmethod
    def send_new_user_email(user_profile, recipient_name, recipient_email, action_by):
        """Send an email for new user registration in the selected company."""
        context = {
            "user_name": f"{user_profile.user.first_name} {user_profile.user.last_name}",
            "recipient_name": recipient_name,
            "company_name": user_profile.company.name,
            "action_by": action_by,
        }
        subject = _("New User Registered: {user_name}").format(user_name=context["user_name"])
        body = render_to_string("emails/new_user_registered.html", context)

        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_linked_customer_admin_email(user_profile, recipient_email, action_by):
        # Verificar que el user_profile tenga una empresa asignada
        if not user_profile.company:
            return

        # Según el tipo de empresa, usamos el related name adecuado
        if user_profile.company.company_type == 'customer':
            partner = user_profile.company.customer_companypartner.first()
        else:
            partner = user_profile.company.company_companypartner.first()

        if not partner:
            # Si no hay un CompanyPartner vinculado, no se envía nada
            return

        context = {
            "user_name": f"{user_profile.user.first_name} {user_profile.user.last_name}",
            "linked_company_name": user_profile.company.name,
            "customer_company_name": partner.customer.name,
            "action_by": action_by,
        }
        subject = _("New User in Linked Company: {linked_company_name}").format(linked_company_name=context["linked_company_name"])
        body = render_to_string("emails/linked_customer_admin_notification.html", context)

        return EmailService.send_email(recipient_email, subject, body)

    @staticmethod
    def send_status_changed_email(jobad, recipient_email, action_by):
        """
        Send an email notification when a job ad's status changes.
        """
        base_url = "https://ppms.proserv-dl.de"
        jobad_url = f"{base_url}/jobads/{jobad.id}/?tab=details"

        # Build context for the email template.
        context = {
            "jobad_title": jobad.position.title,
            "jobad_status": jobad.stage,
            "jobad_url": jobad_url,
            "open_positions": jobad.total - jobad.occupied if jobad.stage == "open_for_everyone" else None,
            "action_by": action_by,
        }

        subject = _("Statusänderung: Stellenanzeige: {jobad_title}, jetzt {jobad_status}").format(
            jobad_title=context["jobad_title"],
            jobad_status=context["jobad_status"]
        )
        body = render_to_string("emails/jobad_status_changed.html", context)

        return EmailService.send_email(recipient_email, subject, body)