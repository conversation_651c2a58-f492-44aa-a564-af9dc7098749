{% extends '_base_frontend.html' %}
{% load static %}
{% load i18n %}
{% load custom_filters %}
{% load application_filters %}

<style>
    .top-card {
        height: 120px;
        padding: 8px;
        font-size: 0.875rem;
    }
    .top-card img {
        width: 32px;
        height: 32px;
    }
    .top-card .text-xl {
        font-size: 0.875rem;
    }
    .top-card .text-sm {
        font-size: 0.75rem;
    }
    .card-scroll {
        overflow-y: auto;
    }
    .toggle-checkbox {
        right: 10px;
        transition: all 0.3s;
        position: absolute;
    }
    .toggle-label {
        width: 50px;
        height: 20px;
        background-color: #4a5568;
        display: flex;
        justify-content: space-between;
        padding: 0 6px;
        align-items: center;
        border-radius: 9999px;
        position: relative;
        cursor: pointer;
    }
    .toggle-checkbox:checked {
        right: -20px;
        border-color: #38bdf8;
    }
    .toggle-checkbox:checked + .toggle-label {
        background-color: #38bdf8;
    }
    .toggle-checkbox:checked + .toggle-label span:first-child {
        color: white;
    }
    .toggle-checkbox:checked + .toggle-label span:last-child {
        color: black;
    }
    .table-cell-truncate {
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        font-size: 0.75rem; 
        text-overflow: ellipsis;
    }
    .table th, .table td {
        padding: 4px;
        font-size: 0.75rem;
    }
    .table-cell-date {
        white-space: nowrap;
    }
</style>

{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-2 md:px-4 lg:px-8">
    <!-- Main Content Section: Top Row Cards -->
    <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
        <!-- OVERVIEW Card -->
        {% if show_new_overview %}
            <div
                id="welcome-card"
                class="relative bg-white/90 backdrop-blur-lg pb-12 px-6 pt-6 rounded-2xl shadow-lg w-full h-[354.4px] overflow-hidden"
                >
                <div class="flex items-center gap-8">
                    <!-- ① Donut for workers -->
                    <div class="w-28 h-28">
                        <canvas id="workersChart"></canvas>
                    </div>

                    <!-- ② Legend‐style breakdown -->
                    <ul class="space-y-4 text-sm">
                        <!-- Open Job-ads -->
                        <li class="grid grid-cols-[auto_1fr_auto] items-center gap-x-2">
                            <span class="w-3 h-3 bg-teal-400 rounded-full"></span>
                            <div>
                                <span class="font-medium">Offene Jobanzeigen:</span>
                                {{ dashboard_open_count }}
                            </div>
                            <a href="{% url 'jobad-list' %}" class="text-accent-500 text-xs hover:underline">
                                {% trans "View List" %} <i class="fa-solid fa-circle-chevron-right ml-2"></i>
                            </a>
                        </li>

                        <!-- Candidates -->
                        <li class="grid grid-cols-[auto_1fr_auto] items-center gap-x-2">
                            <span class="w-3 h-3 bg-green-400 rounded-full"></span>
                            <div>
                            <span class="font-medium">Bewerber:</span>
                            {{ dashboard_total_cands }}
                            </div>
                            <a href="{% url 'candidate-list' %}" class="text-accent-500 text-xs hover:underline">
                                {% trans "View List" %} <i class="fa-solid fa-circle-chevron-right ml-2"></i>
                            </a>
                        </li>

                        <!-- Nationalities -->
                        <li class="grid grid-cols-[auto_1fr_auto] items-center gap-x-2">
                            <span class="w-3 h-3 bg-yellow-400 rounded-full"></span>
                            <div>
                            <span class="font-medium">Nationalitäten:</span>
                            {{ dashboard_nationalities }}
                            </div>
                            <a href="{% url 'reports-supplier-evaluation' %}" class="text-accent-500 text-xs hover:underline">
                                {% trans "View Report" %} <i class="fa-solid fa-circle-chevron-right ml-2"></i>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Pin a button at the bottom-right if you still want one -->
                <div class="flex justify-end">
                    <a
                        href="{% url 'reports' %}"
                        class="py-2 px-4 hover:shadow-lg hover:bg-secondary-500 hover:text-black bg-accent-500 text-white rounded-xl text-sm font-medium transition-all"
                    >
                        <i class="fa-solid fa-chart-column"></i> {% trans "Full Reports" %}
                    </a>
                </div>
            </div>
        {% else %}
            {# ─── the OLD “Welcome Back” card ─── #}
            <div
                id="welcome-card"
                class="top-card text-white p-2 rounded-2xl shadow-md bg-cover bg-center w-full"
                style="background-image: url('{% static 'images/LKW.jpg' %}');"
            >
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                    <img src="{% static 'images/user.png' %}" alt="User"
                        class="w-8 h-8 rounded-full">
                    <div>
                        {% if user.is_authenticated %}
                        <p class="text-sm font-semibold">
                            {{ user.first_name }} {{ user.last_name }}
                        </p>
                        {% else %}
                        <p class="text-sm font-semibold">{% trans "Guest" %}</p>
                        {% endif %}
                        <p class="text-xs text-gray-400">{% trans "Hello, Welcome back!" %}</p>
                    </div>
                    </div>
                    <div class="flex items-center bg-gray-700 rounded-full px-2 py-1">
                    <i class="fa fa-bell text-white text-xs"></i>
                    <span class="ml-1 bg-secondary-500 text-black text-xs rounded px-2 py-1">
                        {{ todo_applications|length }} {% trans "New" %}
                    </span>
                    </div>
                </div>
            </div>
        {% endif %}
        <!-- Excel Report Card -->
        <div id="excel-card" class="top-card bg-white/90 backdrop-blur-lg border border-gray-200 p-4 rounded-2xl shadow-lg w-full">
            <!-- Header -->
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-file-excel text-green-500 mr-2"></i> {% trans "Excel Report" %}
                </h2>
            </div>

            <div x-data="{
                    allCompanies: JSON.parse('{{ companies_json|escapejs }}'),
                    selectedType: 'candidates',
                    companyFilter: '',
                    filteredCompanies: [],
                    userRole: '{{ request.user.userprofile.role }}',
                    userCompanyType: '{{ request.user.userprofile.company.company_type }}',
                    userCompanyId: '{{ request.user.userprofile.company.id }}',
                    linkedCustomerCompanies: JSON.parse('{{ linked_customer_companies_json|escapejs }}'),
                    updateCompanyFilter() {
                        // Global admin: show all companies
                        if (this.userRole == '1' && this.userCompanyType === 'global') {
                            if (this.selectedType === 'jobads') {
                                // Show only companies with company_type = 'customer'
                                this.filteredCompanies = this.allCompanies.filter(company => company.fields.company_type === 'customer');
                            } else {
                                // Show only companies with company_type = 'supplier' or 'vendor' (master vendor)
                                this.filteredCompanies = this.allCompanies.filter(company => 
                                    company.fields.company_type === 'supplier' || company.fields.company_type === 'vendor'
                                );
                            }
                        }
                        else if (this.userRole == '1' && (this.userCompanyType === 'supplier' || this.userCompanyType === 'vendor')) {
                            // Only allow their own company
                            this.filteredCompanies = this.allCompanies.filter(company =>
                                company.pk == this.userCompanyId
                            );
                        }
                        // Supplier users: show only their own company
                        else if(['2','3','4','6','7','8'].includes(this.userRole)) {
                            if(this.userCompanyType === 'supplier') {
                                if(this.selectedType === 'jobads') {
                                    // For jobad exports, show only customer companies linked to this supplier.
                                    this.filteredCompanies = this.allCompanies.filter(company =>
                                        this.linkedCustomerCompanies.includes(company.pk) &&
                                        company.fields.company_type === 'customer'
                                    );
                                } else {
                                    // For candidates and workers, show only the supplier's own company.
                                    this.filteredCompanies = this.allCompanies.filter(company =>
                                        company.pk == this.userCompanyId
                                    );
                                }
                            }
                        }
                        // Customer Admin (Role 1) sees only their own company
                        else if (this.userRole == '1' && this.userCompanyType === 'customer') {
                            if (this.selectedType === 'jobads') {
                                this.filteredCompanies = this.allCompanies.filter(company => company.pk == this.userCompanyId);
                            } else {
                                this.filteredCompanies = [];
                            }
                        }
                        else if (this.userRole == '5' && this.userCompanyType === 'customer') {
                            if (this.selectedType === 'jobads') {
                                this.filteredCompanies = this.allCompanies.filter(company => company.pk == this.userCompanyId);
                            } else {
                                this.filteredCompanies = [];
                            }
                        }
                    },
                    init() {
                        this.updateCompanyFilter();
                    }
                }" x-init="init">
                <!-- Selection Section -->
                <div class="space-y-3">
                    <!-- Evaluation Type -->
                    <label class="block font-semibold text-gray-700 text-xs">{% trans "Choose Evaluation Type:" %}</label>
                    <select id="evaluation_type" x-model="selectedType" @change="updateCompanyFilter()" 
                        class="bg-gray-50 border border-gray-300 text-sm rounded-lg p-2 w-full cursor-pointer focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition">
                        <option value="candidates">{% trans "Candidates" %}</option>
                        <option value="workers">{% trans "Workers" %}</option>
                        <option value="jobads">{% trans "Job ads" %}</option>
                    </select>
                    
                    <!-- Company Selection -->
                    <label class="block font-semibold text-gray-700 text-xs">{% trans "Select Company:" %}</label>
                    <select id="company_filter" x-model="companyFilter"
                        class="bg-gray-50 border border-gray-300 text-sm rounded-lg p-2 w-full">
                        <option value="">{% trans "All Companies" %}</option>
                        <template x-for="company in filteredCompanies" :key="company.pk">
                            <option :value="company.pk" x-text="company.fields.name"></option>
                        </template>
                    </select>
                </div>

                <!-- Date Pickers -->
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="font-semibold text-gray-700 text-xs">{% trans "From Date:" %}</label>
                        <input type="date" id="from_date"
                            class="bg-gray-50 border border-gray-300 text-sm rounded-lg p-2 w-full cursor-pointer focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition">
                    </div>
                    <div>
                        <label class="font-semibold text-gray-700 text-xs">{% trans "To Date:" %}</label>
                        <input type="date" id="to_date"
                            class="bg-gray-50 border border-gray-300 text-sm rounded-lg p-2 w-full cursor-pointer focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition">
                    </div>
                </div>

                <!-- Generate Button -->
                <div class="mt-5 flex justify-center">
                    <button onclick="generateExcel()"
                        class="bg-accent-500 hover:shadow-lg text-white py-2 px-4 rounded-xl text-sm font-medium w-full md:w-auto transition-all hover:bg-secondary-500 hover:text-black">
                        <i class="fas fa-download mr-2"></i> {% trans "Generate Excel" %}
                    </button>
                </div>
            </div>
        </div>
        
        <!-- To Do List Card -->
        <div id="todo-card" class="bg-white/90 backdrop-blur-lg p-4 rounded-2xl shadow-lg w-full h-[354.4px] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">{% trans "To-do-List" %}</h2>
                <i class="fa fa-expand text-gray-500"></i>
            </div>

            {% if todo_applications %}
                <ul class="space-y-3">
                    {% for application in todo_applications %}
                        <li class="flex items-center justify-between bg-gray-50 p-3 rounded-lg shadow-sm hover:shadow-md transition-all">
                            <div class="flex items-center space-x-3">
                                <div>
                                    <a href="{% url 'jobad-detail' application.jobad.id %}?tab=candidate-profile"
                                    class="font-medium text-gray-900 hover:text-accent-500 transition">
                                        {{ application.jobad.position }} - {{ application.candidate.firstname }} {{ application.candidate.lastname }}
                                    </a>
                                    <p class="text-xs text-gray-500">
                                        {{ stage_messages|dict_lookup:application.stage }}
                                    </p>
                                </div>
                            </div>
                            <span class="text-gray-500 text-sm">{{ application.created_date|date:"d.m.Y" }}</span>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p class="text-gray-400 text-sm text-center">{% trans "No pending actions." %}</p>
            {% endif %}
        </div>
    </div>

    <!-- Lower Section -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-4 mb-4">
        <!-- Job ad Section -->
        <div class="bg-white text-gray-800 p-4 rounded-lg shadow-md mt-4">
            <div class="flex justify-between items-center mb-4">
                <p class="text-l text-black font-bold">{% trans "Job ad List" %}</p>
                {% comment %} <button class="flex items-center space-x-2 px-1 py-2 rounded-lg text-accent-500 hover:text-white hover:bg-secondary-500">
                    <i class="fa fa-filter text-xs"></i>
                </button> {% endcomment %}
            </div>
            <table class="min-w-full text-left text-sm overflow-hidden">
                <thead class="bg-gradient-to-r from-blue-800 to-accent-500 text-white sticky top-0 z-10">
                    <tr>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Title" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Company" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Positions" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Location" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Department" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Date" %}</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for jobad in jobads %}
                    <tr class="hover:bg-blue-50 hover:cursor-pointer hover:shadow-md transition-all">
                        <td class="py-2 px-2 table-cell-truncate">
                            <a href="{% url 'jobad-detail' jobad.id %}" class="hover:underline">{{ jobad.position }}</a>
                        </td>
                        <td class="py-2 px-2 table-cell-truncate">{{ jobad.company_value }}</td>
                        <td class="py-2 px-2 table-cell-truncate">
                            {% if jobad.total > 999 %}
                                ∞
                            {% else %}
                                {{ jobad.total|subtract:jobad.occupied }}
                            {% endif %}
                        </td>                        
                        <td class="py-2 px-2 table-cell-truncate">{{ jobad.location.city }}</td>
                        <td class="py-2 px-2 table-cell-truncate">{{ jobad.department_value }}</td>
                        <td class="py-2 px-2 table-cell-date">{{ jobad.created_date|date:"d.m.Y" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="py-2 px-2 text-center text-gray-500">{% trans "No job ads available." %}</td>
                    </tr>
                    {% endfor %}
                </tbody>                                          
            </table>
        </div>
        
        <!-- Candidate List -->
        <div class="bg-white text-gray-800 p-4 rounded-lg shadow-md mt-4">
            <div class="flex justify-between items-center mb-4">
                <p class="text-l text-black font-bold">{% trans "New Applications" %}</p>
                {% comment %} <button class="flex items-center space-x-2 px-1 py-2 rounded-lg text-accent-500 hover:text-white hover:bg-secondary-500">
                    <i class="fa fa-filter text-xs"></i>
                </button> {% endcomment %}
            </div>
            <table class="min-w-full text-left text-sm overflow-hidden">
                <thead class="bg-gradient-to-r from-blue-800 to-accent-500 text-white sticky top-0 z-10">
                    <tr>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Candidate" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Position" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Location" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Department" %}</th>
                        <th class="py-2 px-2 border-b border-gray-300 font-semibold">{% trans "Created" %}</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for application in jobad_applications %}
                    <tr class="hover:bg-blue-50 hover:cursor-pointer hover:shadow-md transition-all">
                        <td class="py-2 px-2 table-cell-truncate">
                            <a href="{% url 'jobad-detail' application.jobad.id %}?tab=candidate-profile" class="hover:underline">
                                {{ application.candidate.firstname }} {{ application.candidate.lastname }}
                            </a>
                            <p style="font-size: 9px" class="text-gray-600">
                                {{ application.candidate.company.name }}
                            </p>
                        </td>
                        <td class="py-2 px-2 table-cell-truncate">
                            <a href="{% url 'jobad-detail' application.jobad.id %}" class="hover:underline">
                                {{ application.jobad.position }}
                            </a>
                        </td>
                        <td class="py-2 px-2 table-cell-truncate">{{ application.jobad.location.city }}</td>
                        <td class="py-2 px-2 table-cell-truncate">{{ application.jobad.department_value }}</td>
                        <td class="py-2 px-2 table-cell-date">{{ application.created_date|date:"d.m.Y" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="py-2 px-2 text-center text-gray-500">{% trans "No candidate available." %}</td>
                    </tr>
                    {% endfor %}
                </tbody>                                             
            </table>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
    const ctx = document.getElementById('workersChart').getContext('2d');

    // 1) Define a custom plugin that draws our center text
    const centerTextPlugin = {
        id: 'centerText',
        beforeDraw(chart, args, options) {
        const {ctx, width, height} = chart;

        // calculate total
        const total = chart.data.datasets[0].data.reduce((sum, v) => sum + v, 0);

        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // "Workers" label
        ctx.font = 'bold 12px Arial';
        ctx.fillStyle = '#444';
        ctx.fillText('Workers', width / 2.8, height / 2 - 10);

        // total number
        ctx.font = 'bold 18px Arial';
        ctx.fillStyle = '#222';
        ctx.fillText(total, width / 2.8, height / 2 + 10);

        ctx.restore();
        }
    };

    // 2) Create the chart, injecting our plugin
    new Chart(ctx, {
        type: 'doughnut',
        data: {
        labels: ['Active', 'Terminated', 'Rejected'],
        datasets: [{
            data: [
            {{ dashboard_active_workers }},
            {{ dashboard_terminated }},
            {{ dashboard_rejected }}
            ],
            backgroundColor: [
            'rgba(16,185,129,0.8)',
            'rgba(251,191,24,0.8)',
            'rgba(239,68,68,0.8)'
            ],
            borderWidth: 0
        }]
        },
        options: {
        cutout: '70%',
        plugins: {
            legend: {
            display: true,
            position: 'right',
            labels: { boxWidth: 10, padding: 10, font: { size: 10 } }
            },
            tooltip: {
            enabled: true,
            callbacks: {
                label: ctx => {
                const pct = ((ctx.parsed / ({{ dashboard_total_workers }} || 1)) * 100).toFixed(0);
                return `${ctx.label}: ${ctx.parsed} (${pct}%)`;
                }
            }
            }
        }
        },
        // 3) register our centerText plugin here
        plugins: [ centerTextPlugin ]
    });
    });
</script>
<script>
    // Function to generate the Excel file
    function generateExcel() {
        const evaluationType = document.getElementById('evaluation_type').value;
        const companyId = document.getElementById('company_filter').value;
        const fromDate = document.getElementById('from_date').value;
        const toDate = document.getElementById('to_date').value;
    
        // Send an AJAX request to the backend with the filter values
        fetch(`/generate-excel/?type=${evaluationType}&company_id=${companyId}&from_date=${fromDate}&to_date=${toDate}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${evaluationType}_data.xlsx`;
            document.body.appendChild(a);
            a.click();
            a.remove();
        })
        .catch(error => console.error('Error generating Excel:', error));    
    }
</script>
{% endblock %}