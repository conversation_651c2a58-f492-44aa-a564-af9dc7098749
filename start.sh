#!/bin/sh

set -e
POSTGRES_PORT=${POSTGRES_PORT:=5432}

echo "Waiting for database: $POSTGRES_HOST:$POSTGRES_PORT"
while ! (nc -z $POSTGRES_HOST $POSTGRES_PORT); do
  sleep 0.1
done

echo "Running database migrations..."
python ./src/manage.py migrate

echo "Running translate messages compilation..."
python ./src/manage.py makemessages -l de

echo "Running translate messages compilation..."
python ./src/manage.py compilemessages

echo "Starting django"
python ./src/manage.py runserver 0.0.0.0:8000