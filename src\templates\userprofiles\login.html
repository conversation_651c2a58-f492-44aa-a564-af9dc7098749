<!DOCTYPE html>
<html lang="en">
{% load static %}
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    
    <!-- Tailwind CSS (CDN link for styling) -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

    <!-- Add any custom styles if needed -->
    <style>
        .body {
            background-color: #001d67;
        }
        .button {
            background-color: #001d67;
            border-color: white;
            border: 1px solid;
        }
        .button:hover {
            border-color: #ffd200;
            color: #ffd200
        }
    </style>
</head>
<body class="body">
    <div class="flex h-screen">
        <!-- Left side with the background image -->
        <div class="w-5/6 bg-cover bg-center" style="background-image: url('{% static 'images/background/background.png' %}');">
        </div>

        <!-- Right side with the login form -->
        <div class="w-1/2 flex items-center justify-center bg-gray-800">
            <div class="bg-accent-500 p-8 max-w-lg w-full">
                <div class="mb-20">
                    <img src="{% static 'images/logo.png' %}" alt="Logo" class="mx-auto h-16">
                </div>
                <div class="text-white text-center">
                    <h1 class="text-2xl font-bold mb-4">Schön, Sie wiederzusehen!</h1>
                    {% comment %} <p class="mb-6">Noch kein Konto? <a href="{% url 'login' %}" class="text-blue-400 hover:underline">Jetzt registrieren</a></p> {% endcomment %}
                </div>
                {% if form.errors %}
                <div class="bg-red-100 text-red-700 px-4 py-3 rounded">
                    <p>Die Zugangsdaten sind nicht korrekt!</p>
                    <ul>
                        {% for field in form %}
                            {% for error in field.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                <form method="POST" action="{% url 'login' %}">
                    {% csrf_token %}
                    <div class="mb-4 mt-4">
                        <label for="username" class="block text-gray-400">Nutzername</label>
                        <input id="username" name="username" type="text" placeholder="Nutzername" class="w-full p-3 rounded-md bg-gray-700 text-white focus:outline-none" required>
                    </div>
                    <div class="mb-4">
                        <label for="password" class="block text-gray-400">Passwort</label>
                        <input id="password" name="password" type="password" placeholder="Passwort" class="w-full p-3 rounded-md bg-gray-700 text-white focus:outline-none" required>
                    </div>
                    <div class="flex justify-center mb-6 w-full">
                        <button class="button text-white w-full py-2 px-6 rounded-xl">Anmelden</button>
                    </div>
                    <div class="flex justify-center mb-6 w-full">
                        <p class="text-white">
                            <a href="{% url 'impressum' %}" class="hover:text-secondary-500">Impressum</a> | 
                            <a href="{% url 'nutzungsbestimmungen' %}" class="hover:text-secondary-500">Nutzungsbestimmungen</a> | 
                            <a href="{% url 'datenschutz' %}" class="hover:text-secondary-500">Datenschutz</a>
                        </p>
                    </div>
                </form>
                <div class="flex justify-center mb-2 w-full">
                    <a href="{% url 'password_reset' %}" class="text-blue-400 hover:underline">Passwort vergessen?</a>
                </div>
            </div>
        </div>
    </div>
    {% if messages %}
    <div class="messages mt-4">
        {% for message in messages %}
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative {{ message.tags }}" role="alert">
            <span class="block sm:inline">{{ message }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}      
</body>
</html>
