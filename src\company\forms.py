from django import forms
from company.models import Company, CompanyDocument, CompanyPosition, CompanyLocation, CompanyPartner, CompanyStructure
from django.conf import settings
from django.forms.widgets import SelectDateWidget
from company.models import Company, CompanyPosition, CompanyLocation, CompanyDocument, RejectionEndReason, \
    CompanySupplier, Supplier


class CompanyForm(forms.ModelForm):
    class Meta:
        model = Company
        exclude = ['created_by', 'updated_by', 'deleted_at', 'structure', 'extended', 'anonymous', 'company_type', 'status']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'street': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'post_code': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'city': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'telephone_number': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'country': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
            'deleted_at': SelectDateWidget(attrs={
                'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['country'].empty_label = None
        self.fields['country'].initial = None
        self.fields['profile_picture'].widget.attrs.update({
            'class': 'bg-white border border-gray-300 rounded-lg text-gray-900 text-sm block w-full p-2.5 focus:outline-none'
        })
        
class PositionForm(forms.ModelForm):
    class Meta:
        model = CompanyPosition
        fields = ['title']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                         'focus:border-blue-500 block w-full p-2.5',
                'placeholder': 'Enter position title'
            }),
        }


class LocationForm(forms.ModelForm):
    company_telephone = forms.CharField(
        required=False,  # This makes the field optional
        widget=forms.TextInput(attrs={
            'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                     'focus:border-blue-500 block w-full p-2.5',
            'placeholder': 'Company telephone'
        })
    )
    class Meta:
        model = CompanyLocation
        fields = ['street', 'street_number', 'post_code', 'city', 'company_telephone']
        widgets = {
            'street': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                         'focus:border-blue-500 block w-full p-2.5',
                'placeholder': 'Street'
            }),
            'street_number': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                         'focus:border-blue-500 block w-full p-2.5',
                'placeholder': 'Nr'
            }),
            'post_code': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                         'focus:border-blue-500 block w-full p-2.5',
                'placeholder': 'Postal code'
            }),
            'city': forms.TextInput(attrs={
                'class': 'bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 '
                         'focus:border-blue-500 block w-full p-2.5',
                'placeholder': 'City'
            }),
        }


class DocumentForm(forms.ModelForm):
    class Meta:
        model = CompanyDocument
        fields = ['document', 'expiration_date', 'structure']
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # only show first-level LGI nodes for company=61
        self.fields['structure'].queryset = CompanyStructure.objects.filter(
            company_id=settings.LGI_COMPANY_ID,
            id__regex=rf"^{settings.LGI_COMPANY_ID}\.\d+$"
        ).order_by('value')
        self.fields['structure'].required = False


class RejectionEndReasonForm(forms.ModelForm):
    class Meta:
        model = RejectionEndReason
        fields = ['text']
        widgets = {
            'text': forms.TextInput(attrs={'class': 'form-control'}),
        }


class CompanySupplierForm(forms.ModelForm):
    class Meta:
        model = CompanySupplier
        fields = ['supplier', 'master_vendor']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['supplier'].queryset = Company.objects.filter(company_type='supplier')
        self.fields['master_vendor'].queryset = Company.objects.filter(company_type='vendor')
