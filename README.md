# PPMS

## Dev Notes
- For dev environment we need redis-server running + 3 terminals
  python src/manage.py runserver
  npx tailwindcss -i ./src/static/src/input.css -o ./src/static/src/output.css --watch
  in the same terminal with the virtual env turned on: $env:PYTHONPATH = "src"  and then => celery -A config worker --loglevel=info --pool=solo
- 
## Overview
Install the required dependencies:
```bash
pip install -r requirements.txt
```
Apply migrations:
```bash
python src/manage.py migrate
```
Start server:
```bash
python src/manage.py runserver
```
## Table of Contents

- [Documentation](#documentation)
  - [Models](#models)
    - [Jobad Models](#jobad-models)
    - [Company Models](#company-models)
    - [Common Models](#common-models)
  - [Contributors](#contributors)
    - [Dispatcher](#dispatcher)
    - [Decorators](#decorators)
    - [AppConfig](#appconfig)

## Documentation

### Models

- [Jobad Models](documentation/models/jobad_models.md)
- [Company Models](documentation/models/company_models.md)
- [Common Models](documentation/models/common_models.md)

### Contributors

- [Dispatcher](documentation/contrib/dispatch.md)
- [Decorators](documentation/contrib/decorators.md)
- [AppConfig](documentation/contrib/appconfig.md)

### Testing

- [Testing](documentation/testing/test.md)