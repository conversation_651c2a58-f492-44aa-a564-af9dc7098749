{% extends "_base_frontend.html" %}
{% load application_filters %} 

{% block title %}All Reports{% endblock %}

{% block custom_header %}
  <style>
    main {
      padding: 1rem !important;
    }
    .report-container {
      display: flex;
      flex-direction: row;
      gap: 1.5rem;
      width: 100%;
    }
    @media (max-width: 1023px) {
      .report-container {
        flex-direction: column;
      }
    }
    :root {
      --y-offset: calc(64px + 64px + 2rem);
    }

    .chart-section,
    .table-section {
      flex: 1;
      max-height: calc(100vh - var(--y-offset));
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      padding: 0.75rem;
      background-color: rgb(229, 231, 235);
      display: flex;
      flex-direction: column;
    }
    .chart-scroll-wrapper,
    .table-scroll-wrapper {
      flex: 1;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
    .chart-scroll-wrapper::-webkit-scrollbar,
    .table-scroll-wrapper::-webkit-scrollbar {
      display: none;
    }
    .table-scroll-wrapper table {
      border-collapse: separate;
      width: 100%;
      border-spacing: 0 0.5rem;
      margin-top: 3.5rem;
    }
    .table-scroll-wrapper thead th {
      position: sticky;
      top: 0;
      background-color: rgb(229, 231, 235);
      z-index: 10;
    }
    .table-scroll-wrapper tbody tr:hover {
      background-color: rgb(229, 231, 235);
    }
  </style>
{% endblock %}

{% block content %}
  <div class="report-container">
    <div class="chart-section">
      <h2 class="text-lg font-semibold mb-2">{{ total_count }} Open Jobads</h2>
      <div class="chart-scroll-wrapper">
        <canvas
          id="jobadsChart"
          style="
            display: block;
            width: 100%;
            height: {{ total_count|mul:40 }}px;
            box-sizing: border-box;
          "
        ></canvas>
      </div>
    </div>
    <div class="table-section">
      <h2 class="text-lg font-semibold mb-2">Report</h2>
      <div class="table-scroll-wrapper">
        <table class="min-w-full text-sm text-left">
          <thead>
            <tr>
              <th class="px-2 py-1 font-medium text-gray-600">ID / Name</th>
              <th class="px-2 py-1 font-medium text-gray-600">Standort</th>
              <th class="px-2 py-1 font-medium text-gray-600">Abteilung</th>
              <th class="px-2 py-1 font-medium text-gray-600">Kostenstelle</th>
              <th class="px-2 py-1 font-medium text-gray-600">Offene</th>
              <th class="px-2 py-1 font-medium text-gray-600">Besetzte</th>
            </tr>
          </thead>
          <tbody>
            {% for ja in open_jobads %}
            <tr>
              <td class="px-2 py-1 text-xs">{{ ja.id }} – {{ ja.position.title }}</td>
              <td class="px-2 py-1 text-xs">{{ ja.location.name }}</td>
              <td class="px-2 py-1 text-xs">{{ ja.department_value }}</td>
              <td class="px-2 py-1 text-xs">{{ ja.cost_department }}</td>
              <td class="px-2 py-1 text-xs">{{ ja.open_positions }}</td>
              <td class="px-2 py-1 text-xs">{{ ja.candidate_count }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  {# ──────────────────────────────────────────────────────────────── #}
  {# Chart.js Script (with X‐axis on top)                            #}
  {# ──────────────────────────────────────────────────────────────── #}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    (function() {
      const labels          = {{ labels|safe }};
      const actualPositions = {{ actual_positions|safe }};
      const cappedPositions = {{ capped_positions|safe }};
      const infiniteMask    = {{ infinite_mask|safe }};
      const candidateCounts = {{ candidates|safe }};

      const ctx = document.getElementById('jobadsChart').getContext('2d');
      new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Offene Positionen (≤ 40)',
              data: cappedPositions,
              backgroundColor: 'rgba(37, 99, 235, 0.6)',
              borderColor: 'rgba(37, 99, 235, 1)',
              borderWidth: 1,
              borderRadius: 4,
            },
            {
              label: '(∞)',
              data: infiniteMask,
              backgroundColor: 'rgba(37, 99, 235, 0.6)',
              borderColor: 'rgba(37, 99, 235, 1)',
              borderWidth: 1,
              borderRadius: 4,
            },
            {
              label: '# of Candidates',
              data: candidateCounts,
              backgroundColor: 'rgba(239, 68, 68, 0.6)',
              borderColor: 'rgba(239, 68, 68, 1)',
              borderWidth: 1,
              borderRadius: 4,
            }
          ]
        },
        options: {
          indexAxis: 'y',
          responsive: false,  
          maintainAspectRatio: false,
          interaction: {
            mode: 'nearest',
            axis: 'y',
            intersect: false
          },
          scales: {
            x: {
              position: 'top',   
              beginAtZero: true,
              max: 40,
              title: { display: true, text: 'Count' },
              ticks: {
                callback: function(value) {
                  return (value === 40) ? '∞' : value;
                },
                font: { size: 12 }
              },
              grid: {
                drawBorder: false,
                color: 'rgba(203, 213, 225, 0.5)'
              }
            },
            y: {
              title: { display: true, text: 'Job Ad (ID – Title)' },
              ticks: { autoSkip: false, font: { size: 11 } },
              grid: {
                drawBorder: false,
                color: 'rgba(203, 213, 225, 0.3)'
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: { boxWidth: 12, padding: 16, font: { size: 12 } }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              callbacks: {
                label: function(context) {
                  const idx = context.dataIndex;
                  const dsLabel = context.dataset.label;

                  if (dsLabel === 'Offene Positionen (≤ 40)') {
                    const actual = actualPositions[idx];
                    return (actual <= 40) ? `Offene Positionen: ${actual}` : '';
                  }
                  if (dsLabel === 'Offene Positionen > 40 (∞)') {
                    return (actualPositions[idx] > 40) ? 'Offene Positionen: ∞' : '';
                  }
                  if (dsLabel === '# of Candidates') {
                    return `# of Candidates: ${context.parsed.x}`;
                  }
                  return '';
                }
              }
            }
          }
        }
      });
    })();
  </script>
{% endblock %}
