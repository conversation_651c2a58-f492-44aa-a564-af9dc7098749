from django.utils.translation import gettext_lazy as _

STAGE_MAPPING = {
    "requested": _("Requested"),
    "approved": _("Approved"),
    "rejected": _("Rejected"),
    "interview": _("Interview Proposal"),
    "interview_completed": _("Interview Completed"),
    "interview_rejected": _("Interview Rejected"),
    "candidate_selection": _("Candidate Selection"),
    "tentatively_occupied": _("Tentatively Occupied"),
    "candidate_selection_rejected": _("Candidate Selection Rejected"),
    "completed": _("Completed"),
    "canceled": _("Canceled"),
    "selection_rejected": _("Selection Rejected"),
    "open_for_everyone": _("Offen"),
    "filled": _("Besetzt"),
    "withdrawn": _("Withdrawn"),
    "open_again": _("Offen"),
    "retry": _("Retry"),
}

def format_stage(value):
    return STAGE_MAPPING.get(value, value)
