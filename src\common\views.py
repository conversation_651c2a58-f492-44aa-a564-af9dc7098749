import csv
from datetime import datetime, date
import json
import io
import logging
from django_countries import countries
import openpyxl
import pandas as pd
from openpyxl import load_workbook
from django.core.serializers import serialize
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.db.models.functions import TruncDate
from django.contrib.auth.decorators import login_required, user_passes_test
from django.shortcuts import render, redirect
from django.http import HttpResponseRedirect, HttpResponse, JsonResponse
from django.contrib.auth.hashers import make_password
from django.urls import reverse, reverse_lazy
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.utils.dateparse import parse_date
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.contrib.auth import get_user_model, login as auth_login, logout as auth_logout
from django.contrib.auth.tokens import default_token_generator
from django.contrib.auth.forms import SetPasswordForm
from django.views import View
from django.views.generic.edit import FormView
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.debug import sensitive_post_parameters
from django.utils.translation import gettext_lazy as _
from django.db.models import Q, F, Count, Min, ExpressionWrapper, DurationField
from django.contrib.auth.views import PasswordResetConfirmView as BasePasswordResetConfirmView, PasswordResetView, LoginView
from common.models import Country
from company.forms import CompanyForm
from company.models import Company, CompanyLocation, CompanyPartner, CompanyPosition, CompanyStructure
from jobad.forms import CandidateForm
from jobad.models import Candidate, Jobad, JobadApplication, JobadApplicationStage, JobadDepartment, JobadStage, JobadStatus, RejectedCandidateReason, Worker, WorkerRejectionReason, WorkerStage
from email_service.tasks import send_async_email 
from userprofiles.models import UserProfileDepartments


def _restrict_to_user_departments(qs, user):
    """
    If user is a proposer (role 5), only keep JobAds whose
    JobadDepartment.structure is in the user's linked structures.
    Otherwise leave qs untouched.
    """
    if user.userprofile.role == 5:
        linked_structure_ids = (
            UserProfileDepartments.objects
              .filter(user_ref=user.userprofile)
              .values_list('structure__id', flat=True)
        )
        return (
            qs
            # note: jobaddepartment (plural!) is the auto‐generated reverse relation
            # and structure is the FK on JobAdDepartment
            .filter(jobaddepartment__structure__in=linked_structure_ids)
            .distinct()
        )
    return qs

class index(View):
    template_name = 'index.html'
    MAX_ENTITIES = 15
    PENDING_STAGES = [
        JobadApplicationStage.REQUESTED,
        JobadApplicationStage.APPROVED,
        JobadApplicationStage.INTERVIEW,
        JobadApplicationStage.INTERVIEW_COMPLETED,
        JobadApplicationStage.TENTATIVELY_OCCUPIED,
        JobadApplicationStage.CANDIDATE_SELECTION,
    ]
    ALLOWED_JOBAD_STAGES = [
        JobadStage.REQUESTED,
        JobadStage.APPROVED,
        JobadStage.OPEN_FOR_EVERYONE,
        JobadStage.OPEN_AGAIN,
    ]

    def get_filtered_jobad_applications(self, user):
        """
        Returns the JobadApplications filtered by the user's company type,
        ordered by creation date (new applications on top) and only those
        in the pending stages.
        """
        user_profile = user.userprofile
        user_company = user_profile.company

        if user_company.company_type == 'customer':
            if user_profile.role == 5:  # Proposer
                linked_structures = UserProfileDepartments.objects.filter(
                    user_ref=user_profile
                ).values_list('structure__id', flat=True)

                linked_jobad_ids = JobadDepartment.objects.filter(
                    structure__in=linked_structures
                ).values_list('jobad_id', flat=True)

                qs = JobadApplication.objects.filter(
                    jobad_id__in=linked_jobad_ids
                )
                qs = qs.filter(stage__in=[
                    JobadApplicationStage.INTERVIEW,
                    JobadApplicationStage.APPROVED,
                    JobadApplicationStage.INTERVIEW_COMPLETED,
                    JobadApplicationStage.TENTATIVELY_OCCUPIED,
                ])
            else:
                qs = JobadApplication.objects.filter(jobad__company=user_company)
                qs = qs.filter(stage__in=self.PENDING_STAGES)
        elif user_company.company_type in ['supplier', 'vendor']:
            linked_structures = CompanyPartner.objects.filter(
                company=user_company
            ).values_list('structure', flat=True)
            qs = JobadApplication.objects.filter(
                jobad__id__in=JobadDepartment.objects.filter(
                    structure__in=linked_structures
                ).values_list('jobad_id', flat=True),
                candidate__company=user_company
            )
        else:
            qs = JobadApplication.objects.all()
        qs = qs.filter(stage__in=self.PENDING_STAGES)
        return qs.order_by('-created_date')
        
    def get_todo_applications(self, user, from_date=None, to_date=None):
        """
        Filters applications for the to-do list based on the user's role,
        ensuring that older to-do items are at the top.
        """
        qs = self.get_filtered_jobad_applications(user)

        user_profile = user.userprofile
        user_company = user_profile.company

        qs = qs.exclude(stage__in=[
            JobadApplicationStage.COMPLETED,
            JobadApplicationStage.REJECTED
        ])

        if user_company.company_type == 'supplier':
            qs = qs.filter( 
                Q(stage=JobadApplicationStage.INTERVIEW) &
                (Q(last_suggestion_by__isnull=True) | Q(last_suggestion_by='') | Q(last_suggestion_by='proposer'))
                |
                Q(stage=JobadApplicationStage.CANDIDATE_SELECTION)
            )
        elif user_company.company_type == 'customer':
            if user_profile.role == 5:
                # For proposers: get structures they are linked to.
                linked_structures = UserProfileDepartments.objects.filter(
                    user_ref=user_profile
                ).values_list('structure__id', flat=True)
                linked_jobad_ids = JobadDepartment.objects.filter(
                    structure__in=linked_structures
                ).values_list('jobad_id', flat=True)

                qs = qs.filter(
                    Q(jobad_id__in=linked_jobad_ids) &
                    (
                        Q(stage=JobadApplicationStage.INTERVIEW, last_suggestion_by='supplier') |
                        Q(stage__in=[
                            JobadApplicationStage.APPROVED,
                            JobadApplicationStage.INTERVIEW_COMPLETED,
                            JobadApplicationStage.TENTATIVELY_OCCUPIED,
                        ])
                    )
                )
            else:
                qs = qs.filter(
                    Q(stage=JobadApplicationStage.REQUESTED) |  
                    Q(stage=JobadApplicationStage.INTERVIEW, last_suggestion_by='supplier') |
                    Q(stage__in=[
                        JobadApplicationStage.APPROVED,
                        JobadApplicationStage.INTERVIEW_COMPLETED,
                        JobadApplicationStage.TENTATIVELY_OCCUPIED,
                    ])
                )
        elif user_company.company_type == 'global':
            qs = qs.filter(stage__in=[
                JobadApplicationStage.INTERVIEW,
                JobadApplicationStage.INTERVIEW_COMPLETED,
                JobadApplicationStage.APPROVED,
                JobadApplicationStage.TENTATIVELY_OCCUPIED,
                JobadApplicationStage.CANDIDATE_SELECTION
            ])

        if from_date is None:
            from_date = date(2025, 3, 1)

        qs = qs.filter(modified_date__gte=from_date)
        if to_date:
            qs = qs.filter(modified_date__lte=to_date)

        # Order ascending so that older applications (by modified_date) appear first.
        return qs.order_by('modified_date')

    def get_filtered_jobads(self, user):
        """
        Filter job ads based on the user's company type and allowed stages,
        ordered so that new job ads appear first.
        """
        user_profile = user.userprofile
        user_company = user_profile.company

        if user_company.company_type == 'customer':
            if user_profile.role == 5:
                linked_structures = UserProfileDepartments.objects.filter(
                    user_ref=user_profile
                ).values_list('structure', flat=True)
                qs = Jobad.objects.filter(
                    id__in=JobadDepartment.objects.filter(
                        structure__in=linked_structures
                    ).values_list('jobad_id', flat=True)
                )
            else:
                qs = Jobad.objects.filter(company=user_company)
        elif user_company.company_type in ['supplier', 'vendor']:
            linked_structures = CompanyPartner.objects.filter(
                company=user_company
            ).values_list('structure', flat=True)
            qs = Jobad.objects.filter(
                id__in=JobadDepartment.objects.filter(
                    structure__in=linked_structures
                ).values_list('jobad_id', flat=True)
            )
        else:
            qs = Jobad.objects.all()

        qs = qs.filter(stage__in=self.ALLOWED_JOBAD_STAGES)
        return qs.order_by('-created_date')

    def get(self, request, *args, **kwargs):
        user = request.user
        is_lgi = request.user.userprofile.company_id == LGI_COMPANY_ID or request.user.userprofile.company.company_type == 'global'
        if request.user.userprofile.company.company_type == 'supplier':
            linked_customer_companies = list(
                CompanyPartner.objects.filter(company=request.user.userprofile.company)
                .values_list('customer_id', flat=True)
            )
        else:
            linked_customer_companies = []
        # Example: get date filters from query parameters (or adapt this to your form inputs)
        todo_from_date = request.GET.get('todo_from_date')
        todo_to_date = request.GET.get('todo_to_date')
        # Parse the dates if provided (using parse_date or datetime.strptime)
        from_date = parse_date(todo_from_date) if todo_from_date else None
        to_date = parse_date(todo_to_date) if todo_to_date else None

        # Get the filtered job ads and applications for other parts of your dashboard
        jobads = self.get_filtered_jobads(user)[:self.MAX_ENTITIES]
        jobad_applications = self.get_filtered_jobad_applications(user)[:self.MAX_ENTITIES]
        # Now get the to-do applications using our new helper method.
        todo_applications = self.get_todo_applications(user, from_date, to_date)[:self.MAX_ENTITIES]
            
        # 1) open jobads (Dashboard “open jobads”)
        open_qs = Jobad.objects.filter(
            company_id=LGI_COMPANY_ID,
            stage__in=[JobadStage.OPEN_FOR_EVERYONE, JobadStage.OPEN_AGAIN]
        )
        open_qs = _restrict_to_user_departments(open_qs, request.user)
        dashboard_open_count = open_qs.count()

        # 2) which suppliers the user may see
        if request.user.userprofile.role == 5:
            linked_structures = UserProfileDepartments.objects.filter(
                user_ref=request.user.userprofile
            ).values_list('structure__id', flat=True)
            supplier_ids = CompanyPartner.objects.filter(
                customer_id=LGI_COMPANY_ID,
                structure__in=linked_structures
            ).values_list('company_id', flat=True)
        else:
            supplier_ids = CompanyPartner.objects.filter(
                customer_id=LGI_COMPANY_ID,
                company__company_type__in=['supplier', 'vendor']
            ).values_list('company_id', flat=True)

        suppliers = Company.objects.filter(id__in=supplier_ids)

        # 3) candidates + workers under those suppliers
        cand_qs   = Candidate.objects.filter(company__in=suppliers)
        worker_qs = Worker.objects.filter(candidate__company__in=suppliers)

        total_cands = cand_qs.count()
        total_workers = worker_qs.count()
        active_workers = worker_qs.filter(
            stage__in=[WorkerStage.APPROVED, WorkerStage.INTERNALLY_DEPLOYED]
        ).count()
        terminated_workers = worker_qs.filter(
            stage__in=[WorkerStage.DEPLOYMENT_FINISHED, WorkerStage.DEPLOYMENT_DISCONTINUED]
        ).count()
        total_declined = RejectedCandidateReason.objects.filter(
            applicant__company__in=suppliers
        ).count()
        if total_cands:
            pct_active = round(active_workers / total_cands * 100)
            pct_term   = round(terminated_workers   / total_cands * 100)
            pct_rej    = round(total_declined    / total_cands * 100)
        else:
            pct_active = pct_term = pct_rej = 0

        # 4) nationality diversity (just count distinct countries)
        nat_count = cand_qs.values('country').distinct().count()

        # Serialize companies and candidates
        companies = Company.objects.all()
        companies_json = serialize('json', companies)
        candidates = Candidate.objects.all()
        candidates_json = serialize('json', candidates)
        stage_messages = {
            'requested': "Kandidaten freigeben",
            'approved': "Interview/Probetag vergeben",
            'interview': "Interviewphase abschließen",
            'interview_completed': "Startdatum festlegen",
            'candidate_selection': "Startdatum bestätigen",
            'tentatively_occupied': "Start des Kandidaten bestätigen",
        }
        company_form = CompanyForm()
        candidate_form = CandidateForm()
        context = {
            "todo_applications": todo_applications,
            "stage_messages": stage_messages, 
            'companies': companies,
            'candidates': candidates,
            'jobads': jobads,
            'todo_applications': todo_applications,
            'candidates_json': candidates_json,
            'company_form': company_form,
            "show_new_overview": is_lgi,
            'candidate_form': candidate_form,
            'jobad_applications': jobad_applications,
            'companies_json': companies_json,
            'todo_from_date': todo_from_date or '',
            'todo_to_date': todo_to_date or '',
            "linked_customer_companies_json": json.dumps(linked_customer_companies),
            'dashboard_open_count':     dashboard_open_count,
            'dashboard_total_cands':    total_cands,
            'dashboard_total_workers':  total_workers,
            'dashboard_active_workers': active_workers,
            'dashboard_terminated':     terminated_workers,
            'dashboard_rejected':       total_declined,
            'dashboard_nationalities':  nat_count,
            "pct_active": pct_active,
            "pct_term":   pct_term,
            "pct_rej":    pct_rej,
        }
        return render(request, self.template_name, context)


# Create your views here.
def company(requests):
    return render(requests, 'company/index.html')

def ensure_date(value):
    if value is None:
        return None
    if isinstance(value, datetime):
        return value.date()
    if isinstance(value, date):
        return value
    return None

class GenerateExcelView(View):
    def get(self, request, *args, **kwargs):
        user = request.user
        user_company = user.userprofile.company
        user_role = user.userprofile.role
        user_company_type = user_company.company_type

        # Get the query parameters
        entity_type = request.GET.get('type', 'candidates')
        company_id = request.GET.get('company_id')
        from_date = request.GET.get('from_date')
        to_date = request.GET.get('to_date')
        print(f"TESTING DATA: {entity_type}")

        # Determine the data to export based on the selected entity_type
        if entity_type == 'candidates':
            data = self.get_candidates_data(company_id, from_date, to_date, user)
        elif entity_type == 'jobads':
            data = self.get_jobads_data(company_id, from_date, to_date, user)
        elif entity_type == 'workers':
            data = self.get_workers_data(company_id, from_date, to_date, user)
        else:
            data = []

        # Create a DataFrame from the data
        df = pd.DataFrame(data)

        # Format date fields
        if 'created_date' in df.columns:
            df['created_date'] = pd.to_datetime(df['created_date'], dayfirst=True).dt.date
        if 'start_date' in df.columns:
            df['start_date'] = pd.to_datetime(df['start_date'], dayfirst=True).dt.date
        if 'end_date' in df.columns:
            df['end_date'] = pd.to_datetime(df['end_date'], dayfirst=True, errors='coerce').dt.date

        # Convert country codes to full country names
        if 'country' in df.columns:
            df['country'] = df['country'].apply(lambda code: str(Country(code)) if code else '')

        # Write DataFrame to a BytesIO stream using openpyxl
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Sheet1')
        # No need to call writer.save()
                
        # Load the workbook with openpyxl
        output.seek(0)
        wb = load_workbook(output)
        ws = wb.active  # Adjust the first sheet

        # Auto-adjust column widths
        for column_cells in ws.columns:
            col_letter = column_cells[0].column_letter
            max_length = 0
            for cell in column_cells:
                try:
                    cell_length = len(str(cell.value))
                    if cell_length > max_length:
                        max_length = cell_length
                except:
                    pass
            ws.column_dimensions[col_letter].width = max_length + 2

        # Save the workbook to a new BytesIO stream
        final_output = io.BytesIO()
        wb.save(final_output)
        final_output.seek(0)

        response = HttpResponse(
            final_output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename={entity_type}_data.xlsx'
        return response

    def get_candidates_data(self, company_id, from_date, to_date, user):
        candidates = Candidate.objects.all()
        
        if user:
            user_profile = user.userprofile
            # Check if the user's company is a customer.
            if user_profile.company.company_type == 'customer':
                if user_profile.role == 5:
                    # For proposers, get the structures they are linked to.
                    linked_structures = list(
                        UserProfileDepartments.objects.filter(user_ref=user_profile)
                        .values_list('structure_id', flat=True)
                    )
                    # Then, get all supplier/vendor companies linked to any of those structures.
                    allowed_company_ids = list(
                        CompanyPartner.objects.filter(structure__in=linked_structures)
                        .values_list('company_id', flat=True)
                        .distinct()
                    )
                elif user_profile.role == 1:
                    # For customer admins, get companies linked via CompanyPartner using the customer field.
                    allowed_company_ids = list(
                        CompanyPartner.objects.filter(customer=user_profile.company)
                        .values_list('company_id', flat=True)
                        .distinct()
                    )
                else:
                    # Fallback: only allow the user's own company.
                    allowed_company_ids = [user_profile.company.id]
                
                # If a company is selected, ensure it’s one of the allowed companies.
                if company_id:
                    candidates = candidates.filter(company_id=company_id, company_id__in=allowed_company_ids)
                else:
                    candidates = candidates.filter(company_id__in=allowed_company_ids)

            elif user_profile.company.company_type in ['supplier', 'vendor']:
                # For suppliers or vendors, only show candidates from their own company.
                candidates = candidates.filter(company_id=user_profile.company.id)
            else:
                # For users not in customer companies, if a company is selected then filter by that.
                if company_id:
                    candidates = candidates.filter(company_id=company_id)
        else:
            # If no user is passed, fall back to filtering by company_id (if provided).
            if company_id:
                candidates = candidates.filter(company_id=company_id)

        # Apply date filters.
        if from_date:
            try:
                from_date_parsed = datetime.strptime(from_date, '%Y-%m-%d')
                candidates = candidates.filter(created_date__gte=from_date_parsed)
            except ValueError:
                print(f"Invalid from_date: {from_date}")
        if to_date:
            try:
                to_date_parsed = datetime.strptime(to_date, '%Y-%m-%d')
                candidates = candidates.filter(created_date__lte=to_date_parsed)
            except ValueError:
                print(f"Invalid to_date: {to_date}")

        # Convert candidates to a list of dictionaries.
        candidate_list = list(candidates.values(
            'id', 'title', 'firstname', 'lastname', 'date_of_birth', 'status',
            'comment', 'company_id__name', 'hourly_rate', 'country', 'created_date'
        ))

        # Get all rejection reasons for these candidates.
        candidate_ids = [cand['id'] for cand in candidate_list]
        rejection_reasons = RejectedCandidateReason.objects.filter(applicant_id__in=candidate_ids)

        # Build a dictionary: candidate id -> rejection reason(s)
        rejection_dict = {}
        for rr in rejection_reasons:
            if rr.applicant_id in rejection_dict:
                rejection_dict[rr.applicant_id] += f", {str(rr.reason)}"
            else:
                rejection_dict[rr.applicant_id] = str(rr.reason)

        # Add rejection reason info to each candidate.
        for candidate in candidate_list:
            candidate['rejection_reason'] = rejection_dict.get(candidate['id'], '')

        return candidate_list

    def get_jobads_data(self, company_id, from_date, to_date, user):
        jobads = Jobad.objects.all()
        if user is None:
            if company_id:
                jobads = jobads.filter(company_id=company_id)
        else:
            user_profile = user.userprofile
            user_company = user_profile.company
            print("entering at", user_company.company_type)
            if user_company.company_type in ['supplier', 'vendor']:
                supplier_partner_structures = list(
                    CompanyPartner.objects.filter(company=user_company)
                    .values_list('structure', flat=True)
                )
                if company_id:
                    allowed_jobads_ids = JobadDepartment.objects.filter(
                        structure__in=supplier_partner_structures,
                        jobad__company_id=company_id
                    ).values_list('jobad_id', flat=True)
                    jobads = jobads.filter(id__in=allowed_jobads_ids)
                else:
                    allowed_jobads_ids = JobadDepartment.objects.filter(
                        structure__in=supplier_partner_structures
                    ).values_list('jobad_id', flat=True)
                    jobads = jobads.filter(id__in=allowed_jobads_ids)

            elif user_company.company_type == 'customer':
                if user_profile.role == 1:
                    # For a customer admin, just show jobads for their own company.
                    jobads = jobads.filter(company=user_company)
                elif user_profile.role == 5:
                    # For a proposer, only show jobads that are linked to one of the proposer’s departments.
                    linked_structures = list(
                        UserProfileDepartments.objects.filter(user_ref=user_profile)
                            .values_list('structure_id', flat=True)
                    )
                    print("Linked structures for proposer:", linked_structures)
                    allowed_jobad_ids = JobadDepartment.objects.filter(structure__in=linked_structures)\
                                        .values_list('jobad_id', flat=True)
                    print("Allowed Jobad IDs based on departments:", allowed_jobad_ids)
                    jobads = jobads.filter(id__in=allowed_jobad_ids)

        if from_date:
            try:
                from_date_parsed = datetime.strptime(from_date, '%Y-%m-%d')
                jobads = jobads.filter(created_date__gte=from_date_parsed)
            except ValueError:
                print(f"Invalid from_date: {from_date}")
        if to_date:
            try:
                to_date_parsed = datetime.strptime(to_date, '%Y-%m-%d')
                jobads = jobads.filter(created_date__lte=to_date_parsed)
            except ValueError:
                print(f"Invalid to_date: {to_date}")

        # Process and format your jobads data for Excel
        jobads_list = list(jobads.values(
            'id', 'position__title', 'company__name', 'company_value', 'department_value',
            'total', 'occupied', 'stage', 'created_date', 'start_date'
        ))

        for rec in jobads_list:
            if rec.get('total') is not None and rec.get('occupied') is not None:
                rec['Open positions'] = rec['total'] - rec['occupied']
            else:
                rec['Open positions'] = None

            start = ensure_date(rec.get('start_date'))
            created = ensure_date(rec.get('created_date'))
            if start and created:
                rec['Time (days)'] = (start - created).days
            else:
                rec['Time (days)'] = None

            if created:
                rec['created_date'] = created.strftime('%d/%m/%Y')
            if start:
                rec['start_date'] = start.strftime('%d/%m/%Y')

        return jobads_list

    def get_workers_data(self, company_id, from_date, to_date, user):
        # Start with all workers (with related candidate and jobad data)
        workers = Worker.objects.select_related('candidate', 'jobad').all()

        # Restrict according to user type:
        user_company = user.userprofile.company

        if user_company.company_type == 'supplier':
            # For supplier users, limit to workers whose candidate is from the supplier’s company.
            workers = workers.filter(candidate__company=user_company)
        elif user_company.company_type == 'customer':
            # For customer users, if the user is a proposer (role 5) we filter based on their linked departments.
            if user.userprofile.role == 5:
                # Get the structures (departments) the proposer is linked to.
                proposer_structure_ids = UserProfileDepartments.objects.filter(
                    user_ref=user.userprofile
                ).values_list('structure', flat=True)
                # Find jobad IDs from JobadDepartment that belong to any of those structures.
                jobad_ids = JobadDepartment.objects.filter(
                    structure__in=proposer_structure_ids
                ).values_list('jobad_id', flat=True)
                # Filter workers whose jobad is among these jobads.
                workers = workers.filter(jobad__id__in=jobad_ids)
            else:
                # For customer users that are not proposers (e.g. admins), use the standard filter.
                workers = workers.filter(jobad__company=user_company)

        if company_id:
            workers = workers.filter(candidate__company_id=company_id)

        # Apply date filters if provided.
        if from_date:
            try:
                from_date_parsed = datetime.strptime(from_date, '%Y-%m-%d')
                workers = workers.filter(start_date__gte=from_date_parsed)
            except ValueError:
                print(f"Invalid from_date: {from_date}")
        if to_date:
            try:
                to_date_parsed = datetime.strptime(to_date, '%Y-%m-%d')
                workers = workers.filter(end_date__lte=to_date_parsed)
            except ValueError:
                print(f"Invalid to_date: {to_date}")

        # Return a list of dictionaries containing the desired fields.
        return list(workers.values(
            'id',
            'candidate__title',
            'candidate__firstname',
            'candidate__lastname',
            'candidate__company__name',
            'jobad__position__title',
            'jobad__department_value',
            'start_date',
            'end_date',
            'stage',
            'candidate__hourly_rate',
            'candidate__date_of_birth'
        ))
    
class CustomPasswordResetView(PasswordResetView):
    def form_valid(self, form):
        """
        Override form_valid to send email using Celery.
        """
        email = form.cleaned_data['email']
        users = list(form.get_users(email))  # Get users with the provided email

        if not users:
            print(f"No user found with email: {email}")
        else:
            for user in users:
                # Generate reset link
                uid = urlsafe_base64_encode(force_bytes(user.pk))
                token = self.token_generator.make_token(user)
                reset_link = self.request.build_absolute_uri(
                    reverse('password_reset_confirm', kwargs={'uidb64': uid, 'token': token})
                )
                print(f"Password reset link for {user.email}: {reset_link}")

                # Send email using Celery
                send_async_email.delay(
                    recipient_email=user.email,
                    subject="Password Reset",
                    body=f"Reset your password using this link: {reset_link}",
                )

        return super().form_valid(form)
    
class CustomLoginView(LoginView):
    """
    Custom login view that forces a password reset if the user's profile
    indicates that a password change is required.
    """
    def form_valid(self, form):
        user = form.get_user()

        # Check if the user must change their password.
        if hasattr(user, 'userprofile') and user.userprofile.status == 'password-change':
            # Generate a password reset token.
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)
            reset_link = self.request.build_absolute_uri(
                reverse('password_reset_confirm', kwargs={'uidb64': uid, 'token': token})
            )
            # Send the password reset email using Celery.
            send_async_email.delay(
                recipient_email=user.email,
                subject="Password Reset",
                body=f"Reset your password using this link: {reset_link}",
            )
            # Log out the user so they must complete the reset process.
            auth_logout(self.request)

            # Inform the user via a message.
            messages.info(self.request, "Please check your email to reset your password.")
            return HttpResponseRedirect(reverse('password_reset_done'))

        # Otherwise, proceed normally.
        return super().form_valid(form)

UserModel = get_user_model()
INTERNAL_RESET_SESSION_TOKEN = "_password_reset_token"    
class CustomPasswordResetConfirmView(BasePasswordResetConfirmView):
    form_class = SetPasswordForm
    post_reset_login = False
    post_reset_login_backend = None
    reset_url_token = "set-password"
    success_url = reverse_lazy("password_reset_complete")
    template_name = "registration/password_reset_confirm.html"
    title = _("Enter new password")
    token_generator = default_token_generator

    @method_decorator(sensitive_post_parameters())
    @method_decorator(never_cache)
    @method_decorator(csrf_protect)
    def dispatch(self, *args, **kwargs):
        if "uidb64" not in kwargs or "token" not in kwargs:
            raise ImproperlyConfigured(
                "The URL path must contain 'uidb64' and 'token' parameters."
            )

        self.validlink = False
        self.user = self.get_user(kwargs["uidb64"])

        if self.user is not None:
            token = kwargs["token"]
            if token == self.reset_url_token:
                session_token = self.request.session.get(INTERNAL_RESET_SESSION_TOKEN)
                if self.token_generator.check_token(self.user, session_token):
                    self.validlink = True
                    return super().dispatch(*args, **kwargs)
            else:
                if self.token_generator.check_token(self.user, token):
                    self.request.session[INTERNAL_RESET_SESSION_TOKEN] = token
                    redirect_url = self.request.path.replace(token, self.reset_url_token)
                    return HttpResponseRedirect(redirect_url)

        return self.render_to_response(self.get_context_data())

    def get_user(self, uidb64):
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            user = UserModel._default_manager.get(pk=uid)
        except (TypeError, ValueError, OverflowError, UserModel.DoesNotExist, ValidationError):
            user = None
        return user

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.user
        return kwargs

    def form_valid(self, form):
        user = form.save()
        # Update user profile status from "password-change" to "created"
        if hasattr(user, 'userprofile') and user.userprofile.status == 'password-change':
            user.userprofile.status = 'created'
            user.userprofile.save()
        # Safely remove the session token
        self.request.session.pop(INTERNAL_RESET_SESSION_TOKEN, None)
        if self.post_reset_login:
            auth_login(self.request, user, self.post_reset_login_backend)
        return HttpResponseRedirect(self.get_success_url())

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.validlink:
            context["validlink"] = True
        else:
            context.update({
                "form": None,
                "title": _("Password reset unsuccessful"),
                "validlink": False,
            })
        return context
    
def impressum(request):
    return render(request, 'static_pages/impressum.html')

def nutzungsbestimmungen(request):
    return render(request, 'static_pages/nutzungsbestimmungen.html')

def datenschutz(request):
    return render(request, 'static_pages/datenschutz.html')

def is_lgi_or_global_admin(user):
    """LGI Admin if company_id == 61 and role in [1,5]; or global Admin (company_id == 1 and role == 1)."""
    if not user.is_authenticated:
        return False
    profile = user.userprofile
    if profile.company_id == 1 and profile.role == 1:
        return True
    if profile.company_id == 61 and profile.role in [1, 5]:
        return True
    return False

logger = logging.getLogger(__name__)

@login_required
@user_passes_test(is_lgi_or_global_admin)
def reports_view(request):
    user = request.user
    user_profile = user.userprofile
    # 1) Filter “open” LGI JobAds (company_id = 61, stage in open stages)
    open_stages = [JobadStage.OPEN_FOR_EVERYONE, JobadStage.OPEN_AGAIN]

    base_qs = Jobad.objects.filter(
        company_id=61,
        stage__in=open_stages
    ).select_related('location')

    # Step 2: If proposer, restrict by linked departments
    if user_profile.role == 5:  # proposer
        # 1. Obtener las claves (structure string) de los departamentos asignados al usuario
        linked_structure_keys = UserProfileDepartments.objects.filter(
            user_ref=user_profile
        ).values_list('structure__id', flat=True)

        # 2. Buscar los jobads que tienen conexiones en esas estructuras
        jobad_ids = JobadDepartment.objects.filter(
            structure__in=linked_structure_keys
        ).values_list('jobad_id', flat=True)

        # 3. Filtrar el queryset original con esos jobad_ids
        base_qs = base_qs.filter(id__in=jobad_ids)

    # Step 3: Location filter
    loc_filter = request.GET.get('location', '')
    if loc_filter:
        try:
            loc_ids = [int(x) for x in loc_filter.split(',') if x.strip()]
            base_qs = base_qs.filter(location_id__in=loc_ids)
        except ValueError:
            pass

    # Step 4: Position filter
    pos_filter = request.GET.get('position', '')
    if pos_filter:
        try:
            pos_ids = [int(x) for x in pos_filter.split(',') if x.strip()]
            base_qs = base_qs.filter(position_id__in=pos_ids)
        except ValueError:
            pass

    # Step 5: Annotate counts
    annotated_qs = base_qs.annotate(
        open_positions=F('total') - F('occupied'),
        approved_count=Count(
            'jobadapplication',
            filter=Q(jobadapplication__stage=JobadApplicationStage.APPROVED),
            distinct=True,
        ),
        interview_count=Count(
            'jobadapplication',
            filter=Q(jobadapplication__stage=JobadApplicationStage.INTERVIEW),
            distinct=True,
        ),
        interview_completed_count=Count(
            'jobadapplication',
            filter=Q(jobadapplication__stage=JobadApplicationStage.INTERVIEW_COMPLETED),
            distinct=True,
        ),
        candidate_selection_count=Count(
            'jobadapplication',
            filter=Q(jobadapplication__stage=JobadApplicationStage.CANDIDATE_SELECTION),
            distinct=True,
        ),
        tentatively_occupied_count=Count(
            'jobadapplication',
            filter=Q(jobadapplication__stage=JobadApplicationStage.TENTATIVELY_OCCUPIED),
            distinct=True,
        ),
    ).order_by('-id')

    # 3) Build arrays for the “cap at 20 / ∞” diagram:
    labels = []
    actual_positions = []
    capped_positions = []
    infinite_mask = []
    approved_counts = []
    interview_counts = []
    interview_done_counts = []
    selection_counts = []
    tentatively_counts = []
    total_candidates_by_job = []
    candidates = []

    for ja in annotated_qs:
        labels.append(ja.id)
        op = ja.open_positions
        actual_positions.append(op)

        if op > 20:
            capped_positions.append(0)
            infinite_mask.append(20)
        else:
            capped_positions.append(op)
            infinite_mask.append(0)

        # collect per‐stage values
        approved_counts.append(ja.approved_count)
        interview_counts.append(ja.interview_count)
        interview_done_counts.append(ja.interview_completed_count)
        selection_counts.append(ja.candidate_selection_count)
        tentatively_counts.append(ja.tentatively_occupied_count)

        # total still for old tooltip if you like
        total_candidates_by_job.append(
            ja.approved_count
          + ja.interview_count
          + ja.interview_completed_count
          + ja.candidate_selection_count
          + ja.tentatively_occupied_count
        )

    total_count = len(labels)
    all_locations = CompanyLocation.objects.filter(company_id=61)
    all_positions = CompanyPosition.objects.filter(company_id=61)

    # 4) Prepare context for the template:
    context = {
        'open_jobads': annotated_qs,
        'labels': labels,
        'actual_positions': actual_positions,
        'capped_positions': capped_positions,
        'infinite_mask': infinite_mask,
        'approved_counts': approved_counts,
        'interview_counts': interview_counts,
        'interview_done_counts': interview_done_counts,
        'selection_counts': selection_counts,
        'tentatively_counts': tentatively_counts,
        'candidates': total_candidates_by_job,
        'total_count': total_count,
        'locations': all_locations,
        'positions': all_positions,
        'selected_positions': pos_filter,
        'selected_locations': loc_filter,
    }

    # 5) Logging for debugging
    logger.info(f"reports_view: {total_count} open LGI JobAds plotted (capped at 20)")
    logger.debug(f"First 5 entries:\n{list(zip(labels, actual_positions, capped_positions, infinite_mask, candidates))[:5]}")

    return render(request, 'reports/reports.html', context)

LGI_COMPANY_ID = 61
ALLOWED_STAGES = [
    'open_again',
    'open_for_everyone',
    'completed',
    'filled',
]

allowed_stages = [
    JobadApplicationStage.APPROVED,
    JobadApplicationStage.INTERVIEW,
    JobadApplicationStage.INTERVIEW_COMPLETED,
    JobadApplicationStage.CANDIDATE_SELECTION,
    JobadApplicationStage.TENTATIVELY_OCCUPIED,
]

@login_required
@user_passes_test(is_lgi_or_global_admin)
def reaction_time_report(request):
    # 1) Filter by company_id (LGI) and the four allowed stages
    qs = Jobad.objects.filter(
        company_id=LGI_COMPANY_ID,
        stage__in=ALLOWED_STAGES
    )
    # Apply proposer filtering if necessary
    if request.user.userprofile.role == 5:  # proposer
        linked_structure_ids = UserProfileDepartments.objects.filter(
            user_ref=request.user.userprofile
        ).values_list('structure__id', flat=True)

        jobad_ids = JobadDepartment.objects.filter(
            structure__in=linked_structure_ids
        ).values_list('jobad_id', flat=True)

        qs = qs.filter(id__in=jobad_ids)
        
    # 2) Truncate JobAd.created_date to a date (drop the time portion)
    qs = qs.annotate(
        created_date_only=TruncDate('created_date')
    )

    # 3) Annotate “first_app_date” = MIN(TruncDate(application.created_date))
    #    across all JobadApplication rows linked to this JobAd.
    qs = qs.annotate(
        first_app_date=Min(
            TruncDate('jobadapplication__created_date'),
            filter=Q(jobadapplication__created_date__isnull=False)
        )
    )

    # 4) Compute “days_to_first_app” = (first_app_date − created_date_only)
    qs = qs.annotate(
        days_to_first_app=ExpressionWrapper(
            F('first_app_date') - F('created_date_only'),
            output_field=DurationField()
        )
    )

    # 5) Compute “days_to_start” = (start_date − created_date_only)
    qs = qs.annotate(
        days_to_start=ExpressionWrapper(
            F('start_date') - F('created_date_only'),
            output_field=DurationField()
        )
    )

    # 6) Exclude any JobAds missing either start_date or first_app_date
    qs = qs.filter(
        start_date__isnull=False,
        first_app_date__isnull=False
    )

    context = {
        'jobads': qs
    }
    return render(request, 'reports/reaction_time.html', context)

@login_required
@user_passes_test(is_lgi_or_global_admin)
def supplier_evaluation(request):
    # 1) get supplier/company IDs based on role
    if request.user.userprofile.role == 5:  # supplier proposer
        linked_structure_ids = UserProfileDepartments.objects.filter(
            user_ref=request.user.userprofile
        ).values_list('structure__id', flat=True)

        supplier_ids = (
            CompanyPartner.objects
            .filter(
                customer_id=LGI_COMPANY_ID,
                structure__in=linked_structure_ids
            )
            .values_list('company_id', flat=True)
            .distinct()
        )
    else:
        supplier_ids = (
            CompanyPartner.objects
            .filter(
                customer_id=LGI_COMPANY_ID,
                company__company_type__in=['supplier', 'vendor']
            )
            .values_list('company_id', flat=True)
            .distinct()
        )

    # 2) fetch exactly one Company for each
    suppliers = Company.objects.filter(id__in=supplier_ids).order_by('name')

    # — 2) allow a ?supplier=ID GET param
    supplier_id = request.GET.get('supplier', '')
    if supplier_id:
        try:
            supplier = Company.objects.get(pk=int(supplier_id))
        except Company.DoesNotExist:
            supplier = None
    else:
        supplier = None

    # — 3) base candidate & worker QS filtered by that supplier (or ALL of them)
    if supplier:
        cand_qs   = Candidate.objects.filter(company=supplier)
        worker_qs = Worker.objects.filter(candidate__company=supplier)
    else:
        cand_qs   = Candidate.objects.filter(company__in=suppliers)
        worker_qs = Worker.objects.filter(candidate__company__in=suppliers)

    # — 4) compute the five summary metrics
    total_cands      = cand_qs.count()
    total_workers    = worker_qs.count()
    active_workers   = worker_qs.filter(stage__in=[WorkerStage.APPROVED, WorkerStage.INTERNALLY_DEPLOYED]).count()
    terminated_workers = worker_qs.filter(
        stage__in=[WorkerStage.DEPLOYMENT_FINISHED, WorkerStage.DEPLOYMENT_DISCONTINUED]
    ).count()
    total_declined   = RejectedCandidateReason.objects.filter(
        applicant__company__in=[supplier] if supplier else suppliers
    ).count()

    cand_reason_qs = (
        RejectedCandidateReason.objects
        .filter(applicant__company__in=[supplier] if supplier else suppliers)
        .values('reason__text')
        .annotate(count=Count('*'))
        .order_by('-count')
    )
    cand_reason_labels = [r['reason__text'] for r in cand_reason_qs]
    cand_reason_values = [r['count']           for r in cand_reason_qs]

    # — 5b) worker‐side rejection reasons
    worker_reason_qs = (
        WorkerRejectionReason.objects
        .filter(worker__candidate__company__in=[supplier] if supplier else suppliers)
        .values('reason__text')
        .annotate(count=Count('*'))
        .order_by('-count')
    )
    worker_reason_labels = [r['reason__text'] for r in worker_reason_qs]
    worker_reason_values = [r['count']           for r in worker_reason_qs]

    # — 6) nationality breakdown
    nat_counts = (
        cand_qs
        .values('country')
        .annotate(count=Count('*'))
        .order_by('-count')
    )
    # instead of using the raw code, look up the full name:
    nat_labels = [
        countries.name(code) if code else 'Unknown'
        for code in (n['country'] for n in nat_counts)
    ]
    nat_values = [n['count'] for n in nat_counts]

    # — 7) prepare Reaction-Time “days to …” charts exactly as before
    qs = Jobad.objects.filter(
        company_id=LGI_COMPANY_ID,
        stage__in=ALLOWED_STAGES
    ).annotate(
        created_date_only=TruncDate('created_date'),
        first_app_date=Min(
            TruncDate('jobadapplication__created_date'),
            filter=Q(jobadapplication__created_date__isnull=False)
        ),
        days_to_first_app=ExpressionWrapper(
            F('first_app_date') - F('created_date_only'),
            output_field=DurationField()
        ),
        days_to_start=ExpressionWrapper(
            F('start_date') - F('created_date_only'),
            output_field=DurationField()
        )
    ).filter(
        start_date__isnull=False,
        first_app_date__isnull=False
    )

    jr_labels = [f"{ja.id} – {ja.position.title}" for ja in qs]
    jr_start  = [ja.days_to_start.days     for ja in qs]
    jr_first  = [ja.days_to_first_app.days for ja in qs]

    # 2) Truncate JobAd.created_date to a date (drop the time portion)
    qs = qs.annotate(
        created_date_only=TruncDate('created_date')
    )

    # 3) Annotate “first_app_date” = MIN(TruncDate(application.created_date))
    #    across all JobadApplication rows linked to this JobAd.
    qs = qs.annotate(
        first_app_date=Min(
            TruncDate('jobadapplication__created_date'),
            filter=Q(jobadapplication__created_date__isnull=False)
        )
    )

    # 4) Compute “days_to_first_app” = (first_app_date − created_date_only)
    qs = qs.annotate(
        days_to_first_app=ExpressionWrapper(
            F('first_app_date') - F('created_date_only'),
            output_field=DurationField()
        )
    )

    # 5) Compute “days_to_start” = (start_date − created_date_only)
    qs = qs.annotate(
        days_to_start=ExpressionWrapper(
            F('start_date') - F('created_date_only'),
            output_field=DurationField()
        )
    )

    # 6) Exclude any JobAds missing either start_date or first_app_date
    qs = qs.filter(
        start_date__isnull=False,
        first_app_date__isnull=False
    )

    jobad_labels = [f"{ja.id} – {ja.position.title}" for ja in qs]
    start_days    = [ja.days_to_start.days      for ja in qs]
    first_days    = [ja.days_to_first_app.days  for ja in qs]

    context = {
        'jobads': qs,
        'suppliers': suppliers,
        'selected_supplier': supplier.id if supplier else '',
        'selected_supplier_obj': supplier, 
        'total_cands': total_cands,
        'total_workers': total_workers,
        'active_workers': active_workers,
        'terminated_workers': terminated_workers,
        'total_declined': total_declined,
        'cand_reason_labels_json':   json.dumps(cand_reason_labels),
        'cand_reason_values_json':   json.dumps(cand_reason_values),
        'worker_reason_labels_json': json.dumps(worker_reason_labels),
        'worker_reason_values_json': json.dumps(worker_reason_values),
        'has_candidate_rejections': bool(cand_reason_qs),
        'has_worker_rejections':    bool(worker_reason_qs),
        'nat_counts': list(nat_counts),
        'nat_labels_json': json.dumps(nat_labels),
        'nat_values_json': json.dumps(nat_values),
        'jobad_labels_json': json.dumps(jobad_labels),
        'start_days_json':    json.dumps(start_days),
        'first_days_json':    json.dumps(first_days),
    }
    return render(request, 'reports/supplier_evaluation.html', context)

@login_required
@user_passes_test(is_lgi_or_global_admin)
def export_reports_excel(request):
    # 1) the same base qs + annotations as reports_view
    open_stages = [JobadStage.OPEN_FOR_EVERYONE, JobadStage.OPEN_AGAIN]
    qs = (
        Jobad.objects
        .filter(company_id=LGI_COMPANY_ID, stage__in=open_stages)
        .select_related('location', 'position')
        .annotate(
            open_positions=F('total') - F('occupied'),
            approved_count=Count(
                'jobadapplication',
                filter=Q(jobadapplication__stage=JobadApplicationStage.APPROVED),
                distinct=True,
            ),
            interview_count=Count(
                'jobadapplication',
                filter=Q(jobadapplication__stage=JobadApplicationStage.INTERVIEW),
                distinct=True,
            ),
            interview_completed_count=Count(
                'jobadapplication',
                filter=Q(jobadapplication__stage=JobadApplicationStage.INTERVIEW_COMPLETED),
                distinct=True,
            ),
            candidate_selection_count=Count(
                'jobadapplication',
                filter=Q(jobadapplication__stage=JobadApplicationStage.CANDIDATE_SELECTION),
                distinct=True,
            ),
            tentatively_occupied_count=Count(
                'jobadapplication',
                filter=Q(jobadapplication__stage=JobadApplicationStage.TENTATIVELY_OCCUPIED),
                distinct=True,
            ),
        )
        .order_by('id')
    )
    qs = _restrict_to_user_departments(qs, request.user)

    # 2) Re-apply the location filter
    loc_filter = request.GET.get('location', '')
    if loc_filter:
        try:
            loc_ids = [int(x) for x in loc_filter.split(',') if x.strip()]
            qs = qs.filter(location_id__in=loc_ids)
        except ValueError:
            pass

    # 3) Re-apply the position filter
    pos_filter = request.GET.get('position', '')
    if pos_filter:
        try:
            pos_ids = [int(x) for x in pos_filter.split(',') if x.strip()]
            qs = qs.filter(position_id__in=pos_ids)
        except ValueError:
            pass

    # 4) Build workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Offene JobAd Report"

    # 3) Header
    ws.append([
        "JobAd ID", "Position", "Standort", "Abteilung", "Kostenstelle",
        "Offene Positionen", "Besetzte",
        "Offene Kandidaten", "Interview angefragt", "Interview bestätigt",
        "Kandidat bestätigen", "Vorläufig besetzt",
    ])

    # 4) Rows
    for ja in qs:
        ws.append([
            ja.id,
            ja.position.title,
            str(ja.location),
            ja.department_value,
            ja.cost_department,
            ja.open_positions if ja.open_positions <= 100 else "Unbegrenzt",
            ja.occupied,
            ja.approved_count,
            ja.interview_count,
            ja.interview_completed_count,
            ja.candidate_selection_count,
            ja.tentatively_occupied_count,
        ])

    # 5) Stream it
    response = HttpResponse(
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    response["Content-Disposition"] = 'attachment; filename="offene_jobad_report.xlsx"'
    wb.save(response)
    return response

@login_required
@user_passes_test(is_lgi_or_global_admin)
def export_reaction_time_excel(request):
    # build the same qs as in reaction_time_report
    qs = (
        Jobad.objects
        .filter(company_id=LGI_COMPANY_ID, stage__in=ALLOWED_STAGES)
        .select_related('position')
        .annotate(created_date_only=TruncDate('created_date'))
        .annotate(
            first_app_date=Min(
                TruncDate('jobadapplication__created_date'),
                filter=Q(jobadapplication__created_date__isnull=False)
            )
        )
        .annotate(
            days_to_first_app=ExpressionWrapper(
                F('first_app_date') - F('created_date_only'),
                output_field=DurationField()
            )
        )
        .annotate(
            days_to_start=ExpressionWrapper(
                F('start_date') - F('created_date_only'),
                output_field=DurationField()
            )
        )
        .filter(start_date__isnull=False, first_app_date__isnull=False)
    )

    # 2) restrict to proposer’s allowed departments
    qs = _restrict_to_user_departments(qs, request.user)
    
    # create Excel workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Reaction Times"

    # header row
    ws.append([
        "JobAd ID",
        "Position",
        "Tage bis Jobbeginn",
        "Tage bis erste Bewerbung",
    ])

    # data rows
    for ja in qs:
        ws.append([
            ja.id,
            ja.position.title,
            ja.days_to_start.days,
            ja.days_to_first_app.days,
        ])

    # send it down
    response = HttpResponse(
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    response["Content-Disposition"] = 'attachment; filename="reaction_times.xlsx"'
    wb.save(response)
    return response

@login_required
@user_passes_test(is_lgi_or_global_admin)
def export_supplier_excel(request):
    # build the same qs as in reaction_time_report
    qs = (
        Jobad.objects
        .filter(company_id=LGI_COMPANY_ID, stage__in=ALLOWED_STAGES)
        .annotate(created_date_only=TruncDate('created_date'))
        .annotate(
            first_app_date=Min(
                TruncDate('jobadapplication__created_date'),
                filter=Q(jobadapplication__created_date__isnull=False)
            )
        )
        .annotate(
            days_to_first_app=ExpressionWrapper(
                F('first_app_date') - F('created_date_only'),
                output_field=DurationField()
            )
        )
        .annotate(
            days_to_start=ExpressionWrapper(
                F('start_date') - F('created_date_only'),
                output_field=DurationField()
            )
        )
        .filter(start_date__isnull=False, first_app_date__isnull=False)
    )

    # create Excel workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Reaction Times"

    # header row
    ws.append([
        "JobAd ID",
        "Position",
        "Tage bis Jobbeginn",
        "Tage bis erste Bewerbung",
    ])

    # data rows
    for ja in qs:
        ws.append([
            ja.id,
            ja.position.title,
            ja.days_to_start.days,
            ja.days_to_first_app.days,
        ])

    # send it down
    response = HttpResponse(
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )
    response["Content-Disposition"] = 'attachment; filename="reaction_times.xlsx"'
    wb.save(response)
    return response