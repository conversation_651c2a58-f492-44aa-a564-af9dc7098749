from django.shortcuts import redirect
from django.conf import settings
import re

EXEMPT_URLS = [
    '/login/',
    '/password_reset/',
    '/password_reset/done/',
    '/reset/done/',
    '/impressum/',
    '/nutzungsbestimmungen/',
    '/datenschutz/',
]
EXEMPT_URL_PATTERNS = [
    re.compile(r'^/reset/[\w\-]+/[\w\-]+/$'),  # Matches /reset/<uidb64>/<token>/
]


class LoginRequiredMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Allow exempt URLs
        if request.path in EXEMPT_URLS or any(pattern.match(request.path) for pattern in EXEMPT_URL_PATTERNS):
            return self.get_response(request)

        # Redirect if unauthenticated
        if not request.user.is_authenticated:
            return redirect(settings.LOGIN_URL)

        return self.get_response(request)
