{% load i18n %}
<div x-data="{
    step: 1, 
    showPassword: false, 
    companySelected: '', 
    companyType: '', 
    password: '', 
    confirmPassword: '', 
    passwordsMatch: true, 
    submitAttempted: false,
    userGroups: [], 
    companyUserGroups: {
        global: ['Personnel Officer', 'Admin'],
        customer: ['Super approver', 'Proposer', 'Personnel officer', 'Admin'],
        supplier: ['Recruiter Lieferant', 'Personnel Officer', 'Admin'],
        vendor: ['Recruiter Master Vendor', 'Personnel Officer', 'Admin']
    },
    initSelect2() { 
        const vm = this;
        $('.company-select').select2({
            placeholder: 'Select a company',
            allowClear: true,
            width: '100%'
        }).on('change', function () {
            vm.companySelected = $(this).val();
            vm.calculateCompanyType();
        });
    }, 
    calculateCompanyType() {
        console.log('companyselected', this.companySelected)
        if (this.companySelected == '2' || this.companySelected == '61') {
            this.companyType = 'customer';
        } else if (this.companySelected == '4') {
            this.companyType = 'vendor';
        } else {
            this.companyType = 'supplier';
        }
        this.userGroups = this.companyUserGroups[this.companyType];
    }, 
    checkPasswords() {
        this.passwordsMatch = this.password === this.confirmPassword;
    },
    goToStep2() {
        this.submitAttempted = true;
        this.checkPasswords();
        if (this.passwordsMatch) {
            // Map user group to role ID
            const roleMap = {
                'Admin': 1,
                'Personnel Officer': 2,
                'Recruiter Master Vendor': 3,
                'Recruiter Lieferant': 4,
                'Proposer': 5,
                'Job ad approver': 6,
                'Worker approver': 7,
                'Super approver': 8
            };

            const selectedUserGroup = document.getElementById('usergroup').value;
            const roleId = roleMap[selectedUserGroup];

            // Set the role ID in a hidden input
            document.getElementById('role').value = roleId;

            console.log('Role ID set in step 1:', roleId); // Debugging to check the role ID

            this.step = 2; // Move to step 2
        }
    },
    submitForm() {
        this.submitAttempted = true;
        this.checkPasswords();
        if (this.step === 2 && this.passwordsMatch) {
            this.$refs.registrationForm.submit();
        }
    }
}" 
x-init="initSelect2()" 
x-show="true" 
@keydown.escape.window="open = false"
class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-lg relative">
        <button class="absolute top-4 right-4 text-gray-500 hover:text-gray-800" @click="open = false">
            <i class="fas fa-times"></i>
        </button>
        <h2 class="text-xl font-bold mb-4">{% trans "User registration" %}</h2>
        <form x-ref="registrationForm" method="POST" action="{% url 'create_user_profile' %}">
            {% csrf_token %}
            <!-- Step 1 -->
            <div x-show="step === 1">
                <div class="mb-4">
                    <label for="active" class="block text-sm font-medium text-gray-700">{% trans "Active account" %}</label>
                    <select id="active" name="active" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                        <option value="True">{% trans "Active" %}</option>
                        <option value="False">{% trans "Inactive" %}</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="company_id" class="block text-sm font-medium text-gray-700">{% trans "Company" %}</label>
                    <select id="company_id" name="company" class="company-select mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" style="width: 100%;" required>
                        <option value="">{% trans "Select a company..." %}</option>
                        {% for company in companies %}
                            <option value="{{ company.id }}">{{ company.name }}</option>
                        {% endfor %}
                    </select>
                </div>               
                
                <div class="mb-4">
                    <label for="usergroup" class="block text-sm font-medium text-gray-700">{% trans "Usergroup" %}</label>
                    <select 
                        id="usergroup" 
                        name="role" 
                        class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500 p-2"
                        required
                    >
                        <template x-for="group in userGroups" :key="group">
                            <option x-text="group"></option>
                        </template>
                    </select>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="firstname" class="block text-sm font-medium text-gray-700">{% trans "First name" %}</label>
                        <input id="firstname" name="firstname" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                    </div>
                    <div>
                        <label for="lastname" class="block text-sm font-medium text-gray-700">{% trans "Last name" %}</label>
                        <input id="lastname" name="lastname" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700">{% trans "Username" %}</label>
                        <input id="username" name="username" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                    </div>
                    <div>
                        <label for="initial" class="block text-sm font-medium text-gray-700">{% trans "Initial" %}</label>
                        <input id="initial" name="initial" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700">{% trans "E-Mail" %}</label>
                    <input id="email" name="email" type="email" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500" required>
                </div>

                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700">{% trans "Password" %}</label>
                    <input id="password" name="password" type="password" x-model="password" @input="checkPasswords" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                </div>
                
                <div class="mb-4">
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">{% trans "Repeat Password" %}</label>
                    <input id="confirm_password" type="password" x-model="confirmPassword" @input="checkPasswords" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                </div>
                
                <p x-show="submitAttempted && !passwordsMatch" class="text-red-500 text-sm mb-4">{% trans "Passwords do not match!" %}</p>

                <div class="flex justify-between items-center">
                    <button type="button" @click="goToStep2()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white px-4 py-2 rounded-md">{% trans "Continue" %}</button>
                    <button type="button" @click="open = false" class="bg-gray-500 hover:bg-gray-700 py-2 px-4 rounded-md text-white">{% trans "Cancel" %}</button>
                </div>
            </div>
            <input type="hidden" id="role" name="role">
            <!-- Step 2 -->
            <div x-show="step === 2">
                <div class="mb-4">
                    <label for="street" class="block text-sm font-medium text-gray-700">{% trans "Street" %}</label>
                    <input id="street" name="street" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                </div>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="postal_code" class="block text-sm font-medium text-gray-700">{% trans "Postal code" %}</label>
                        <input id="postal_code" name="post_code" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                    </div>
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700">{% trans "City" %}</label>
                        <input id="city" name="city" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                    </div>
                </div>
                <div class="mb-4">
                    <label for="country" class="block text-sm font-medium text-gray-700">{% trans "Origin" %}</label>
                    {{ profile_form.country }}
                </div>            
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <label for="telephone" class="block text-sm font-medium text-gray-700">{% trans "Telephone number" %}</label>
                        <input id="telephone" name="telephone_number" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                    </div>
                    <div>
                        <label for="mobile" class="block text-sm font-medium text-gray-700">{% trans "Mobile number" %}</label>
                        <input id="mobile" name="mobile_number" type="text" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-accent-500 focus:border-accent-500">
                    </div>
                </div>
    
                <div class="flex justify-between items-center">
                    <button @click="step = 1" type="button" class="text-gray-700">{% trans "Back" %}</button>
                    <button @click="submitForm()" type="submit" class="bg-accent-500 text-white px-4 py-2 rounded-md">{% trans "Save" %}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script>
    // Make sure Select2 is initialized every time the modal is shown
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalSelect2', () => ({
            init() {
                // Initialize Select2 on the company select element
                $('.company-select').select2({
                    placeholder: 'Select a company...',
                    allowClear: true,
                    width: '100%',
                    dropdownCssClass: 'rounded-md shadow-lg border-gray-300 focus:ring-accent-500 focus:border-accent-500',
                    containerCssClass: 'rounded-md border-gray-300 focus:ring-accent-500 focus:border-accent-500 p-2',
                });
            }
        }))
    });
</script>


