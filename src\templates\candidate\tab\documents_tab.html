{% load static %}
{% load i18n %}
<div x-data="{ openModal: false, dropzoneActive: false, deleteModalOpen: false, documentToDelete: null, selectedType: 'CV' }" class="p-8 bg-white">
    <div class="flex justify-end items-center mb-6">
        {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 4 or request.user.userprofile.role == 3 %}
            <button @click="openModal = true" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded">
                {% trans "Upload Document" %}
            </button>
        {% endif %}
    </div>
    <!-- Upload Modal -->
    <div x-show="openModal" @click.away="openModal = false" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
        <div class="bg-white w-full max-w-md mx-auto rounded-lg shadow-xl transform transition-all" @click.stop>
            <div class="bg-accent-500 text-white px-6 py-4 rounded-t-lg">
                <h3 class="text-lg font-bold">{% trans "Upload Document" %}</h3>
            </div>
            <div class="p-6">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <!-- Document Type -->
                    <label for="document_type" class="block text-sm font-semibold text-gray-700 mb-1">
                        {% trans "Document Type" %}
                    </label>
                    <select name="document_type" id="document_type" class="border rounded px-2 py-1 mb-4 w-full focus:outline-none focus:ring focus:border-accent-500" required x-model="selectedType">
                        <option value="CV">{% trans "CV" %}</option>
                        <option value="work_permit">{% trans "Work Permit" %}</option>
                        <option value="fuhrerschein">{% trans "Car License" %}</option>
                        <option value="staplerschein">{% trans "Forklift License" %}</option>
                    </select>
                    <!-- File Input -->
                    <label for="document" class="block text-sm font-semibold text-gray-700 mb-1">
                        {% trans "Choose File" %}
                    </label>
                    <input type="file" name="document" id="document" class="border rounded px-2 py-1 mb-4 w-full focus:outline-none focus:ring focus:border-accent-500" required>
                    <!-- Expiration Date (conditional) -->
                    <div x-show="selectedType !== 'CV' && selectedType !== 'staplerschein'" x-transition>
                        <label for="expiration_date" class="block text-sm font-semibold text-gray-700 mb-1">
                        {% trans "Expiration Date" %}
                        </label>
                        <input type="date" name="expiration_date" id="expiration_date" class="border rounded px-2 py-1 mb-4 w-full focus:outline-none focus:ring focus:border-accent-500" :required="selectedType !== 'CV' && selectedType !== 'staplerschein'">
                    </div>
                    <!-- Modal Footer (Buttons) -->
                    <div class="flex justify-end">
                        <button type="submit" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mr-2">
                        {% trans "Save Document" %}
                        </button>
                        <button type="button" @click="openModal = false" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                        {% trans "Cancel" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- drag & drop -->
    <div
        x-show="dropzoneActive"
        @dragover.prevent="dropzoneActive = true"
        @dragleave.prevent="dropzoneActive = false"
        @drop.prevent="dropzoneActive = false; handleDrop($event)"
        class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
        <div class="bg-white p-6 rounded-lg text-center">
            <p>{% trans "Drop your files here" %}</p>
        </div>
    </div>
    <div class="relative" @dragover.prevent="dropzoneActive = true" @drop.prevent="handleDrop($event)">
        <table class="min-w-full divide-y divide-gray-200 text-sm">
            <thead class="bg-accent-500 text-white font-bold">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "File Name" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Uploaded By" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Uploaded On" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Expiration Date" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider"></th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for document in documents %}
                    <tr class="{% cycle 'bg-white' 'bg-gray-100' %}">
                        <td class="px-6 py-4 whitespace-nowrap text-lg flex items-center">
                            {% if document.document_type == 'CV' %}
                                <img src="{% static 'images/icons/file_icon.png' %}" alt="CV Icon" class="w-5 h-5 mr-2">
                            {% elif document.document_type == 'work_permit' %}
                                <img src="{% static 'images/icons/workpermit.png' %}" alt="Work Permit Icon" class="w-5 h-5 mr-2">
                            {% elif document.document_type == 'fuhrerschein' %}
                                <img src="{% static 'images/icons/car.png' %}" alt="Fuhrerschein Icon" class="w-5 h-5 mr-2">
                            {% elif document.document_type == 'staplerschein' %}
                                <img src="{% static 'images/icons/forklift.png' %}" alt="Staplerschein Icon" class="w-5 h-5 mr-2">
                            {% endif %}
                            {{ document.get_document_type_display }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-lg">{{ document.uploaded_by }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-lg">{{ document.uploaded_on }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-lg">{{ document.expiration_date }}</td>
                        <!-- New column for actions -->
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{% url 'document_download' 'candidate' document.id %}" class="text-blue-500 mr-2" title="{% trans 'Download' %}">
                                <i class="fas fa-download fa-lg"></i>
                            </a>
                            {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 4 or request.user.userprofile.role == 3 %}
                                <a href="{% url 'candidate-document-delete' document.id %}" class="text-red-500" title="{% trans 'Delete' %}">
                                    <i class="fas fa-trash fa-lg"></i>
                                </a>
                            {% endif %}
                        </td>
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-10">
                            <div class="flex flex-col items-center justify-center">
                                <img src="{% static 'images/documentsic1.png' %}" alt="No documents" class="mt-4 h-16 w-16">
                                <p class="mt-2 text-gray-600">{% trans "No documents available." %}</p>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>            
        </table>
    </div>
    <!-- Delete Confirmation Modal -->
    <div x-show="deleteModalOpen" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50" x-cloak>
        <div class="bg-white p-6 rounded-lg" @click.stop>
            <h3 class="text-lg font-semibold mb-4">{% trans "Confirm Deletion" %}</h3>
            <p class="mb-4">{% trans "Are you sure you want to delete this document?" %}</p>
            <div class="flex justify-end">
                <button type="button" 
                    @click="
                        fetch(`/candidate/document/delete/${documentToDelete}/`, {
                            method: 'POST',
                            headers: {
                                'X-CSRFToken': '{{ csrf_token }}'
                            }
                        }).then(response => { 
                            if(response.ok){ location.reload(); } 
                            else { alert('Error deleting document.'); } 
                        });
                        deleteModalOpen = false;
                    " 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded mr-2">
                    {% trans "Confirm" %}
                </button>
                <button type="button" @click="deleteModalOpen = false" class="bg-gray-500 text-white font-bold py-2 px-4 rounded">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>    
</div>

<script>
    function handleDrop(event) {
        event.preventDefault();
        let files = event.dataTransfer.files;
        if (files.length) {
            let formData = new FormData();
            formData.append('document', files[0]);
            let expiration_date = prompt("Please enter the expiration date (YYYY-MM-DD):");
            if (expiration_date) {
                formData.append('expiration_date', expiration_date);

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Error uploading file.');
                    }
                }).catch(error => {
                    console.error('Error:', error);
                });
            }
        }
    }

    document.addEventListener('dragover', function(event) {
        event.preventDefault();
    });

    document.addEventListener('drop', function(event) {
        //prevent double handling
        event.preventDefault();
    });
</script>
