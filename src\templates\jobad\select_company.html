<style>
    /* Same table styling as supplier.html */
    table {
        width: 100%;
        border-collapse: collapse;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 16px;
    }

    th, td {
        text-align: center;
        padding: 8px;
        border-bottom: 1px solid #ddd;
        vertical-align: middle;
    }

    th {
        background-color: #001d67;
        color: white;
        font-size: 0.875rem;
        text-transform: uppercase;
    }

    td {
        font-size: 0.875rem;
        color: #333;
        height: 100%;
    }

    .node-item {
        display: block;
        padding: 8px 12px;
        background-color: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        font-size: 0.875rem;
        text-align: left;
        margin-bottom: 4px;
    }

    .node-item:hover {
        background-color: #001d67;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .selected-node {
        background-color: #374151;
        color: white;
    }

    td:first-child {
        width: 25%;
    }

    td:nth-child(2), td:nth-child(3), td:nth-child(4) {
        width: 25%;
    }

    .company-icons {
        display: flex;
        gap: 10px;
        margin-left: auto;
    }

    .company-icon {
        cursor: pointer;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.875rem;
        color: #4a5568;
        transition: color 0.3s ease;
        padding: 8px;
        border-radius: 8px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .company-icon.active {
        border-color: #001d67;
        background-color: #e0e8f9;
        color: #001d67;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .company-image {
        width: 35px;
        height: 35px;
        object-fit: cover;
    }
</style>

{% extends '_base_frontend.html' %}
{% load static %}
{% load i18n %}
{% block title %}Create Jobad{% endblock %}

{% block content %}
<div class="container mx-auto p-8 bg-gray-100 rounded-lg">
    <div class="flex items-center justify-between mb-6">
        <a href="/jobads/" class="back-arrow text-xl cursor-pointer text-black flex align-center mr-2 hover:text-secondary-500">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h2 class="text-start text-xl font-bold mr-4">{% trans "Create Jobad" %}</h2>
        <!-- Company Selection Icons -->
        <div class="company-icons flex">
            {% for company in companies %}
                <div class="company-icon {% if forloop.first %}active{% endif %}" data-company-id="{{ company.id }}" onclick="selectCompany(this)">
                    {% if company.profile_picture %}
                        <img src="{{ company.profile_picture.url }}" alt="{{ company.name }}" class="company-image">
                    {% else %}
                        <i class="fas fa-building"></i> <!-- Fallback icon if no profile picture is set -->
                    {% endif %}
                </div>            
            {% endfor %}
        </div>
    </div> 

    <form method="post" id="select-company-form">
        {% csrf_token %}

        <!-- Structure Selection Table -->
        <div id="structure-selection">
            <table class="w-full border-collapse shadow-lg mb-8">
                <thead class="bg-accent-500 text-white font-bold">
                    <tr>
                        {% if structure %}
                            {% for level in structure %}
                                <th>{{ level }}</th>
                            {% endfor %}
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        {% if structure %}
                            {% for level in structure %}
                                <td id="level-{{ forloop.counter0 }}">
                                    <div class="level flex flex-col gap-2" id="nodes-{{ forloop.counter0 }}">
                                        <!-- This will dynamically load child nodes via JavaScript -->
                                    </div>
                                </td>
                            {% endfor %}
                        {% endif %}
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Hidden input to store the selected department and company -->
        <input type="hidden" name="company_value" id="company_value" value="">
        <input type="hidden" name="company" id="selected_company" value="">
        <input type="hidden" name="selected_department" id="selected_department" value="">
        <input type="hidden" name="selected_department_structure" id="selected_department_structure" value="">

        <!-- Submit button to proceed to the next step -->
        <button type="submit" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded mt-4">{% trans "Continue" %}</button>
    </form>
</div>

<script>
    function selectCompany(element) {
        const companyId = element.getAttribute('data-company-id');
        
        // Set selected company ID in hidden input
        document.getElementById('selected_company').value = companyId;
        
        // Update active class for visual selection
        document.querySelectorAll('.company-icon').forEach(icon => {
            icon.classList.remove('active');
        });
        element.classList.add('active');
    
        // Show structure selection and load nodes
        document.getElementById('structure-selection').style.display = 'block';
        loadCompanyStructure(companyId);
    }

    function loadCompanyStructure(companyId) {
        if (!companyId) {
            console.error('Invalid company ID:', companyId);
            return;
        }

        fetch(`/company/${companyId}/structure/`)
            .then(response => response.json())
            .then(data => {
                const structureAccordion = treeAccordion(companyId);
                structureAccordion.init();
                console.log('companystructure', data.structure);
            })
            .catch(error => console.error('Error loading company structure:', error));
    }

    function treeAccordion(companyId) {
        return {
            nodes: {},
            selectedNodes: {},
            selectedDepartment: '',
            selectedDepartmentNodeId: '',

            init() {
                this.loadChildren(companyId, 0);
                console.log('nodes', this.nodes);
            },

            toggleNode(nodeId, currentLevelIndex) {
                for (let i = currentLevelIndex + 1; i <= 3; i++) {
                    document.getElementById(`nodes-${i}`).innerHTML = '';
                }

                this.loadChildren(nodeId, currentLevelIndex + 1);

                document.querySelectorAll('.selected-node').forEach(node => {
                    node.classList.remove('selected-node');
                });

                const selectedNode = document.getElementById(`node-btn-${nodeId}`);
                if (selectedNode) {
                    selectedNode.classList.add('selected-node');
                }

                this.checkIfLowestLevel(nodeId, currentLevelIndex, selectedNode);
            },

            loadChildren(parentId, currentLevelIndex) {
                const url = parentId === companyId
                    ? `/structure/children/?company_id=${companyId}`
                    : `/structure/${parentId}/children/?company_id=${companyId}`;

                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        const levelContainer = document.getElementById(`nodes-${currentLevelIndex}`);
                        if (!levelContainer) {
                            console.error('Level container not found:', `nodes-${currentLevelIndex}`);
                            return;
                        }

                        levelContainer.innerHTML = '';

                        if (data.length === 0) {
                            this.updateSelectedDepartment(parentId, currentLevelIndex);
                        }

                        data.forEach(child => {
                            const childBtn = document.createElement('div');
                            childBtn.innerText = child.value;
                            childBtn.classList.add('node-item');
                            childBtn.id = `node-btn-${child.id}`;
                            childBtn.addEventListener('click', (event) => {
                                event.preventDefault();
                                this.toggleNode(child.id, currentLevelIndex);
                            });

                            levelContainer.appendChild(childBtn);
                        });
                    })
                    .catch(error => console.error('Error loading children:', error));
            },

            checkIfLowestLevel(nodeId, currentLevelIndex, selectedNode) {
                const url = `/structure/${nodeId}/children/`;
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length === 0) {
                            this.selectedDepartment = selectedNode.innerText;
                            this.selectedDepartmentNodeId = nodeId;
                            document.getElementById('selected_department').value = this.selectedDepartment;
                            document.getElementById('selected_department_structure').value = this.selectedDepartmentNodeId;
                            // Compute and store company value from the selected node ID.
                            let segments = nodeId.split('.');
                            let companyValue = segments.slice(0, 2).join('.');
                            document.getElementById('company_value').value = companyValue;
                        }
                    })
                    .catch(error => console.error('Error checking lowest level:', error));
            },

            updateSelectedDepartment(nodeId, currentLevelIndex) {
                this.selectedDepartment = document.getElementById(`node-btn-${nodeId}`).innerText;
                this.selectedDepartmentNodeId = nodeId;
                document.getElementById('selected_department').value = this.selectedDepartment;
                document.getElementById('selected_department_structure').value = this.selectedDepartmentNodeId;
            }
        };
    }

    function updateCompanyValue() {
        const selectedDeptNodeId = document.getElementById('selected_department_structure').value;
        if (selectedDeptNodeId) {
            const segments = selectedDeptNodeId.split('.');
            // Join the first two segments to form the company value (e.g., "2.2")
            const companyValue = segments.slice(0, 2).join('.');
            document.getElementById('company_value').value = companyValue;
        }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        const firstCompanyIcon = document.querySelector('.company-icon');
        if (firstCompanyIcon) {
            setTimeout(() => {
                selectCompany(firstCompanyIcon);  // Select the first company by default after a small delay
            }, 100); // Delay of 100 ms
        }
    });    
</script>
{% endblock %}
