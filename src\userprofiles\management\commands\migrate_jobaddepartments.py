import csv
from django.core.management.base import BaseCommand
from jobad.models import Jobad, JobadDepartment

class Command(BaseCommand):
    help = "Crea las entidades JobadDepartment para los Jobads de la compañía con pk 61 a partir de un CSV."

    def add_arguments(self, parser):
        parser.add_argument(
            'jobad_departments_csv',
            type=str,
            help='Ruta al archivo CSV con los datos de jobad departments.'
        )

    def handle(self, *args, **options):
        jobad_departments_csv = options['jobad_departments_csv']
        jobad_departments_data = {}

        try:
            with open(jobad_departments_csv, newline='', encoding='utf-8') as dept_file:
                dept_reader = csv.DictReader(dept_file)
                for row in dept_reader:
                    jobad_id = row.get('jobad', '').strip()
                    structure = row.get('structure', '').strip()
                    if jobad_id and structure:
                        jobad_departments_data[jobad_id] = structure
        except FileNotFoundError:
            self.stderr.write(f"Archivo CSV no encontrado: {jobad_departments_csv}")
            return
        except Exception as e:
            self.stderr.write(f"Error cargando el CSV de jobad departments: {e}")
            return

        # Recorrer los datos del CSV y crear JobadDepartment para company.pk 61
        for jobad_id, structure in jobad_departments_data.items():
            try:
                jobad_id_int = int(jobad_id)
            except ValueError:
                self.stderr.write(f"ID de Jobad inválido: {jobad_id}. Se omite.")
                continue

            try:
                jobad = Jobad.objects.get(pk=jobad_id_int)
            except Jobad.DoesNotExist:
                self.stderr.write(f"Jobad con id {jobad_id_int} no encontrado. Se omite.")
                continue

            # Solo procesamos si el Jobad pertenece a la compañía con pk 61
            if jobad.company.pk != 61:
                self.stdout.write(f"Jobad {jobad_id_int} no pertenece a la compañía 61. Se omite.")
                continue

            # Verificar si ya existe un JobadDepartment para este jobad
            if JobadDepartment.objects.filter(jobad=jobad).exists():
                self.stdout.write(f"JobadDepartment para Jobad {jobad_id_int} ya existe. Se omite.")
                continue

            try:
                JobadDepartment.objects.create(jobad=jobad, structure=structure)
                self.stdout.write(f"Creado JobadDepartment para Jobad {jobad_id_int} con estructura '{structure}'.")
            except Exception as e:
                self.stderr.write(f"Error creando JobadDepartment para Jobad {jobad_id_int}: {e}")

        self.stdout.write("Migración de JobadDepartment para compañía 61 completada.")
