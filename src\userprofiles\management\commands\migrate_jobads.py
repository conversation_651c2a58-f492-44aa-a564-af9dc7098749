import csv
import json
import os
import urllib.parse
import requests
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils.dateparse import parse_datetime
from django.utils import timezone
from urllib.parse import quote
from company.models import Company, CompanyPosition, CompanyLocation
from django.conf import settings
from jobad.models import Jobad, JobadDepartment
from userprofiles.models import UserProfile

class Command(BaseCommand):
    help = "Migrate jobads from a CSV file into the new Jobad model and create JobadDepartment for company 2."

    def add_arguments(self, parser):
        parser.add_argument('jobads_csv', type=str, help='Path to the CSV file with jobad data.')
        parser.add_argument('jobad_departments_csv', type=str, help='Path to the CSV file with jobad department data.')
        parser.add_argument('company_structures_csv', type=str, help='Path to the CSV file with company structures data.')

    def safe_json(self, value):
        if not value or value.strip().lower() in ('', 'null'):
            return None
        try:
            return json.loads(value)
        except Exception:
            return None

    def handle(self, *args, **options):
        # Load extra CSV files:
        jobad_departments_data = {}
        try:
            with open(options['jobad_departments_csv'], newline='', encoding='utf-8') as dept_file:
                dept_reader = csv.DictReader(dept_file)
                for row in dept_reader:
                    jobad_id = row.get('jobad', '').strip()
                    structure = row.get('structure', '').strip()
                    if jobad_id and structure:
                        jobad_departments_data[jobad_id] = structure
        except FileNotFoundError:
            self.stderr.write(f"Jobad departments CSV file not found: {options['jobad_departments_csv']}")
        except Exception as e:
            self.stderr.write(f"Error loading jobad departments CSV: {e}")

        company_structures_data = {}
        try:
            with open(options['company_structures_csv'], newline='', encoding='utf-8') as cs_file:
                cs_reader = csv.DictReader(cs_file)
                for row in cs_reader:
                    structure_id = row.get('id', '').strip()
                    value = row.get('value', '').strip()
                    if structure_id and value:
                        company_structures_data[structure_id] = value
        except FileNotFoundError:
            self.stderr.write(f"Company structures CSV file not found: {options['company_structures_csv']}")
        except Exception as e:
            self.stderr.write(f"Error loading company structures CSV: {e}")

        # Log in for file downloads (if needed)
        session = requests.Session()
        login_url = "https://proserv-ppms.de/api/auth"
        login_payload = {"username": "brunodelmoro", "password": "AbcAbc123!"}
        login_response = session.post(login_url, json=login_payload)
        if login_response.status_code != 200:
            self.stderr.write(f"Login failed with status code: {login_response.status_code}")
            self.stderr.write("Response: " + login_response.text)
            return
        token = login_response.headers.get("authorization")
        if token:
            session.headers.update({"Authorization": token})
        else:
            self.stderr.write("Login succeeded but no token was found in the response headers.")
            return
        self.stdout.write("Logged in successfully for file download.")

        # Process jobads CSV
        jobads_csv_path = options['jobads_csv']
        self.stdout.write(f"Processing Jobads CSV file: {jobads_csv_path}")
        with open(jobads_csv_path, newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                def clean_value(value):
                    if not value:
                        return ""
                    value = value.strip()
                    return "" if value.upper() == "NULL" else value

                try:
                    jobad_id = int(row.get('id'))
                except (TypeError, ValueError):
                    self.stderr.write("Invalid or missing id; skipping row.")
                    continue

                given_id = row.get('given_id', '').strip()

                company_id = row.get('company_id')
                try:
                    company = Company.objects.get(pk=company_id)
                except Company.DoesNotExist:
                    self.stderr.write(f"Company with id {company_id} not found for jobad {jobad_id}; skipping.")
                    continue

                position_title = row.get('position', '').strip()
                if not position_title:
                    self.stderr.write(f"Missing position for jobad {jobad_id}; skipping.")
                    continue
                position, _ = CompanyPosition.objects.get_or_create(company=company, title=position_title)

                location_str = row.get('location', '').strip()
                if not location_str:
                    self.stderr.write(f"Missing location for jobad {jobad_id}; skipping.")
                    continue
                location_qs = CompanyLocation.objects.filter(company=company, city=location_str)
                if location_qs.exists():
                    location = location_qs.first()
                else:
                    location = CompanyLocation.objects.create(
                        company=company,
                        city=location_str,
                        street='Strasse',
                        street_number='1',
                        post_code='1',
                        company_telephone=''
                    )

                try:
                    time_period = int(row.get('time_period', 0))
                    total = int(row.get('total', 0))
                    occupied = int(row.get('occupied', 0))
                    finished_deployments = int(row.get('finished_deployments', 0))
                except ValueError as e:
                    self.stderr.write(f"Error parsing numeric fields for jobad {jobad_id}: {e}")
                    continue

                stage = row.get('stage', '').strip()
                status = row.get('status', '').strip()

                def safe_user_id(val):
                    try:
                        user_id = int(val)
                    except (ValueError, TypeError):
                        return 1
                    try:
                        UserProfile.objects.get(pk=user_id)
                        return user_id
                    except UserProfile.DoesNotExist:
                        return 1

                created_by_id = safe_user_id(row.get('created_by_id'))
                modified_by_id = safe_user_id(row.get('modified_by_id'))
                approved_by_id = safe_user_id(row.get('approved_by_id'))

                def parse_dt(val):
                    val = val.strip()
                    if not val or val.lower() in ('null', ''):
                        return None
                    try:
                        return parse_datetime(val)
                    except Exception:
                        try:
                            return datetime.fromisoformat(val)
                        except Exception as e:
                            self.stderr.write(f"Error parsing datetime '{val}' for jobad {jobad_id}: {e}")
                            return None

                created_date = parse_dt(row.get('created_date', ''))
                if created_date and timezone.is_naive(created_date):
                    created_date = timezone.make_aware(created_date)
                modified_date = parse_dt(row.get('modified_date', ''))
                if modified_date and timezone.is_naive(modified_date):
                    modified_date = timezone.make_aware(modified_date)
                approved_date = parse_dt(row.get('approved_date', ''))
                if approved_date and timezone.is_naive(approved_date):
                    approved_date = timezone.make_aware(approved_date)

                employee_group = row.get('employee_group', '').strip()
                raw_description = clean_value(row.get('description', ''))
                description = urllib.parse.unquote(raw_description, encoding='cp1252', errors='replace')
                try:
                    description = description.encode('cp1252').decode('utf-8')
                except Exception as e:
                    self.stderr.write(f"Error re-encoding description for jobad {jobad_id}: {e}")
                cost_department = clean_value(row.get('cost_department', ''))
                weekly_working_hours = clean_value(row.get('weekly_working_hours', ''))
                if weekly_working_hours:
                    try:
                        weekly_working_hours = float(weekly_working_hours)
                    except ValueError:
                        weekly_working_hours = None
                anonymous = row.get('anonymous', '').strip().lower() == 'true'

                documents_json = self.safe_json(row.get('documents', ''))
                document_with_idx = self.safe_json(row.get('document_with_idx', ''))
                new_delay_duration = row.get('new_delay_duration', '').strip() or None

                tasks_json = self.safe_json(row.get('tasks', ''))
                tasks_text = "\n".join([item.get("name", "").strip() for item in tasks_json if item.get("name")]) if tasks_json and isinstance(tasks_json, list) else ""
                requirements_json = self.safe_json(row.get('requirements', ''))
                requirements_text = "\n".join([item.get("name", "").strip() for item in requirements_json if item.get("name")]) if requirements_json and isinstance(requirements_json, list) else ""
                driverlicense = self.safe_json(row.get('driverlicense', ''))
                starting_shift_time = row.get('starting_shift_time', '').strip() or None
                ending_shift_time = row.get('ending_shift_time', '').strip() or None

                contact_json = self.safe_json(row.get('contact', ''))
                if contact_json and isinstance(contact_json, list) and len(contact_json) > 0:
                    first_contact = contact_json[0]
                    contact_transformed = {
                        "contact_name": first_contact.get("name", "").strip(),
                        "contact_phone": first_contact.get("phoneNumber") or ""
                    }
                else:
                    contact_transformed = None

                g25_selected = row.get('g25selected', '').strip().lower() in ('true', '1')

                working_hours_json = self.safe_json(row.get('working_hours', ''))
                if working_hours_json and isinstance(working_hours_json, list):
                    working_hours_transformed = [
                        {"start": shift.get("start_time", "").strip(), "end": shift.get("end_time", "").strip()}
                        for shift in working_hours_json if shift is not None
                    ]
                else:
                    working_hours_transformed = None

                # Default department_value from CSV
                department_value = row.get('department_name', '').strip()

                # --- Extra processing for company Michelin (id 2) ---
                # If the company is Michelin (id 2), try to override department_value
                structure = None
                if company.pk == 2:
                    # Use the jobad id as a string key to check in jobad_departments_data
                    structure = jobad_departments_data.get(str(jobad_id))
                    if structure:
                        # Look up the structure in company_structures_data for the department value
                        dept_val = company_structures_data.get(structure)
                        if dept_val:
                            department_value = dept_val

                # --- Optionally, process document download for jobad ---
                if documents_json and isinstance(documents_json, list) and len(documents_json) > 0:
                    doc = documents_json[0]
                    filename = doc.get("filename", "").strip()
                    if filename:
                        file_path = f"jobad/{filename}"
                        download_endpoint = "https://proserv-ppms.de/api/files/download"
                        payload = doc
                        self.stdout.write(f"Downloading file for jobad {jobad_id} using endpoint {download_endpoint}")
                        try:
                            response = session.post(download_endpoint, json=payload, timeout=10)
                        except Exception as e:
                            self.stderr.write(f"Error downloading document for jobad {jobad_id}: {e}")
                            response = None
                        if response and response.status_code == 200:
                            full_file_path = os.path.join(settings.MEDIA_ROOT, file_path)
                            os.makedirs(os.path.dirname(full_file_path), exist_ok=True)
                            with open(full_file_path, "wb") as f:
                                f.write(response.content)
                            self.stdout.write(f"Saved file to {full_file_path}")
                            doc["url"] = os.path.join(settings.MEDIA_URL, quote(file_path))
                        else:
                            self.stderr.write(f"Failed to download document for jobad {jobad_id}. Status code: {response.status_code if response else 'N/A'}")
                        documents_json = [doc]
                else:
                    documents_json = None

                # Now create the Jobad record
                try:
                    jobad = Jobad.objects.create(
                        id=jobad_id,
                        given_id=given_id,
                        position=position,
                        location=location,
                        time_period=time_period,
                        publisher=row.get('publisher', '').strip(),
                        company_value=row.get('publisher', '').strip(),  # default from publisher
                        company=company,
                        total=total,
                        occupied=occupied,
                        finished_deployments=finished_deployments,
                        stage=stage,
                        status=status,
                        created_by_id=created_by_id,
                        modified_by_id=modified_by_id,
                        approved_by_id=approved_by_id,
                        created_date=created_date,
                        modified_date=modified_date,
                        approved_date=approved_date,
                        employee_group=employee_group,
                        description=description,
                        cost_department=cost_department,
                        weekly_working_hours=weekly_working_hours,
                        anonymous=anonymous,
                        document_with_idx=document_with_idx,
                        new_delay_duration=new_delay_duration,
                        tasks=tasks_text,
                        requirements=requirements_text,
                        driverlicense=driverlicense,
                        starting_shift_time=starting_shift_time,
                        ending_shift_time=ending_shift_time,
                        contact=contact_transformed,
                        g25_selected=g25_selected,
                        working_hours=working_hours_transformed,
                        department_value=department_value,
                        documents=documents_json,
                    )
                    self.stdout.write(f"Created Jobad {jobad.id} for company {company.id}")
                except Exception as e:
                    self.stderr.write(f"Error creating Jobad {jobad_id}: {e}")
                    continue

                # If company is Michelin (id 2) and a structure was found, create the JobadDepartment entity.
                if company.pk in (2, 61) and structure:
                    try:
                        from jobad.models import JobadDepartment  # import here if not imported at top
                        JobadDepartment.objects.create(jobad=jobad, structure=structure)
                        self.stdout.write(f"Created JobadDepartment for Jobad {jobad.id} with structure {structure}")
                    except Exception as e:
                        self.stderr.write(f"Error creating JobadDepartment for jobad {jobad_id}: {e}")

        self.stdout.write("Jobad migration complete.")