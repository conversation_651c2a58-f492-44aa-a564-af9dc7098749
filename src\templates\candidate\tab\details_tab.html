{% load static %}
{% load i18n %}
<style>
    .input-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .input-container div {
        flex-direction: column;
    }

    .buttons {
        display: flex;
        justify-content: end;
        margin-top: 4px;
    }

    .profile-image-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin-bottom: 1rem;
    }

    .profile-image-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        cursor: pointer;
    }

    .profile-image-container input {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .country-flag-container {
        position: absolute;
        top: 10px;
        right: 10px;
        background: white;
        padding: 2px;
        border-radius: 50%;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }

    .country-flag-container img {
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }

    .form-container {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
    }
</style>

<div class="max-w-4xl mx-auto h-full">
    <form method="post" action="{% url 'candidate-detail' candidate.pk %}?tab=details" enctype="multipart/form-data" class="form-container">
        {% csrf_token %}
        <div class="input-container">
            <div class="profile-image-container">
                {% if candidate.profile_picture %}
                    <img id="profile-image-preview" src="{{ candidate.profile_picture.url }}" alt="Profile Image">
                {% else %}
                    <img id="profile-image-preview" src="{% static 'images/avatar2.jpg' %}" alt="Default Profile Image">
                {% endif %}
                {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 3 or request.user.userprofile.role == 4 %}
                    <input type="file" name="profile_picture" accept="image/*" id="profile-picture-input" onchange="previewImage(event)">
                {% endif %}
                {% if candidate.country %}
                    <div class="country-flag-container">
                        <img src="{{ candidate.country.flag }}" alt="{{ candidate.country.name }}" title="{{ candidate.country.name }}">
                    </div>
                {% endif %}
            </div>
            <div>
                <label class="block text-gray-700 text-sm font-bold mb-2">{% trans "Company" %}</label>
                <input type="text" value="{{ company_name }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" disabled>
                <input type="hidden" name="company" value="{{ candidate.company.id }}">
            </div>
            {% for field in form %}
                {% if field.name != 'documents' and field.name != 'profile_picture' and field.name != 'company' %}
                    <div>
                        <label class="text-gray-700 text-sm font-bold mb-2" for="{{ field.id_for_label }}">
                            {{ field.label }}
                        </label>
                        {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 3 or request.user.userprofile.role == 4 %}
                            {{ field }}
                        {% else %}
                            <input type="text" value="{{ field.value|default_if_none:'' }}" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" disabled>
                        {% endif %}
                        {% for error in field.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 3 or request.user.userprofile.role == 4 %}
            <div class="buttons">
                <button type="button" onclick="showModal()" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 mr-6 rounded">{% trans "Delete Candidate" %}</button>
                <button type="submit" class="bg-accent-500 hover:bg-secondary-500 hover:text-black text-white font-bold py-2 px-4 rounded">{% trans "Save Changes" %}</button>
            </div>
        {% endif %}
    </form>
    <div id="delete-modal" style="display: none;" class="fixed z-50 inset-0 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
            <h2 class="text-lg font-bold mb-4">{% trans "Confirm Deletion" %}</h2>
            <p class="mb-6">{% trans "Are you sure you want to delete this candidate?" %}</p>
            <div class="flex justify-end gap-4">
                <button onclick="hideModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">{% trans "Cancel" %}</button>
                <form method="post" action="{% url 'delete-candidate' candidate.pk %}">
                    {% csrf_token %}
                    <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">{% trans "Delete" %}</button>
                </form>
            </div>
        </div>
    </div>    
</div>

<script>
    function showModal() {
        document.getElementById('delete-modal').style.display = 'flex';
    }
    function hideModal() {
        document.getElementById('delete-modal').style.display = 'none';
    }
    function previewImage(event) {
        const input = event.target;
        const reader = new FileReader();
        reader.onload = function() {
            const dataURL = reader.result;
            const output = document.getElementById('profile-image-preview');
            output.src = dataURL;
        };
        reader.readAsDataURL(input.files[0]);
    }
</script>
