from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from unfold.admin import ModelAdmin
from django.contrib import messages

from jobad.models import (
    JobaDocument, Jobad, JobadApplication, JobadTemplate, Candidate, Worker, Interview,
    JobadAdditionalField, JobadDepartment, JobadFavorite, JobadManualStage,
    JobadTemplate, Selection, WorkerRejectionReason, RejectedCandidateReason, Task, CandidateDocument
)


# Inlines
class JobadInline(admin.StackedInline):
    model = Jobad
    extra = 0


class JobadApplicationInline(admin.StackedInline):
    model = JobadApplication
    extra = 0


class WorkerInline(admin.StackedInline):
    model = Worker
    extra = 0


class InterviewInline(admin.StackedInline):
    model = Interview
    extra = 0


class JobadAdditionalFieldInline(admin.TabularInline):
    model = JobadAdditionalField
    extra = 0


class JobadDepartmentInline(admin.TabularInline):
    model = JobadDepartment
    extra = 0


class JobadFavoriteInline(admin.TabularInline):
    model = JobadFavorite
    extra = 0


class JobadManualStageInline(admin.TabularInline):
    model = JobadManualStage
    extra = 0


class SelectionInline(admin.TabularInline):
    model = Selection
    extra = 0


class WorkerRejectionReasonInline(admin.TabularInline):
    model = WorkerRejectionReason
    extra = 0


class RejectedCandidateReasonInline(admin.TabularInline):
    model = RejectedCandidateReason
    extra = 0


class TaskInline(admin.TabularInline):
    model = Task
    extra = 0


class CandidateDocumentInline(admin.StackedInline):
    model = CandidateDocument
    extra = 0


@admin.action(description="❌ Delete all jobads from the system")
def delete_all_jobads(modeladmin, request, queryset):
    total = Jobad.objects.count()
    Jobad.objects.all().delete()
    messages.success(request, _(f"Se eliminaron {total} Jobads."))

@admin.register(Jobad)
class JobadAdmin(ModelAdmin):
    actions = ['delete_selected', 'delete_all_jobads']
    list_display = (
        'position', 'location', 'company', 'publisher', 'working_hours', 'time_period', 'total', 'occupied', 'finished_deployments',
        'stage', 'status', 'created_by_id', 'created_date', 'department_value', 'modified_by_id', 'modified_date',
        'contact', 'weekly_working_hours', 'tasks', 'requirements', 'employee_group'
    )
    search_fields = ('position', 'location', 'company', 'publisher', 'status',)
    list_filter = ('stage', 'status', 'company',)
    ordering = ('-created_date',)
    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_all_jobads'] = (delete_all_jobads, 'delete_all_jobads', delete_all_jobads.short_description)
        return actions 

@admin.action(description="❌ Delete all jobad applications from the system")
def delete_all_jobadapplications(modeladmin, request, queryset):
    total = JobadApplication.objects.count()
    JobadApplication.objects.all().delete()
    messages.success(request, _(f"Se eliminaron {total} JobadApplications."))

@admin.register(JobadApplication)
class JobadApplicationAdmin(ModelAdmin):
    actions = ['delete_selected', 'delete_all_jobadapplications']
    list_display = ('jobad', 'candidate', 'stage', 'status', 'interview_skipped' , 'created_date', 'modified_date',)
    search_fields = (
        'jobad__position', 'candidate__firstname', 'candidate__lastname', 'status', 'created_date', 'modified_date',)
    list_filter = ('stage', 'status',)
    ordering = ('-created_date',)
    inlines = [InterviewInline, SelectionInline, ]
    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_all_jobadapplications'] = (delete_all_jobadapplications, 'delete_all_jobadapplications', delete_all_jobadapplications.short_description)
        return actions 


@admin.register(JobadTemplate)
class JobadTemplateAdmin(ModelAdmin):
    list_display = ('name', 'position', 'location', 'time_period', 'description', 'total', 'contact',)
    search_fields = ('name', 'position', 'location', 'contact',)
    list_filter = ('position', 'location',)
    ordering = ('name',)


@admin.register(Candidate)
class CandidateAdmin(ModelAdmin):
    list_display = (
        'firstname', 'lastname', 'date_of_birth', 'company' , 'status', 'created_date',
        'comment',
        'application_date', 'profile_picture_display', 'country_flag_display',
        'hourly_rate', 'country',
    )
    search_fields = (
        'firstname', 'lastname', 'company__name', 'status', 'date_of_birth',
        'created_date', 'application_date'
    )
    list_filter = ('company', 'status',)
    ordering = ('-created_date',)
    inlines = [JobadApplicationInline, WorkerInline, RejectedCandidateReasonInline, CandidateDocumentInline]

    # Show mini profile picture in admin tool
    def profile_picture_display(self, obj):
        if obj.profile_picture:
            return format_html(
                '<a href="{0}" target="_blank"><img src="{0}" width="50" height="50" /></a>',
                obj.profile_picture.url
            )
        return '(No image)'
    profile_picture_display.allow_tags = True
    profile_picture_display.short_description = 'Profile Picture'

    # show mini flag in admin tool
    def country_flag_display(self, obj):
        if obj.country:
            return format_html(
                '<img src="{0}" alt="{1}" width="20" height="15" />',
                obj.country.flag,
                obj.country.name
            )
        return '(No flag)'
    country_flag_display.allow_tags = True
    country_flag_display.short_description = 'Country Flag'

    fieldsets = (
        (None, {
            'fields': (
                'firstname', 'lastname', 'date_of_birth', 'company', 'application_date', 'created_date',
                'modified_date', 'title', 'status', 'comment', 'deleted_at', 'anonymous',
                'document_with_idx', 'country', 'profile_picture'
            )
        }),
    )


@admin.action(description="❌ Delete all Workers from the system")
def delete_all_workers(modeladmin, request, queryset):
    total = Worker.objects.count()
    Worker.objects.all().delete()
    messages.success(request, _(f"Se eliminaron {total} Worker."))

@admin.register(Worker)
class WorkerAdmin(ModelAdmin):
    actions = ['delete_selected', 'delete_all_workers']
    list_display = ('candidate', 'jobad', 'stage', 'status', 'start_date', 'end_date', 'created_by',)
    search_fields = (
        'candidate__firstname', 'candidate__lastname', 'jobad__position', 'stage', 'status', 'start_date', 'end_date',
        'created_by',)
    list_filter = ('candidate', 'jobad', 'stage', 'status', 'start_date', 'end_date', 'created_by',)
    ordering = ('-start_date',)
    inlines = [WorkerRejectionReasonInline, TaskInline]

    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_all_workers'] = (delete_all_workers, 'delete_all_workers', delete_all_workers.short_description)
        return actions 


@admin.register(Interview)
class InterviewAdmin(ModelAdmin):
    list_display = ('application', 'stage', 'status', 'interview_dates',)
    search_fields = (
        'application__jobad__position', 'application__candidate__firstname', 'application__candidate__lastname',
        'stage',
        'status',)
    list_filter = ('stage', 'status',)
    ordering = ('-interview_dates',)


@admin.register(JobadAdditionalField)
class JobadAdditionalFieldAdmin(ModelAdmin):
    list_display = ('jobad', 'field_id', 'label', 'value', 'is_required', 'max_input',)
    search_fields = ('jobad__position', 'label', 'value',)
    list_filter = ('jobad', 'is_required',)
    ordering = ('jobad', 'field_id')


@admin.register(JobadDepartment)
class JobadDepartmentAdmin(ModelAdmin):
    list_display = ('jobad_id_display', 'structure',)
    search_fields = ('jobad__id', 'structure',)
    list_filter = ('jobad',)
    ordering = ('jobad', 'structure')

    def jobad_id_display(self, obj):
        return obj.jobad.id  # Display the Jobad ID

    # Keep the column name as "Jobad" in the admin panel
    jobad_id_display.short_description = 'Jobad'


@admin.register(JobadFavorite)
class JobadFavoriteAdmin(ModelAdmin):
    list_display = ('jobad', 'user_ref',)
    search_fields = ('jobad__position', 'user_ref__username',)
    list_filter = ('jobad',)
    ordering = ('jobad', 'user_ref')


@admin.register(JobadManualStage)
class JobadManualStageAdmin(ModelAdmin):
    list_display = ('jobad', 'manual_stage_change',)
    search_fields = ('jobad__position', 'manual_stage_change',)
    ordering = ('jobad',)


@admin.register(Selection)
class SelectionAdmin(ModelAdmin):
    list_display = ('application', 'start_date', 'end_date', 'start_time', 'contact_person_name', 'contact_person_phone', 'exceeds_eqp', 'stage', 'status',)
    search_fields = (
        'application__jobad__position', 'application__candidate__firstname', 'application__candidate__lastname',
        'stage',
        'status',)
    list_filter = ('stage', 'status',)
    ordering = ('-start_date',)


@admin.register(WorkerRejectionReason)
class WorkerRejectionReasonAdmin(ModelAdmin):
    list_display = ('worker', 'reason',)
    search_fields = (
        'worker__candidate__firstname', 'worker__candidate__lastname', 'worker__jobad__position', 'reason__text',)
    list_filter = ('reason',)
    ordering = ('worker',)


@admin.register(RejectedCandidateReason)
class RejectedCandidateReasonAdmin(ModelAdmin):
    list_display = ('applicant', 'reason',)
    search_fields = (
        'applicant__jobad__position', 'applicant__candidate__firstname', 'applicant__candidate__lastname',
        'reason__text',)
    list_filter = ('reason',)
    ordering = ('applicant',)


@admin.register(Task)
class TaskAdmin(ModelAdmin):
    list_display = ('title', 'jobad', 'application', 'worker', 'done', 'done_by', 'done_date', 'created_date',)
    search_fields = ('title', 'jobad__position', 'application__jobad__position', 'application__candidate__firstname',
                     'application__candidate__lastname', 'worker__candidate__firstname', 'worker__candidate__lastname',
                     'done_by',)
    list_filter = ('done',)
    ordering = ('-created_date',)


@admin.register(CandidateDocument)
class CandidateDocumentAdmin(ModelAdmin):
    list_display = ('candidate', 'document_link', 'document_type', 'uploaded_by', 'expiration_date')
    search_fields = ('candidate__name',)

    def document_link(self, obj):
        url = reverse("document_download", args=["candidate", obj.pk])
        return format_html('<a href="{}" target="_blank">📎 {}</a>', url, obj.document.name.split("/")[-1])

    document_link.short_description = "Document"

@admin.register(JobaDocument)
class JobaDocumentAdmin(ModelAdmin):
    list_display = ('jobad', 'document',)
    search_fields = ('jobad__name',)