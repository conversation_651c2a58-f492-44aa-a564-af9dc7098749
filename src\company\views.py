import json
import uuid
import requests

from django.contrib import messages
from django.http import StreamingHttpResponse, Http404
from django.db.models import Q
from django.http import JsonResponse, FileResponse
from django.template.loader import render_to_string
from email_service.tasks import send_async_email 
from django.utils.translation import gettext as _
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404, redirect, render
from django.db import transaction
from urllib.parse import urlencode
from django.urls import reverse_lazy, reverse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.views.generic import CreateView, DeleteView, UpdateView
from azure.storage.blob import BlobServiceClient
from azure.identity import DefaultAzureCredential
from common.models import Settings
from company.forms import CompanyForm, PositionForm, LocationForm, DocumentForm, \
    CompanySupplierForm
from company.models import Company, CompanyDocument, CompanyPosition, CompanyLocation, RejectionEndReason, \
    CompanyStructure, CompanyPartner
from django.conf import settings
from jobad.models import CandidateDocument
from userprofiles.forms import UserProfileForm
from userprofiles.models import UserProfile, UserProfileDepartments


class BaseCompanyListView(View):
    template_name = 'company/list.html'
    company_type = None
    title = None

    def get_filtered_companies(self, request):
        user = request.user
        user_profile = user.userprofile
        user_company = user_profile.company

        if user_company.company_type == 'global':
            # Global users see all companies of the specified type
            return Company.objects.filter(company_type=self.company_type)
        elif user_company.company_type == 'customer':
            if self.company_type == 'customer':
                # Customers see only their own company
                return Company.objects.filter(id=user_company.id)
            elif self.company_type == 'supplier':
                # Customers see linked suppliers
                linked_companies = CompanyPartner.objects.filter(
                    customer=user_company
                ).values_list('company', flat=True)
                return Company.objects.filter(id__in=linked_companies, company_type='supplier')
            elif self.company_type == 'vendor':
                # Customers see linked vendors
                linked_vendors = CompanyPartner.objects.filter(
                    customer=user_company, is_vendor=True
                ).values_list('company', flat=True)
                return Company.objects.filter(id__in=linked_vendors, company_type='vendor')
        elif user_company.company_type == 'supplier':
            if self.company_type == 'supplier':
                # Suppliers see only their own company
                return Company.objects.filter(id=user_company.id)
            elif self.company_type == 'customer':
                # Suppliers see their linked customer company
                linked_customer = CompanyPartner.objects.filter(
                    company=user_company
                ).values_list('customer', flat=True).first()
                return Company.objects.filter(id=linked_customer, company_type='customer')
            elif self.company_type == 'vendor':
                # Suppliers see linked vendors, determined by is_vendor=True
                linked_vendors = CompanyPartner.objects.filter(
                    company=user_company, is_vendor=True
                ).values_list('company', flat=True)
                return Company.objects.filter(id__in=linked_vendors, company_type='vendor')
        elif user_company.company_type == 'vendor':
            if self.company_type == 'customer':
                # Aquí, el vendor ve el customer al que está vinculado a través de CompanyPartner.
                linked_customers = CompanyPartner.objects.filter(
                    company=user_company
                ).values_list('customer', flat=True)
                return Company.objects.filter(id__in=linked_customers, company_type='customer')
            elif self.company_type == 'vendor':
                # En la pestaña Master Vendor, el vendor ve su propia compañía.
                return Company.objects.filter(id=user_company.id)
            elif self.company_type == 'supplier':
                # Lógica para la pestaña suppliers:
                # 1. Obtener de los CompanyPartner del vendor los registros cuyas estructuras tengan 5 segmentos
                vendor_partners = CompanyPartner.objects.filter(company=user_company)
                deepest_structures = []
                for partner in vendor_partners:
                    if partner.structure:
                        parts = partner.structure.split('.')
                        if len(parts) == 5:  # Es el patrón companyId.x.x.x.x
                            deepest_structures.append(partner.structure)
                deepest_structures = list(set(deepest_structures))
                
                # 2. Buscar en CompanyPartner los suppliers conectados a alguno de esos nodos
                supplier_partner_ids = CompanyPartner.objects.filter(
                    structure__in=deepest_structures,
                    company__company_type='supplier'
                ).values_list('company', flat=True)
                
                # 3. Retornar las compañías supplier correspondientes
                return Company.objects.filter(id__in=supplier_partner_ids, company_type='supplier')
        else:
            # En caso de que el tipo de compañía sea inesperado.
            return Company.objects.none()

    def get(self, request, *args, **kwargs):
        companies = self.get_filtered_companies(request)
        query = request.GET.get('search')
        if query:
            companies = companies.filter(Q(name__icontains=query))
        form = CompanyForm()
        context = {
            'object_list': companies,
            'form': form,
            'title': self.title,
            'company_type': self.company_type,
        }
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        form = CompanyForm(request.POST, request.FILES)
        if form.is_valid():
            company = form.save(commit=False)
            company.company_type = self.company_type
            company.save()
            messages.success(request, f'{self.title} successfully created', extra_tags='bg-green-500 text-black')
            return redirect(reverse('company-detail', kwargs={'pk': company.pk}))
        else:
            companies = self.get_filtered_companies(request)
            query = request.GET.get('search')
            if query:
                companies = companies.filter(Q(name__icontains=query))
            context = {
                'object_list': companies,
                'form': form,
                'title': self.title,
                'company_type': self.company_type,
            }
            return render(request, self.template_name, context)


class CompanyListView(BaseCompanyListView):
    company_type = 'customer'
    title = 'Companies'


class SupplierListView(BaseCompanyListView):
    company_type = 'supplier'
    title = 'Suppliers'


class MasterVendorListView(BaseCompanyListView):
    company_type = 'vendor'
    title = 'Master Vendors'


class CompanyDeleteView(DeleteView):
    model = Company
    success_url = reverse_lazy('company-list')

    def get(self, request, *args, **kwargs):
        # Retrieve the object
        self.object = self.get_object()
        # Perform the deletion
        self.object.delete()
        messages.success(request, "Company deleted successfully.")
        return redirect(self.success_url)


class CompanyCreateView(CreateView):
    model = Company
    form_class = CompanyForm
    template_name = 'company/add.html'
    success_url = reverse_lazy('company-list')


def company_detail(request, pk):
    company = get_object_or_404(Company, pk=pk)
    tab = request.GET.get('tab', 'details')
    allowed_tabs = ['details', 'documents', 'terms', 'user']
    if company.company_type == 'customer':
        allowed_tabs.extend(['locations', 'employees', 'structure', 'supplier'])
    if tab not in allowed_tabs:
        tab = 'details'

    default_structure = ["Country", "Location", "Department", "Plant"]
    positions = CompanyPosition.objects.filter(company=company).order_by('title') if tab == 'locations' else []
    locations = CompanyLocation.objects.filter(company=company).order_by('street', 'city') if tab == 'locations' else []
    documents = CompanyDocument.objects.filter(company=company) if tab == 'documents' else []
    rejection_reasons = RejectionEndReason.objects.filter(company=company, setting__pk=1) if tab == 'employees' else []
    end_reasons = RejectionEndReason.objects.filter(company=company, setting__pk=2) if tab == 'employees' else []
    probetag = RejectionEndReason.objects.filter(company=company, setting__pk=3) if tab == 'employees' else []
    structures = CompanyStructure.objects.filter(company=company) if tab in ['structure', 'supplier'] else []
    structure = company.structure if company.structure else ["", "", "", ""]
    structure += [""] * (4 - len(structure))
    num_additional_columns = 4 - len(structure)
    suppliers = Company.objects.filter(company_type='supplier') if tab == 'supplier' else []
    vendors = Company.objects.filter(company_type='vendor') if tab == 'supplier' else []
    users = UserProfile.objects.filter(company=company) if tab == 'user' else []

    linked_supplier_nodes_json = '[]'
    linked_suppliers = []
    if tab == 'supplier':
        company_partners = CompanyPartner.objects.filter(customer=company)
        supplier_search = request.GET.get('q', '')
        if supplier_search:
            company_partners = company_partners.filter(company__name__icontains=supplier_search)
        
        # Paginate the results (e.g., 20 per page)
        paginator = Paginator(company_partners, 45)
        page = request.GET.get('page')
        try:
            partners_page = paginator.page(page)
        except PageNotAnInteger:
            partners_page = paginator.page(1)
        except EmptyPage:
            partners_page = paginator.page(paginator.num_pages)

        linked_supplier_nodes = list(partners_page.object_list.values_list('structure', flat=True))
        linked_supplier_nodes_json = json.dumps(linked_supplier_nodes)
        preselected_nodes_by_supplier = {}
        supplier_dict = {}
        for partner in partners_page.object_list:
            supplier_id = partner.company.id
            preselected_nodes_by_supplier.setdefault(supplier_id, []).append(partner.structure)
            
            supplier_name = partner.company.name
            if supplier_name not in supplier_dict:
                supplier_dict[supplier_name] = {
                    'id': supplier_id,
                    'name': supplier_name,
                    'node_levels': []
                }
            current_node = partner.structure
            try:
                node = CompanyStructure.objects.get(id=current_node)
                level = len(node.id.split('.')) - 1
                while len(supplier_dict[supplier_name]['node_levels']) <= level:
                    supplier_dict[supplier_name]['node_levels'].append([])
                supplier_dict[supplier_name]['node_levels'][level].append(node)
            except CompanyStructure.DoesNotExist:
                continue
        linked_suppliers = list(supplier_dict.values())
        linked_supplier_nodes_json = json.dumps(preselected_nodes_by_supplier)
        
    if tab == 'user':
        users = UserProfile.objects.filter(company=company)
        profile_form = UserProfileForm()
    else:
        users = []
        profile_form = None

    if tab == 'employees':
        rejection_reasons = RejectionEndReason.objects.filter(
            company=company, setting__pk=1
        ).order_by('text')

        probetag = RejectionEndReason.objects.filter(
            company=company, setting__pk=3
        ).order_by('text')

        end_reasons = RejectionEndReason.objects.filter(
            company=company, setting__pk=2
        ).order_by('text')
        reasons_list = [
            ("Ablehnung vor Probetag", rejection_reasons, reverse('company-detail', kwargs={'pk': company.pk}), 1),
            ("Ablehnung nach Probetag", probetag, reverse('company-detail', kwargs={'pk': company.pk}), 3),
            ("Abmeldegrund", end_reasons, reverse('company-detail', kwargs={'pk': company.pk}), 2)
        ]

        if request.method == 'POST':
            reason_text = request.POST.get('reason_text')
            setting_id = request.POST.get('setting_id')

            if reason_text and setting_id:
                setting = get_object_or_404(Settings, pk=setting_id)
                RejectionEndReason.objects.create(
                    company=company,
                    text=reason_text,
                    setting=setting,
                    edited=False,
                    previous_id=None
                )
                messages.success(request, 'New reason added successfully')
                return redirect(f"{reverse('company-detail', kwargs={'pk': pk})}?tab=employees")
    else:
        reasons_list = []

    # Calculate the maximum depth (number of levels) across all suppliers
    max_levels = max(len(supplier['node_levels']) for supplier in linked_suppliers) if linked_suppliers else 0

    # Create the levels range for the template context
    levels_range = range(max_levels)

    print(f"linked_suppliers: {linked_suppliers}")

    supplier_form = CompanySupplierForm()
    open_location_modal = False

    if request.method == 'POST':
        if 'openLocationModal' in request.POST:
            open_location_modal = True

        supplier_form = CompanySupplierForm()
        document_form = DocumentForm()
        form = CompanyForm(instance=company)
        location_form = LocationForm()
        if tab == 'supplier':
            supplier_form = CompanySupplierForm(request.POST)
            if supplier_form.is_valid():
                company_supplier = supplier_form.save(commit=False)
                company_supplier.company = company
                company_supplier.save()
                messages.success(request, 'Supplier linked successfully', extra_tags='success')
                return redirect(f"{reverse('company-detail', kwargs={'pk': pk})}?tab=supplier")
        elif 'document' in request.FILES:
            document_form = DocumentForm(request.POST, request.FILES)
            if document_form.is_valid():
                document = document_form.save(commit=False)
                document.company = company
                document.uploaded_by = request.user
                document.save()
                messages.success(request, 'Document uploaded successfully', extra_tags='success')

                company_url = request.build_absolute_uri()

                recipients = UserProfile.objects.filter(
                    company=company,
                    user__is_active=True
                ).select_related('user')

                subject = _('Neues Dokument für %(name)s') % {'name': company.name}
                # render a simple text/html body (create this template yourself)
                for profile in recipients:
                    context = {
                        'recipient_name': profile.user.get_full_name(),
                        'company_name': company.name,
                        'company_url': company_url,
                    }
                    body = render_to_string("emails/company_document_uploaded.html", context)
                    send_async_email.delay(profile.user.email, subject, body)

                return redirect(f"{reverse('company-detail', kwargs={'pk': pk})}?tab=documents")
        else:
            form = CompanyForm(request.POST, request.FILES, instance=company)
            if form.is_valid():
                form.save()
                messages.success(request, 'Company details updated successfully', extra_tags='success')
                if company.company_type == 'supplier':
                    return redirect('supplier-list')
                elif company.company_type == 'vendor':
                    return redirect('master-vendor-list')
                else:
                    return redirect('company-list')
    else:
        form = CompanyForm(instance=company)
        document_form = DocumentForm()
        supplier_form = CompanySupplierForm()
        location_form = LocationForm()

    context = {
        'company': company,
        'company_type': company.company_type,
        'form': form,
        'positions': positions,
        'locations': locations,
        'tab': tab,
        'document_form': document_form,
        'documents': documents,
        'rejection_reasons': rejection_reasons,
        'end_reasons': end_reasons,
        'probetag': probetag,
        'structures': structures,
        'structure': structure,
        'num_additional_columns': num_additional_columns,
        'suppliers': suppliers,
        'supplier_form': supplier_form,
        'linked_suppliers': linked_suppliers,
        'vendors': vendors,
        'allowed_tabs': allowed_tabs,
        'location_form': location_form,
        'openLocationModal': open_location_modal,
        'default_structure': default_structure,
        'levels_range': levels_range,
        'reasons_list': reasons_list,
        'users': users,
        'profile_form': profile_form,
        'linked_supplier_nodes_json': linked_supplier_nodes_json,
    }
    if tab == 'supplier':
        context['partners_page'] = partners_page  # Paginated CompanyPartner objects
        context['supplier_search'] = supplier_search

    return render(request, 'company/company_detail.html', context)

class PositionCreateView(CreateView):
    model = CompanyPosition
    form_class = PositionForm
    template_name = 'company/position_form.html'

    def form_valid(self, form):
        form.instance.company_id = self.kwargs['company_id']
        return super().form_valid(form)

    def get_success_url(self):
        return f"{reverse('company-detail', kwargs={'pk': self.kwargs['company_id']})}?tab=locations"


class LocationCreateView(CreateView):
    model = CompanyLocation
    form_class = LocationForm

    # template_name = 'company/tabs/position_list_view.html'

    def form_valid(self, form):
        form.instance.company_id = self.kwargs['company_id']
        return super().form_valid(form)

    def form_invalid(self, form):
        company = get_object_or_404(Company, pk=self.kwargs['company_id'])
        positions = CompanyPosition.objects.filter(company=company)
        locations = CompanyLocation.objects.filter(company=company)
        tab = 'locations'
        context = {
            'company': company,
            'form': CompanyForm(instance=company),
            'positions': positions,
            'locations': locations,
            'tab': tab,
            'location_form': form,
            'openLocationModal': True,
        }
        return render(self.request, 'company/company_detail.html', context)

    def get_success_url(self):
        return f"{reverse('company-detail', kwargs={'pk': self.kwargs['company_id']})}?tab=locations"


class PositionUpdateView(UpdateView):
    model = CompanyPosition
    form_class = PositionForm
    template_name = 'company/position_form.html'

    def get_success_url(self):
        return f"{reverse('company-detail', kwargs={'pk': self.kwargs['company_id']})}?tab=locations"


class LocationUpdateView(UpdateView):
    model = CompanyLocation
    form_class = LocationForm
    template_name = 'company/location_form.html'

    def get_success_url(self):
        return f"{reverse('company-detail', kwargs={'pk': self.kwargs['company_id']})}?tab=locations"


def delete_position(request, pk):
    position = get_object_or_404(CompanyPosition, pk=pk)
    company_id = position.company_id
    position.delete()

    return redirect(f"{reverse('company-detail', kwargs={'pk': company_id})}?tab=locations")


def delete_reason(request, pk):
    reason = get_object_or_404(RejectionEndReason, pk=pk)
    company_id = reason.company_id
    reason.delete()

    return redirect(f"{reverse('company-detail', kwargs={'pk': company_id})}?tab=employees")


def delete_location(request, pk):
    location = get_object_or_404(CompanyLocation, pk=pk)
    company_id = location.company_id
    location.delete()

    return redirect(f"{reverse('company-detail', kwargs={'pk': company_id})}?tab=locations")


def upload_document(request, pk):
    company = get_object_or_404(Company, pk=pk)
    if request.method == 'POST':
        form = DocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save(commit=False)
            document.company = company
            document.uploaded_by = request.user
            document.save()
            return redirect('company-detail', pk=company.pk)
    else:
        form = DocumentForm()
    return render(request, 'company/upload_document.html', {'form': form, 'company': company})


DOCUMENT_MODELS = {
    "candidate": CandidateDocument,
    "company": CompanyDocument,
}
 
def download_document(request, doc_type, pk):
    # 1. Resolve model
    model = DOCUMENT_MODELS.get(doc_type)
    if not model:
        raise Http404("Invalid document type")

    # 2. Fetch document
    try:
        doc = model.objects.get(pk=pk)
    except model.DoesNotExist:
        raise Http404("Document not found")

    # 3. Prepare blob parameters
    blob_name = doc.document.name
    container_name = settings.AZURE_CONTAINER
    account_name = settings.AZURE_ACCOUNT_NAME
    account_url = f"https://{account_name}.blob.core.windows.net"
    print("Blob name:", blob_name)
    print("Container:", container_name)
    print("Account URL:", account_url)
    # 4. Authenticate and download with Azure SDK
    try:
        credential = DefaultAzureCredential()
        blob_service_client = BlobServiceClient(account_url=account_url, credential=credential)
        blob_client = blob_service_client.get_blob_client(container=container_name, blob=blob_name)

        stream = blob_client.download_blob().chunks()
    except Exception as e:
        print("Blob download error:", e)
        raise Http404("Failed to access blob")

    filename = blob_name.split("/")[-1]

    # 5. Stream to browser
    response = StreamingHttpResponse(
        stream,
        content_type="application/octet-stream"
    )
    response["Content-Disposition"] = f'attachment; filename="{filename}"'
    return response

class SupplierCreateView(CompanyCreateView):
    company_type = 'supplier'
    title = 'Supplier'

    def get_success_url(self):
        return reverse_lazy('supplier-list')


class MasterVendorCreateView(CompanyCreateView):
    company_type = 'vendor'
    title = 'Master Vendor'

    def get_success_url(self):
        return reverse_lazy('master-vendor-list')


def update_structure(request, company_id):
    company = get_object_or_404(Company, pk=company_id)

    if request.method == 'POST':
        # Extract structure levels from the POST data
        structure = []
        for i in range(4):  # Assuming 4 levels max (Company, Location, Plant, Department)
            input_name = f'input_{i}'
            if request.POST.get(input_name):
                structure.append(request.POST[input_name])

        # Update the company's structure JSON field
        company.structure = structure
        company.save()

        messages.success(request, "Company structure updated successfully.")
        return redirect(reverse('company-detail', kwargs={'pk': company_id}) + "?tab=structure")

    return render(request, 'company/tabs/structure.html', {'company': company})


def get_structure_by_parent(request, parent_id=None): 
    company_id = request.GET.get('company_id')
    user = request.user
    user_profile = user.userprofile

    if parent_id:
        # Fetch child nodes for the given parent ID
        print(f"Fetching children for parent ID: {parent_id}")
        if user_profile.role == 5 and user_profile.company.company_type == 'customer':
            # Restrict child nodes to those linked to the proposer
            linked_structures = UserProfileDepartments.objects.filter(
                user_ref=user_profile
            ).values_list('structure_id', flat=True)

            children = CompanyStructure.objects.filter(
                parent=parent_id, id__in=linked_structures
            ).values('id', 'value')
        elif user_profile.company.company_type == 'vendor':
            # Restrict child nodes to those linked to the vendor's company
            linked_structures = CompanyPartner.objects.filter(
                company=user_profile.company
            ).values_list('structure', flat=True)

            children = CompanyStructure.objects.filter(
                parent=parent_id, id__in=linked_structures
            ).values('id', 'value')
        else:
            # Default behavior: Fetch all child nodes
            children = CompanyStructure.objects.filter(parent=parent_id).values('id', 'value')
    else:
        # Fetch top-level nodes for the company
        print(f"Fetching top-level nodes for company ID: {company_id}")
        if user_profile.role == 5 and user_profile.company.company_type == 'customer':
            # Restrict top-level nodes to those linked to the proposer
            linked_structures = UserProfileDepartments.objects.filter(
                user_ref=user_profile
            ).values_list('structure_id', flat=True)

            top_level_nodes = set(
                structure.split('.')[0] + '.' + structure.split('.')[1]
                for structure in linked_structures if '.' in structure
            )

            children = CompanyStructure.objects.filter(id__in=top_level_nodes).values('id', 'value')
        elif user_profile.company.company_type == 'vendor':
            # It is crucial now: if no company_id is given, we cannot filter the nodes.
            if not company_id:
                return JsonResponse({"error": "company_id not provided for vendor user."}, status=400)

            # Validate and retrieve the customer company.
            # (Assuming customer companies have a company_type = 'customer')
            selected_company = get_object_or_404(Company, pk=company_id, company_type='customer')

            # Now filter CompanyPartner records to only those linked to the selected customer.
            # Note: Adjust the field lookup if your CompanyPartner model relates to the customer as 'customer'
            linked_structures = CompanyPartner.objects.filter(
                company=user_profile.company,   # The vendor's own company
                customer=selected_company       # Only the structures of the selected customer
            ).values_list('structure', flat=True)

            if parent_id:
                children = CompanyStructure.objects.filter(
                    parent=parent_id,
                    id__in=linked_structures
                ).values('id', 'value')
            else:
                # Compute top-level nodes by processing the linked structures.
                top_level_nodes = set(
                    structure.split('.')[0] + '.' + structure.split('.')[1]
                    for structure in linked_structures if '.' in structure
                )
                children = CompanyStructure.objects.filter(
                    id__in=top_level_nodes
                ).values('id', 'value')
        else:
            # Default behavior: Fetch all top-level nodes for the company
            company = get_object_or_404(Company, pk=company_id)
            children = CompanyStructure.objects.filter(parent=company.id).values('id', 'value')

    return JsonResponse(list(children), safe=False)

@csrf_exempt
@require_POST
def add_node(request, parent_id=None):
    data = json.loads(request.body)
    print("Received data:", data)  # Debugging

    if parent_id is None:
        # Handle top-level node creation
        company_id = request.GET.get('company_id')  # Get company ID from query params
        company = get_object_or_404(Company, pk=company_id)
        print(f"Top-level node. Company ID: {company_id}, Company: {company.name}")

        last_top_level_node = CompanyStructure.objects.filter(company=company, parent=company_id).order_by(
            '-id').first()
        if last_top_level_node:
            last_id_parts = last_top_level_node.id.split('.')
            new_last_part = int(last_id_parts[-1]) + 1
            new_id = f"{company.id}.{new_last_part}"  # Using company ID for top-level nodes
            print(f"New top-level node ID: {new_id}")
        else:
            new_id = f"{company.id}.0"  # First top-level node
            print(f"Creating first top-level node with ID: {new_id}")

        parent = str(company.id)  # For top-level nodes, parent will be the company ID

    else:
        # Handle child node creation
        print(f"Child node creation. Parent ID: {parent_id}")
        parent = get_object_or_404(CompanyStructure, pk=parent_id)
        company = parent.company  # This correctly handles the company by its ID
        print(f"Parent found: {parent.id}, Company: {company.id}, {company.name}")

        # Get all child nodes and determine the next available suffix
        child_nodes = CompanyStructure.objects.filter(parent=parent.id).values_list('id', flat=True)
        if child_nodes.exists():
            suffixes = [int(child_id.split('.')[-1]) for child_id in child_nodes]
            new_last_part = max(suffixes) + 1
            new_id = f"{parent.id}.{new_last_part}"
        else:
            new_id = f"{parent.id}.0"
        print(f"New child node ID: {new_id}")

        parent = str(parent.id)  # For child nodes, parent will be the ID of the parent structure

    # Create the new node with the correct parent reference
    new_node = CompanyStructure.objects.create(
        id=new_id,
        parent=parent,  # Either company ID (string) for top-level nodes or structure ID for child nodes
        value=data['name'],
        company=company  # Using the company object (ID is implicit)
    )

    print(f"Node created: {new_node}, Parent: {new_node.parent}")  # Debugging

    return JsonResponse({'success': True, 'node_id': new_node.id})


def link_supplier(request, company_id):
    if request.method == 'POST':
        try:
            # Data from the form
            selected_supplier_id = request.POST.get('selected_supplier')
            selected_master_vendor_id = request.POST.get('selected_master_vendor')
            # Convert the string 'null' to actual None
            selected_master_vendor_id = None if selected_master_vendor_id == 'null' else selected_master_vendor_id
            selected_nodes = json.loads(request.POST.get('selected_nodes', '[]'))

            # Validate required fields
            if not selected_supplier_id or not selected_nodes:
                return JsonResponse({'error': 'Missing required fields'}, status=400)

            # Turning data into real objects
            company = get_object_or_404(Company, pk=company_id)
            selected_supplier = get_object_or_404(Company, pk=selected_supplier_id)

            # Begin an atomic transaction so that deletion and creation occur together
            with transaction.atomic():
                # Delete existing supplier connection rows for this customer and supplier.
                CompanyPartner.objects.filter(customer=company, company=selected_supplier).delete()

                # If a master vendor is provided, delete its existing connection rows as well.
                if selected_master_vendor_id:
                    selected_master_vendor = get_object_or_404(Company, pk=selected_master_vendor_id)
                    CompanyPartner.objects.filter(customer=company, company=selected_master_vendor).delete()

                # For each node in the new selection, create a new connection.
                for node in selected_nodes:
                    group_id = uuid.uuid4()
                    # Create the connection for the supplier
                    CompanyPartner.objects.create(
                        customer=company,
                        company=selected_supplier,
                        structure=node,
                        group_id=str(group_id),
                        is_vendor=False,
                        no_vendor=(not selected_master_vendor_id),
                    )

                    # If a master vendor was selected, create its connection as well.
                    if selected_master_vendor_id:
                        CompanyPartner.objects.create(
                            customer=company,
                            company=selected_master_vendor,
                            structure=node,
                            group_id=str(uuid.uuid4()),
                            is_vendor=True,
                            no_vendor=False,
                        )

            return JsonResponse({'message': 'CompanyPartner(s) updated successfully'}, status=201)

        except Exception as e:
            print(f"Error creating CompanyPartner: {e}")
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid request method'}, status=405)

# Fetching after selecting
def company_structure(request, company_id):
    company = get_object_or_404(Company, id=company_id)
    structure = company.structure if company.structure else ["Division", "Location", "Plant", "Department"]
    return JsonResponse({'structure': structure})


def select_company_view(request):
    companies = Company.objects.filter(company_type='customer')
    selected_company = None
    structure = []

    if request.method == 'POST':
        company_id = request.POST.get('company')
        if company_id:
            selected_company = Company.objects.get(id=company_id)
            structure = selected_company.structure if selected_company.structure else ["Division", "Location", "Plant",
                                                                                       "Department"]

    return render(request, 'jobad/select_company.html', {
        'companies': companies,
        'selected_company': selected_company,
        'structure': structure
    })


def get_structure_connections(request):
    supplier_name = request.GET.get('supplier')
    company_id = request.GET.get('company_id')
    
    # Validate the parameters
    if not supplier_name or not company_id:
        return JsonResponse({'error': 'Invalid parameters'}, status=400)

    # Fetch the connections for the given supplier and company
    try:
        connections = CompanyPartner.objects.filter(
            company__name=supplier_name,
            customer_id=company_id
        )
        structures = [connection.structure for connection in connections]

        return JsonResponse({'structures': structures})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
    
def get_supplier_connections(request, company_id):
    # Get all CompanyPartner instances for the given customer.
    partners = CompanyPartner.objects.filter(customer_id=company_id)
    
    # Collect all unique structure IDs from all partners.
    all_structure_ids = set()
    for partner in partners:
        # Assume supplier.structure is a comma-separated string of IDs.
        for s_id in partner.structure.split(','):
            all_structure_ids.add(s_id)
    
    # Fetch all CompanyStructure objects for these IDs in one query.
    structures = CompanyStructure.objects.filter(id__in=all_structure_ids)
    # Create a dictionary mapping ID (as string) to CompanyStructure instance.
    structure_dict = {str(s.id): s for s in structures}
    
    linked_suppliers = []
    for partner in partners:
        node_chains = []
        # For each structure ID in this partner.
        for s_id in partner.structure.split(','):
            nodes = []
            # Get the CompanyStructure instance from the dictionary.
            current_node = structure_dict.get(s_id)
            # Traverse up the tree, building the chain.
            while current_node:
                nodes.insert(0, current_node.value)  # Insert at beginning to have top-to-bottom order.
                # If the parent exists, look it up in our dictionary.
                if current_node.parent_id:
                    current_node = structure_dict.get(str(current_node.parent_id))
                else:
                    current_node = None
            node_chains.append(nodes)
        
        linked_suppliers.append({
            'name': partner.company.name,
            'node_chains': node_chains
        })
    
    context = {
        'linked_suppliers': linked_suppliers,
    }
    return render(request, 'supplier.html', context)

def company_document_download(request, doc_id):
    document = get_object_or_404(CompanyDocument, id=doc_id)
    return FileResponse(document.document.open('rb'),
                        as_attachment=True,
                        filename=document.document.name)

def company_document_delete(request, doc_id):
    doc = get_object_or_404(CompanyDocument, pk=doc_id)
    company_id = doc.company_id
    doc.delete()

    # If we're coming from the overview, bounce back there:
    if request.GET.get('next') == 'documents':
        params = {'open': company_id}
        if request.GET.get('q'):
            params['q'] = request.GET['q']
        url = reverse('documents-overview') + '?' + urlencode(params)
        return redirect(url)

    # Otherwise fall back to the old company-detail tab:
    return redirect(f"{reverse('company-detail', kwargs={'pk': company_id})}?tab=documents")

def documents_overview(request):
    
    q = request.GET.get('q', '').strip()
    user_profile = request.user.userprofile
    user_company = user_profile.company
    lgi_id = settings.LGI_COMPANY_ID
    is_admin = (
        user_profile.role in [1, 5]  # Admin or Proposer
        and (user_company.id == lgi_id or user_company.company_type == 'global')
    )

    if request.method == 'POST' and is_admin:
        form = DocumentForm(request.POST, request.FILES)
        if form.is_valid():
            doc = form.save(commit=False)
            doc.company = Company.objects.get(pk=request.POST['company_id'])
            doc.uploaded_by = request.user
            
            # Handle structure assignment if provided
            if 'structure_id' in request.POST and request.POST['structure_id']:
                try:
                    structure = CompanyStructure.objects.get(id=request.POST['structure_id'])
                    doc.structure = structure
                except CompanyStructure.DoesNotExist:
                    pass
                
            doc.save()
            messages.success(request, _('Document uploaded successfully'))

            # documents_url = request.build_absolute_uri()
            # recipients = UserProfile.objects.filter(
            #     company=doc.company,
            #     user__is_active=True
            # ).select_related('user')

            # subject = _('Neues Dokument für %(name)s') % {'name': doc.company.name}

            # for profile in recipients:
            #     context = {
            #         'recipient_name': profile.user.get_full_name(),
            #         'company_name':   doc.company.name,
            #         'company_url':    documents_url,
            #     }
            #     body = render_to_string("emails/company_document_uploaded.html", context)
            #     send_async_email.delay(profile.user.email, subject, body)
        else:
            messages.error(request, _('Error uploading document'))
        
        # re-open the same modal
        redirect_url = reverse('documents-overview')
        
        # Determine which modal to reopen
        if 'structure_id' in request.POST and request.POST['structure_id']:
            open_param = f"{request.POST['company_id']}_{request.POST['structure_id']}"
        else:
            open_param = request.POST['company_id']
            
        params = {'open': open_param, 'q': q}
        return redirect(f"{redirect_url}?{urlencode(params)}")
    
    # Get companies to display based on user role
    if is_admin:
        # LGI admin sees only their partners; global admin sees all vendors+suppliers
        if user_company.id == lgi_id:
            partner_companies = CompanyPartner.objects.filter(
                customer_id=lgi_id,
                company__company_type__in=['vendor', 'supplier']
            )
            if q:
                partner_companies = partner_companies.filter(company__name__icontains=q)
            
            # Get all partner company IDs
            partner_ids = list(partner_companies.values_list('company_id', flat=True))
        else:
            # global admin
            partner_companies = Company.objects.filter(
                company_type__in=['vendor', 'supplier']
            )
            if q:
                partner_companies = partner_companies.filter(name__icontains=q)
                
            partner_ids = list(partner_companies.values_list('id', flat=True))
    else:
        # Regular users only see their own company
        partner_ids = [user_company.pk]
    
    # Always include LGI
    if lgi_id not in partner_ids:
        partner_ids.append(lgi_id)
    
    # Get all companies that should be displayed
    companies = Company.objects.filter(id__in=partner_ids).order_by('name')
    
    # Get documents for these companies
    documents = CompanyDocument.objects.filter(
        company_id__in=partner_ids
    ).select_related('company', 'uploaded_by') \
     .order_by('company__name', '-uploaded_on')

    if q and not is_admin:
        # For non-admins, filter documents by search term
        documents = documents.filter(
            Q(document__icontains=q) |
            Q(company__name__icontains=q)
        )

    # For LGI structures, get documents for each structure
    lgi_structures = CompanyStructure.objects.filter(
        company_id=lgi_id,
        id__regex=rf"^{lgi_id}\.\d+$"
    ).order_by('value')

    lgi_docs = CompanyDocument.objects.filter(company_id=lgi_id) \
        .select_related('uploaded_by')
    if q:
        lgi_docs = lgi_docs.filter(
            Q(structure__value__icontains=q) |
            Q(document__icontains=q)
        )

    lgi_list = [
        (s, list(lgi_docs.filter(structure=s)))
        for s in lgi_structures
    ]

    # Group documents under each company
    docs_by_company = {}
    
    # First, ensure all companies are in the dictionary even if they have no documents
    for company in companies:
        docs_by_company[company] = []
    
    # Then add documents to their respective companies
    for doc in documents:
        docs_by_company[doc.company].append(doc)

    return render(request, 'company/documents_overview.html', {
        'docs_by_company': docs_by_company,
        'companies': companies,
        'is_admin': is_admin,
        'lgi_structures': lgi_structures,
        'lgi_list': lgi_list,
        'lgi_id': lgi_id,
        'q': q,
        'user_company': user_company,
    })
