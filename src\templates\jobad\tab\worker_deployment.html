{% load application_filters %}
{% load i18n %}
<div 
    x-data="workerDeployment()"
    data-terminate-url="{% url 'terminate-deployment' 0 %}"
    class="container mx-auto mt-4 px-4"
>
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-700">
            {{ workers|length }} 
            {% if workers|length == 1 %}
                {% trans "Worker" %}
            {% else %}
                {% trans "Workers" %}
            {% endif %}
        </h2>
        <div class="relative">
            <input type="text" x-model="search" placeholder="{% trans 'Search...' %}"
                   class="w-64 border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring focus:border-accent-500" />
        </div>
    </div>

    <!-- Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-x-auto">
        <table class="min-w-full bg-white rounded-lg">
            <thead>
                <tr class="bg-gray-200">
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700"></th>
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700">{% trans "Full Name" %}</th>
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700">{% trans "VS" %}</th>
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700">{% trans "Company" %}</th>
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700">{% trans "Start Date / End Date" %}</th>
                    <th class="py-4 px-6 text-left text-sm font-semibold text-gray-700">{% trans "Status" %}</th>
                    <th class="py-4 px-6"></th>
                </tr>
            </thead>
            <tbody>
                {% for worker in workers %}
                <tr class="bg-white hover:bg-gray-50 transition-all duration-200" x-show="search === '' || ('{{ worker.candidate.firstname }} {{ worker.candidate.lastname }}'.toLowerCase()).includes(search.toLowerCase())">
                    <td class="py-5 pr-4 pl-6 border-b">
                        <a href="{% url 'candidate-profile' worker.candidate_id %}" class="text-accent-500">
                            <i class="fas fa-user"></i>
                        </a>
                        {% with cv_docs=worker.candidate.candidatedocument_set|filter_by:"document_type=CV" %}
                            {% if cv_docs %}
                                <a href="{% url 'document_download' 'candidate' cv_docs.0.id %}" class="text-blue-500 ml-2" title="{% trans 'Download CV' %}">
                                    <i class="fas fa-file-download"></i>
                                </a>
                            {% endif %}
                        {% endwith %}
                    </td>
                    <td class="py-3 px-4 border-b text-gray-900">
                        {{ worker.candidate.firstname }} {{ worker.candidate.lastname }}
                    </td>
                    <td class="py-3 px-4 border-b text-gray-900">
                        {{ worker.candidate.hourly_rate|default_if_none:"-" }} €
                    </td>
                    <td class="py-3 px-4 border-b text-gray-900">
                        {{ worker.candidate.company.name }}
                    </td>
                    <td class="py-3 px-4 border-b text-gray-900">
                        {{ worker.start_date|date:"d.m.Y" }} / {{ worker.end_date|date:"d.m.Y" }}
                    </td>
                    <td class="py-3 px-4 border-b text-gray-900">
                        {{ worker.stage|format_stage }}
                    </td>
                    <td class="py-3 px-4 border-b justify-start space-x-4">
                        <!-- Icons section -->
                        <a href="#" title="{% trans 'History' %}" class="text-gray-500 hover:text-accent-500">
                            <i class="fas fa-history"></i>
                        </a>
                        <a href="#" title="{% trans 'Documents' %}" class="text-gray-500 hover:text-accent-500">
                            <i class="fas fa-file-alt"></i>
                        </a>
                        <a href="#" title="{% trans 'Settings' %}" class="text-gray-500 hover:text-accent-500">
                            <i class="fas fa-cog"></i>
                        </a>
                        <!-- When clicking Edit, we store the worker's ID -->
                        <a href="#"
                           title="{% trans 'Edit' %}"
                           @click.prevent="openEditModal = true; selectedWorker = {{ worker.id }}"
                           class="text-gray-500 hover:text-accent-500"
                        >
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
                {% if not workers %}
                <tr>
                    <td colspan="6" class="py-4 px-6 text-center text-sm text-gray-500">
                        {% trans "No workers found for this job ad" %}
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Edit Worker Modal -->
    <div
        x-show="openEditModal"
        class="fixed z-10 inset-0 bg-black bg-opacity-50 flex justify-center items-center"
        x-cloak
    >
        <div class="bg-white p-6 rounded-lg shadow-lg w-96">
            <h2 class="text-xl font-bold mb-4">{% trans "Edit Worker" %}</h2>
            <p class="mb-4">{% trans "What would you like to do?" %}</p>

            <div class="mb-4">
                <select x-model="selectedAction" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="extension">{% trans "Request extension" %}</option>
                    <option value="pause">{% trans "Pause deployment" %}</option>
                    <option value="discontinue">{% trans "Discontinue deployment and replace worker" %}</option>
                    <option value="terminate">{% trans "Terminate deployment" %}</option>
                    <option value="internal">{% trans "Request internal employment" %}</option>
                </select>
            </div>

            <div class="flex justify-end mt-6">
                <button
                    @click="openEditModal = false"
                    class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded mr-2"
                >
                    {% trans "Cancel" %}
                </button>
                <button
                    @click.prevent="handleEditConfirm"
                    class="bg-accent-500 hover:bg-accent-600 text-white font-bold py-2 px-4 rounded"
                >
                    {% trans "Confirm" %}
                </button>
            </div>
        </div>
    </div>

    <!-- Terminate Deployment Confirmation Modal -->
    <div x-show="openTerminateModal" class="fixed z-20 inset-0 bg-black bg-opacity-50 flex justify-center items-center" x-cloak>
        <div class="bg-white p-6 rounded-lg shadow-lg w-96">
            <h2 class="text-xl font-bold mb-4">{% trans "Terminate Worker" %}</h2>
            <p class="mb-4">{% trans "Are you sure you want to discontinue this worker?" %}</p>
            
            <div class="mb-4">
                <label class="block text-sm font-semibold text-gray-700">
                    {% trans "Select Termination Date" %}
                </label>
                <input type="date" x-model="terminationDate" class="w-full p-2 border border-gray-300 rounded-lg">
            </div>
            <!-- New Rejection Reason Dropdown -->
            <div class="mb-4">
                <label class="block text-sm font-semibold text-gray-700">
                    {% trans "Select Termination Reason" %}
                </label>
                <select x-model="selectedWorkerRejectionReason" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="">{% trans "Select a reason" %}</option>
                    <template x-for="reason in workerRejectionReasons" :key="reason.id">
                        <option :value="reason.id" x-text="reason.text"></option>
                    </template>
                </select>
            </div>
            <div class="flex justify-end mt-6 space-x-2">
                <button @click="openTerminateModal = false" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                    {% trans "Cancel" %}
                </button>
                <button @click.prevent="terminateWorker" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">
                    {% trans "Confirm" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function workerDeployment() {
      return {
        openEditModal: false,
        search: '',
        selectedWorker: null,
        openTerminateModal: false,
        selectedAction: '',
        terminationDate: '',
        terminateUrl: '',
        workerRejectionReasons: [],
        selectedWorkerRejectionReason: '',

        init() {
          // Save the URL from the data attribute
          this.terminateUrl = this.$el.dataset.terminateUrl;
          // Fetch the worker rejection reasons using settings id 2
          this.fetchWorkerRejectionReasons();
        },

        fetchWorkerRejectionReasons() {
          fetch(`/company/{{ jobad.company.id }}/rejection-reasons/?settings_id=2`, {
            method: 'GET',
            headers: {
              'X-CSRFToken': '{{ csrf_token }}',
              'Content-Type': 'application/json'
            }
          })
          .then(response => response.json())
          .then(data => {
            this.workerRejectionReasons = data.reasons;
          })
          .catch(error => {
            console.error('Error fetching worker rejection reasons:', error);
          });
        },

        handleEditConfirm() {
          if (this.selectedAction === 'terminate') {
            this.openTerminateModal = true;
          } else {
            alert('Action not implemented in this demo');
            this.openEditModal = false;
          }
        },

        terminateWorker() {
          // Use the saved URL and replace the placeholder with the worker id.
          let url = this.terminateUrl.replace('0', this.selectedWorker);
          let payload = {
            end_date: this.terminationDate,
            rejection_reason_id: this.selectedWorkerRejectionReason
          };
          fetch(url, {
            method: 'POST',
            headers: {
              'X-CSRFToken': '{{ csrf_token }}',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload)
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              location.reload();
            } else {
              alert('Error terminating worker.');
            }
          })
          .catch(error => {
            console.error('Error terminating worker:', error);
          });
        }
      }
    }
</script>
