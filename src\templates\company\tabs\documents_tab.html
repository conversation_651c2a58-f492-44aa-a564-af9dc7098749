{% load static %}
{% load i18n %}
{% load custom_filters %}
<style>
    .filename-cell {
        direction: rtl;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 300px;
    }    
</style>
<div x-data="{ openModal: false, dropzoneActive: false }" class="p-8 bg-white">
    <div class="flex justify-end items-center mb-6">
        {% if request.user.userprofile.role == 1 %}
            <button @click="openModal = true" class="bg-accent-500 hover:bg-secondary-500 text-white font-bold py-2 px-4 rounded">
                {% trans "Upload Document" %}
            </button>
        {% endif %}
    </div>
    <!-- modal -->
    <div x-show="openModal" @click.away="openModal = false" class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
        <div class="bg-white p-6 rounded-lg" @click.stop>
            <h3 class="text-lg font-semibold mb-4">{% trans "Upload Document" %}</h3>
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="file" name="document" id="document" class="border rounded px-2 py-1 mb-4" required>
                <input type="date" name="expiration_date" id="expiration_date" class="border rounded px-2 py-1 mb-4" required>
                <div class="flex justify-end">
                    <button type="submit" class="bg-accent-500 hover:bg-secondary-500 text-white font-bold py-2 px-4 rounded mr-2">{% trans "Save Document" %}</button>
                    <button type="button" @click="openModal = false" class="bg-gray-500 text-white font-bold py-2 px-4 rounded">{% trans "Cancel" %}</button>
                </div>
            </form>
        </div>
    </div>
    <!-- drag & drop -->
    <div
        x-show="dropzoneActive"
        @dragover.prevent="dropzoneActive = true"
        @dragleave.prevent="dropzoneActive = false"
        @drop.prevent="dropzoneActive = false; handleDrop($event)"
        class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-50">
        <div class="bg-white p-6 rounded-lg text-center">
            <p>{% trans "Drop your files here" %}</p>
        </div>
    </div>
    <div class="relative border-gray-300 rounded-lg p-4" @dragover.prevent="dropzoneActive = true" @drop.prevent="handleDrop($event)">
        <table class="min-w-full divide-y divide-gray-200 text-sm">
            <thead class="bg-accent-500 text-white font-bold">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "File Name" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Uploaded By" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Uploaded On" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider">{% trans "Expiration Date" %}</th>
                    <th scope="col" class="px-6 py-3 text-left font-medium uppercase tracking-wider"></th>
                </tr>
            </thead>
            <tbody class="bg-white">
                {% for document in documents %}
                    <tr class="{% cycle 'bg-white' 'bg-gray-100' %}">
                        <td class="px-6 py-4 whitespace-nowrap filename-cell">{{ document.document.name|after_hash:45 }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ document.uploaded_by }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ document.uploaded_on }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ document.expiration_date }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <!-- Download link -->
                            <a href="{% url 'document_download' 'company' document.id %}" class="text-blue-500 mr-2" title="{% trans 'Download' %}">
                                <i class="fas fa-download fa-lg"></i>
                            </a>
                            {% if request.user.userprofile.role == 1 %}
                                <a href="{% url 'company-document-delete' document.id %}" class="text-red-500" title="{% trans 'Delete' %}">
                                    <i class="fas fa-trash fa-lg"></i>
                                </a>
                            {% endif %}
                        </td>                        
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="4" class="text-center py-10">
                            <div class="flex flex-col items-center justify-center">
                                <img src="{% static 'images/documentsic1.png' %}" alt="No documents" class="mt-4 h-16 w-16">
                                <p class="mt-2 text-gray-600">{% trans "No documents available." %}</p>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
                <tr @dragover.prevent="dropzoneActive = true" @drop.prevent="handleDrop($event)" class="border-dashed border-2 border-gray-300">
                    <td colspan="4" class="text-center py-4 text-gray-500">
                        {% trans "Drag and drop files here" %}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
    function handleDrop(event) {
        event.preventDefault();
        let files = event.dataTransfer.files;
        if (files.length) {
            let formData = new FormData();
            formData.append('document', files[0]);
            let expiration_date = prompt("Please enter the expiration date (YYYY-MM-DD):");
            if (expiration_date) {
                formData.append('expiration_date', expiration_date);

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                }).then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Error uploading file.');
                    }
                }).catch(error => {
                    console.error('Error:', error);
                });
            }
        }
    }

    document.addEventListener('dragover', function(event) {
        event.preventDefault();
    });

    document.addEventListener('drop', function(event) {
        //prevent double handling
        event.preventDefault();
    });
</script>
