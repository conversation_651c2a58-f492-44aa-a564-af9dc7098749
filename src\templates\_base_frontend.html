{% load compress %}
{% load static %}
{% load i18n %}
<script>
function showModal() {
    document.getElementById('deleteModal').style.display = 'flex';
}

function closeModal() {
    document.getElementById('deleteModal').style.display = 'none';
}
</script>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "Dashboard" %}{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{% static 'images/faviconn.png' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{% static 'flags/sprite.css' %}">
    <link rel="stylesheet" href="{% static 'flags/sprite-hq.css' %}">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    {% compress css %}
    <link rel="stylesheet" href="{% static 'src/output.css' %}">
    {% endcompress %}
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        .dropdown:hover .dropdown-menu {
            display: block;
        }
        .nav-item span {
            font-size: 0.75rem;
        }
        .nav-item a {
            position: relative;
        }
        .nav-item a::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -5px;
            width: 0;
            height: 2px;
            background-color: #ffd200;
            transition: width 0.3s ease-in-out;
        } 
        .nav-item a:hover::after {
            width: 100%;
        }       
    </style>
</head>
<body class="font-sans flex flex-col min-h-screen">
    <header class="bg-cover w-full h-16 flex items-center justify-center relative" style="background-image: url({% static 'images/background/banner.png' %});">
        <div class="container mx-auto flex items-center justify-between h-full px-4" x-data="{ open: false }">
            <!-- Logo Section for Mobile Dropdown -->
            <a href="#" class="flex items-center text-white md:hidden" @click.prevent="open = !open">
                <img src="{% static 'images/logo.png' %}" alt="Logo" class="h-8 cursor-pointer">
            </a>
            <div x-show="open" @click.outside="open = false" class="absolute top-14 left-0 w-full bg-gray-800 text-white z-50 md:hidden" x-cloak>
                <ul class="flex flex-col p-3 space-y-1 text-sm">
                    <li><a href="/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Dashboard" %}</a></li>
                    <li><a href="/jobads/?tab=active" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Job ads" %}</a></li>
                    <li><a href="/candidates/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Candidates" %}</a></li>
                    <li><a href="/companies/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Companies" %}</a></li>
                    <li><a href="/suppliers/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Suppliers" %}</a></li>
                    <li><a href="/master-vendors/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Master Vendors" %}</a></li>
                    {% if show_documents_tab or request.user.userprofile.company_id == 1 %}
                        <li>
                            <a href="/documents/"
                                class="hover:text-secondary-500 transition-colors"
                                @click="open = false">
                                {% trans "Documents" %}
                            </a>
                        </li>
                    {% endif %}
                    <li><a href="/profile/" class="hover:text-secondary-500 transition-colors" @click="open = false">{% trans "Users" %}</a></li>
                </ul>
            </div>
            
            <!-- Desktop Logo Section -->
            <a href="/" class="hidden md:flex items-center text-white ml-8">
                <img src="{% static 'images/logo.png' %}" alt="{% trans 'Logo' %}" class="h-8 cursor-pointer">
            </a>
            <nav class="hidden md:flex items-center space-x-4">
                <ul class="flex items-center space-x-4 text-white text-lg font-bold">
                    {% with request.path as current_path %}
                        <li class="nav-item">
                            <a href="/" class="hover:text-secondary-500 transition-colors duration-300 {% if current_path == '/' %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Dashboard" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/jobads/?tab=active" class="hover:text-secondary-500 transition-colors duration-300 {% if '/jobads/' in current_path %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Job ads" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/candidates/" class="hover:text-secondary-500 transition-colors duration-300 {% if '/candidates/' in current_path %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Candidates" %}
                            </a>
                        </li>
                        {% if request.user.userprofile.role == 1 or request.user.userprofile.role == 3 %}
                            <li class="nav-item">
                                <a href="/companies/" class="hover:text-secondary-500 transition-colors duration-300 {% if current_path == '/companies/' or company_type == 'customer' %}text-secondary-500 after:!w-full{% endif %}">
                                    {% trans "Companies" %}
                                </a>
                            </li>
                        {% endif %}
                        <li class="nav-item">
                            <a href="/suppliers/" class="hover:text-secondary-500 transition-colors duration-300 {% if current_path == '/suppliers/' or company_type == 'supplier' %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Suppliers" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/master-vendors/" class="hover:text-secondary-500 transition-colors duration-300 {% if current_path == '/master-vendors/' or company_type == 'vendor' %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Master Vendors" %}
                            </a>
                        </li>
                        {% if show_documents_tab or request.user.userprofile.company_id == 1 %}
                            <li class="nav-item">
                                <a href="/documents/"
                                    class="hover:text-secondary-500 transition-colors duration-300
                                            {% if '/documents/' in current_path %}text-secondary-500 after:!w-full{% endif %}">
                                    {% trans "Documents" %}
                                </a>
                            </li>
                        {% endif %}
                        {% if show_reports_tab %}
                            <li x-data="{ open: false }" class="nav-item relative">
                            <!-- top‐level “Reports” link -->
                            <a href="#"
                                @click.prevent="open = !open"
                                class="hover:text-secondary-500 transition-colors duration-300
                                {% if '/reports/' in current_path %}text-secondary-500 after:!w-full{% endif %}">
                                {% trans "Reports" %}
                                <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>

                            <!-- submenu: hidden by default, shown on hover -->
                            <ul x-show="open" click.outside="open = false" class="absolute left-0 mt-1 w-48 bg-white text-gray-800 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-50">
                                <li>
                                <a href="/reports/"
                                    class="block px-4 py-2 hover:bg-gray-100 text-sm">
                                    <i class="fa-solid fa-globe mr-2 text-gray-500"></i>
                                    Übersicht
                                </a>
                                </li>
                                <li>
                                <a href="/reports/reaction-time/"
                                    class="block px-4 py-2 hover:bg-gray-100 text-sm flex items-center">
                                    <i class="fa-solid fa-clock mr-2 text-gray-500"></i>
                                    Reaktionszeiten
                                </a>
                                </li>
                                <a href="/reports/supplier-evaluation/"
                                    class="block px-4 py-2 hover:bg-gray-100 text-sm flex items-center">
                                    <i class="fa-solid fa-building mr-2 text-gray-500"></i>
                                    Lieferantenbewertung
                                </a>
                                </li>
                            </ul>
                            </li>
                        {% endif %}
                        {% if request.user.userprofile.role == 1 %}
                            <li class="nav-item">
                                <a href="/profile/" class="hover:text-secondary-500 transition-colors duration-300 {% if '/profile/' in request.path %}text-secondary-500 after:!w-full{% endif %}">
                                    {% trans "Users" %}
                                </a>
                            </li>
                        {% endif %}
                    {% endwith %} 
                </ul>
            </nav>
            <!-- Action Icons on the Right -->
            <div class="hidden md:flex items-center space-x-4 text-white text-sm">
                <div class="relative" x-data="{ open: false }">
                    <!-- Button to Show Active Language Flag -->
                    <button @click="open = !open" class="hover:text-secondary-500 transition-colors flex items-center">
                        {% if active_flag %}
                            <img src="{{ active_flag }}" alt="{{ active_name }}" class="h-6 w-8">
                        {% else %}
                            <i class="fas fa-globe text-2xl"></i>
                        {% endif %}
                    </button>                    
                
                    <!-- Dropdown Menu -->
                    <div x-show="open" @click.outside="open = false" x-cloak class="absolute right-0 mt-1 bg-white text-black rounded-md shadow-lg z-10 w-20">
                        <ul class="py-1 px-2">
                            <li>
                                <form method="post" action="{% url 'set_language' %}">
                                    {% csrf_token %}
                                    <input type="hidden" name="next" value="{{ request.path }}">
                                    <button type="submit" name="language" value="en" class="flex items-center px-2 py-1 w-full hover:bg-gray-200 text-xs">
                                        <span class="ml-2">{{ language_flags.en.name }}</span>
                                    </button>
                                </form>
                            </li>
                            <li>
                                <form method="post" action="{% url 'set_language' %}">
                                    {% csrf_token %}
                                    <input type="hidden" name="next" value="{{ request.path }}">
                                    <button type="submit" name="language" value="de" class="flex items-center px-2 py-2 w-full hover:bg-gray-200">
                                        <span class="ml-2">{{ language_flags.de.name }}</span>
                                    </button>
                                </form>
                            </li>
                        </ul>                        
                    </div>
                </div>                                                                                             
                <!-- User Dropdown -->
                <div class="nav-item relative hover:bg-accent-800" x-data="{ open: false }">
                    <a href="#" class="flex flex-col items-center justify-center h-10 px-2 cursor-pointer hover:text-secondary-500" @click="open = !open">
                        <p class="text-xs font-bold">{{ request.user.first_name }} {{ request.user.last_name }}</p>
                        <p class="text-xs">{{ user_role }}</p>
                    </a>
                    <!-- Dropdown menu -->
                    <div class="dropdown-menu absolute w-40 h-20 bg-white rounded-md shadow-lg z-20 flex flex-col justify-center" x-show="open" @click.outside="open = false" x-cloak>
                        <a href="/myprofile/" class="flex items-center px-3 py-2 text-gray-800 hover:bg-gray-100 text-sm">
                            <i class="fas fa-cog mr-2"></i> {% trans "Profile Settings" %}
                        </a>
                        <a href="{% url 'logout' %}" class="flex items-center px-3 py-2 text-gray-800 hover:bg-gray-100 text-sm">
                            <i class="fas fa-sign-out-alt mr-1"></i> {% trans "Log out" %}
                        </a>
                    </div>                                  
                </div>
            </div>
        </div>
    </header>
    <div id="alert-container">
        {% include 'partials/alerts.html' %}
    </div>
    {% block custom_header %}{% endblock %}
    <main class="flex-grow bg-gray-200 lg:p-12 p-4 ">
        <div class="container mx-auto">
            {% block content %}
            {% endblock %}
        </div>
    </main>
    <footer class="bg-gray-200 text-center p-4 mt-auto">
        © 2024 ProServ | PPMS. {% trans "All rights reserved." %}
    </footer>
</body>
</html>
