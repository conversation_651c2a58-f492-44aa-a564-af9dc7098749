{% extends '_base_frontend.html' %}
{% load i18n %}
{% block title %}{% trans "Add Jobad" %}{% endblock %}

{% block content %}
<style>
    .no-picker::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
    </style>    
<div x-data="jobadForm()" class="container mx-auto mt-4 px-4">
    <div class="flex items-center bg-white rounded-lg shadow-lg justify-between mb-4 p-4">
        <a href="/jobads/add" class="back-arrow text-xl cursor-pointer text-black flex align-center hover:text-secondary-500">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h2 class="flex-grow text-2xl font-bold text-center text-black">{% trans "Create Jobad" %}</h2>
        <div class="w-6"></div>
    </div>

    <form method="post" enctype="multipart/form-data" class="bg-white p-6 rounded-lg shadow-lg" @submit.prevent="$el.querySelector('#working_hours').value = JSON.stringify(working_hours); $event.target.submit();">
        {% csrf_token %}

        <!-- First Row: Company, Job Position, Job Location -->
        <div class="grid grid-cols-4 md:grid-cols-4 gap-6 mb-4">
            <!-- Company -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Company" %}</h3>
                <p>{{ structure_name }}</p>
                <input type="hidden" name="company" value="{{ company.id }}">
            </div>
            <div>
                <label class="block font-medium text-gray-700">{% trans "Use saved template" %}</label>
                <select x-model="selectedTpl"
                        @change="applyTemplate()"
                        class="border border-gray-300 rounded-lg p-2 w-full">
                  <option value="">{% trans "— Templates —" %}</option>
                  {% for tpl in templates %}
                    <option value="{{ tpl.id }}">{{ tpl.name }}</option>
                  {% endfor %}
                </select>
            </div>     
            <!-- Job Position -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Position" %}</h3>
                {% if company.id == 2 %}
                    <input
                        type="text"
                        name="position_text"
                        x-model="position_text"
                        class="border rounded w-full p-2"
                        placeholder="{% trans 'Enter position title' %}"
                    >
                {% else %}
                    <select x-model="position" name="position" class="border border-gray-300 rounded-lg w-full p-2">
                    {% for pos in positions %}
                        <option value="{{ pos.id }}"{% if pos.id|stringformat:"s" == form.position.value|stringformat:"s" %} selected{% endif %}>
                        {{ pos.title }}
                        </option>
                    {% endfor %}
                    </select>
                {% endif %}
            </div>   
            <!-- Job Location -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Job Location" %}</h3>
                <select x-model="location" name="location" class="border border-gray-300 rounded-lg w-full p-2">
                    {% for loc in locations %}
                        <option value="{{ loc.id }}" {% if loc.id == form.location.value %}selected{% endif %}>
                            {{ loc.street }} {{ loc.street_number }}, {{ loc.city }}
                        </option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <!-- Second Row: Weekly Working Hours and Time Period -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Weekly Working Hours (Hs)" %}</h3>
                <input type="number" x-model="weekly_working_hours" name="weekly_working_hours" class="border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'Enter working hours' %}">
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Time Period (Monate)" %}</h3>
                <input type="number" x-model="time_period" name="time_period" class="border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'Deployment time in months' %}">
            </div>
        </div>

        <!-- Third Row: Tasks and Requirements -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Tasks" %}</h3>
                <textarea x-model="tasks" name="tasks" class="border border-gray-300 rounded-lg w-full p-2" rows="5" placeholder="{% trans 'List the tasks' %}"></textarea>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Requirements" %}</h3>
                <textarea x-model="requirements" name="requirements" class="border border-gray-300 rounded-lg w-full p-2" rows="5" placeholder="{% trans 'List the requirements' %}"></textarea>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <div>
                <h3 class="text-lg font-semibold text-gray-700">{% trans "Description" %}</h3>
                <textarea x-model="description" name="description" class="border border-gray-300 rounded-lg w-full p-2" rows="5" placeholder="{% trans 'List the description' %}"></textarea>
            </div>
            <div class="md:col-span-1">
                <div class="bg-gray-100 p-4 rounded-lg border border-gray-300">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-user text-accent-500 mr-3"></i>
                        <h3 class="text-lg font-semibold text-gray-700">{% trans "Contact Information" %}</h3>
                    </div>
                    <div class="grid grid-cols-1 gap-4">
                        <input type="text" x-model="contact_name" id="contact_name" name="contact_name" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight" placeholder="{% trans 'Contact Name' %}">
                        <input type="text" x-model="contact_phone" id="contact_phone" name="contact_phone" class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight" placeholder="{% trans 'Contact Phone' %}">
                    </div>
                </div>
            </div>
        </div>

        <!-- Fifth Row: Left Column (Shifts, Start Date) / Right Column (Number, Cost, Group, Creator, Car License) -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
            <!-- Left Column -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">{% trans "Working Hour Shifts" %}</h3>
                <template x-for="(shift, index) in working_hours" :key="index">
                    <div class="flex gap-4 mb-2">
                        <input type="time" x-model="shift.start" class="no-picker border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'Start time (HH:MM)' %}">
                        <input type="time" x-model="shift.end" class="no-picker border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'End time (HH:MM)' %}">
                        <button type="button" @click="removeShift(index)" class="text-red-500 hover:text-red-700">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </template>
                <button type="button" @click="addShift" class="text-accent-500 hover:text-accent-700">
                    <i class="fas fa-plus"></i> {% trans "Add Shift" %}
                </button>
                <div class="mt-8">
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Job Start Date" %}</h3>
                    <input
                        type="date"
                        x-model="start_date"
                        name="start_date"
                        class="border {% if form.start_date.errors %}border-red-500{% else %}border-gray-300{% endif %} rounded-lg w-full p-2"
                        placeholder="{% trans 'Select start date' %}">
                    
                    {% if form.start_date.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.start_date.errors.0 }}</p>
                    {% endif %}
                </div>
                <div class="mb-4 mt-4">
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Upload Job Ad Document" %}</h3>
                    <input type="file" name="jobad_document" id="jobad_document" class="border rounded px-2 py-1 w-full">
                </div>
            </div>

            <!-- Right Column: Nested grid -->
            <div class="grid grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Number of Employees" %}</h3>
                    <input type="number" x-model="total" name="total" class="border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'Enter total positions' %}">
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Cost Center" %}</h3>
                    <input type="text" x-model="cost_department" name="cost_department" class="border border-gray-300 rounded-lg w-full p-2" placeholder="{% trans 'Enter cost department' %}">
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Employee Group" %}</h3>
                    <select x-model="employee_group" name="employee_group" class="border border-gray-300 rounded-lg w-full p-2">
                        <option value="office">{% trans "Office" %}</option>
                        <option value="production">{% trans "Production" %}</option>
                    </select>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">{% trans "Add Creator" %}</h3>
                    <select name="created_by" class="border border-gray-300 rounded-lg w-full p-2">
                        <!-- Pre-select the logged-in user as the default -->
                        <option value="{{ user.userprofile.id }}" selected>{{ user.first_name }} {{ user.last_name }}</option>
                        {% for profile in users %}
                            {% if profile.id != user.userprofile.id %}
                                <option value="{{ profile.id }}">
                                    {{ profile.user.first_name }} {{ profile.user.last_name }}
                                </option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <!-- Car License Toggle -->
                <div class="col-span-2"
                    x-data="carLicenseToggle('{{ company.name|escapejs }}')"
                    x-init="init()"
                >
                    <h3 class="text-lg font-semibold text-gray-700 mb-2">
                        {% trans "Car License?" %}
                    </h3>

                    <label class="inline-flex relative items-center cursor-pointer">
                        <!-- Hidden checkbox that Alpine uses for the boolean value -->
                        <input type="checkbox"
                            class="sr-only peer"
                            :disabled="disabled"
                            x-model="fuhrerschein">
                        
                        <!-- The toggle track -->
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-accent-500
                                    rounded-full peer peer-checked:bg-accent-500 peer-disabled:bg-gray-300
                                    peer-checked:after:translate-x-full peer-checked:after:border-white
                                    after:content-[''] after:absolute after:top-[2px] after:left-[2px]
                                    after:bg-white after:border-gray-300 after:border after:rounded-full
                                    after:h-5 after:w-5 after:transition-all">
                        </div>

                        <!-- Label text: changes from 'No' to 'Yes' -->
                        <span class="ml-3 text-sm font-medium text-gray-700">
                            <span x-text="fuhrerschein ? 'Yes' : 'No'"></span>
                        </span>
                    </label>

                    <!-- Hidden input for your Django form -->
                    <input type="hidden" name="fuhrerschein_selected" :value="fuhrerschein ? 'true' : 'false'">
                </div>
            </div>
        </div>
        <!-- Hidden input for shifts -->
        <input type="hidden" id="working_hours" name="working_hours" :value="JSON.stringify(working_hours)">

        <div class="grid grid-cols-2 items-center gap-2 mb-6">
            <!-- template name field, flex-1 so it fills the row -->
            <input
              type="text"
              name="template_name"
              x-model="template_name"
              placeholder="{% trans 'Save as template…' %}"
              class="flex-1 border border-gray-300 rounded-lg p-2"
            />
          
            <!-- hidden flag -->
            <input
              type="hidden"
              name="create_template"
              :value="template_name ? '1' : ''"
            />
          
            <!-- single submit button whose label toggles -->
            <button
              type="submit"
              class="bg-accent-500 hover:bg-secondary-500 text-white font-bold py-2 px-4 rounded"
            >
              <span  
                x-text="template_name
                  ? '{% trans "Save + Create Template" %}'
                  : '{% trans "Save Jobad" %}'">
              </span>
            </button>
        </div>          
    </form>
</div>

<!-- Script to handle auto‐checking/disabling “Car License?” when LKW + LGI -->
<script>
    function carLicenseToggle(companyName) {
        return {
          fuhrerschein: false,
          disabled: false,
    
          init() {
            // Run your auto-check logic once on load:
            this.updateFuhrerscheinCheckbox();
    
            // Also re-check whenever the user changes the position dropdown
            const positionSelect = document.querySelector('select[name="position"]');
            positionSelect.addEventListener('change', () => {
              this.updateFuhrerscheinCheckbox();
            });
          },
    
          updateFuhrerscheinCheckbox() {
            const positionSelect = document.querySelector('select[name="position"]');
            const selectedPositionText = positionSelect.options[positionSelect.selectedIndex]
              .text
              .toLowerCase();
    
            // Convert the company name to lowercase
            const cName = companyName.toLowerCase();
    
            // If the position includes 'lkw' and the company includes 'lgi', auto-check and disable
            if (selectedPositionText.includes('lkw') && cName.includes('lgi')) {
              this.fuhrerschein = true;
              this.disabled = true;  // disable toggling
            } else {
              this.disabled = false; // allow toggling
            }
          }
        };
    }
    function jobadForm() {
        return {
          // —— initial fields pulled from Django form values:
          template_name: '',
          position_text: '{{ form.position_text.value|default:""|escapejs }}',
          position: '{{ form.position.value|default:""|escapejs }}',
          location: '{{ form.location.value|default:""|escapejs }}',
          time_period: {{ form.time_period.value|default:"null" }},
          total: {{ form.total.value|default:"null" }},
          weekly_working_hours: '{{ form.weekly_working_hours.value|default:""|escapejs }}',
          cost_department: '{{ form.cost_department.value|default:""|escapejs }}',
          employee_group: '{{ form.employee_group.value|default:""|escapejs }}',
          start_date: '{{ form.start_date.value|default:""|escapejs }}',
          contact_name: '{{ default_contact_name|escapejs }}',
          contact_phone: '{{ default_contact_phone|escapejs }}',
          tasks: `{{ form.tasks.value|default:""|escapejs }}`,
          description: `{{ form.description.value|default:""|escapejs }}`,
          requirements: `{{ form.requirements.value|default:""|escapejs }}`,
          working_hours: JSON.parse('{{ form.working_hours.value|default:"[]"|escapejs }}'),
          templates: {{ templates_json|safe }},
          selectedTpl: '',
    
          // —— your existing methods:
          addShift() {
            this.working_hours.push({ start: '', end: '' })
          },
          removeShift(i) {
            this.working_hours.splice(i,1)
          },
    
          // —— copy a template’s fields into Alpine’s state
          applyTemplate() {
            const tpl = this.templates.find(t => t.id == this.selectedTpl);
            if(!tpl) return
            this.position_text = tpl.position
            this.location = tpl.location
            this.time_period = tpl.time_period
            this.total = tpl.total
            this.description = tpl.description
            this.tasks = tpl.tasks
            this.requirements = tpl.requirements
            this.contact_name = tpl.contact_name
            this.contact_phone = tpl.contact_phone
            this.working_hours = tpl.working_hours
            this.weekly_working_hours = tpl.weekly_working_hours
            this.cost_department = tpl.cost_department
            this.employee_group = tpl.employee_group
          }
        }
    }
// Run on initial load and whenever the position dropdown changes.
document.addEventListener('DOMContentLoaded', function() {
    const positionSelect = document.querySelector('select[name="position"]');
});
</script>

{% endblock %}
